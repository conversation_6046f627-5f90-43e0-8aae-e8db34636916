#!/bin/bash

# Docker Setup Verification Script
# Checks if all Docker files are properly configured

echo "🔍 Docker Setup Verification"
echo "============================"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

check_file() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅${NC} $description: $file"
        return 0
    else
        echo -e "${RED}❌${NC} $description: $file (MISSING)"
        return 1
    fi
}

check_dir() {
    local dir=$1
    local description=$2
    
    if [ -d "$dir" ]; then
        echo -e "${GREEN}✅${NC} $description: $dir"
        return 0
    else
        echo -e "${RED}❌${NC} $description: $dir (MISSING)"
        return 1
    fi
}

echo -e "${BLUE}📁 Checking Directory Structure...${NC}"
check_dir "backend/medical-qa" "Medical QA Backend"
check_dir "backend/content-brief" "Content Brief Backend"
check_dir "backend/medical-report" "Medical Report Backend"
check_dir "src" "Frontend Source"

echo ""
echo -e "${BLUE}🐳 Checking Docker Files...${NC}"
check_file "docker-compose.yml" "Docker Compose"
check_file "Dockerfile" "Frontend Dockerfile"
check_file "nginx.conf" "Nginx Configuration"
check_file "backend/medical-qa/Dockerfile" "Medical QA Dockerfile"
check_file "backend/content-brief/Dockerfile" "Content Brief Dockerfile"
check_file "backend/medical-report/Dockerfile" "Medical Report Dockerfile"

echo ""
echo -e "${BLUE}🔧 Checking Environment Files...${NC}"
check_file "backend/medical-qa/.env" "Medical QA Environment"
check_file "backend/content-brief/.env" "Content Brief Environment"
check_file "backend/medical-report/.env" "Medical Report Environment"

echo ""
echo -e "${BLUE}📋 Checking Scripts...${NC}"
check_file "test_docker_deployment.sh" "Deployment Test Script"
check_file "quick_test.sh" "Quick Test Script"

echo ""
echo -e "${BLUE}🚫 Checking .dockerignore Files...${NC}"
check_file ".dockerignore" "Frontend .dockerignore"
check_file "backend/medical-qa/.dockerignore" "Medical QA .dockerignore"
check_file "backend/content-brief/.dockerignore" "Content Brief .dockerignore"
check_file "backend/medical-report/.dockerignore" "Medical Report .dockerignore"

echo ""
echo -e "${BLUE}🔍 Checking Docker Installation...${NC}"
if command -v docker &> /dev/null; then
    echo -e "${GREEN}✅${NC} Docker CLI installed"
    
    if docker info &> /dev/null; then
        echo -e "${GREEN}✅${NC} Docker daemon running"
        echo -e "${GREEN}✅${NC} Ready to build and test!"
    else
        echo -e "${YELLOW}⚠️${NC} Docker daemon not running"
        echo -e "${YELLOW}💡${NC} Start Docker Desktop: open -a Docker"
    fi
else
    echo -e "${RED}❌${NC} Docker not installed"
    echo -e "${YELLOW}💡${NC} Install Docker Desktop from: https://docker.com/products/docker-desktop"
fi

echo ""
echo -e "${BLUE}📊 Port Configuration:${NC}"
echo "   Frontend:        3000"
echo "   Medical QA:      3001"
echo "   Content Brief:   3002"
echo "   Medical Report:  3003"

echo ""
echo -e "${BLUE}🚀 Next Steps:${NC}"
echo "1. Start Docker Desktop: open -a Docker"
echo "2. Run deployment test: ./test_docker_deployment.sh"
echo "3. Access frontend: http://localhost:3000"

echo ""
echo -e "${GREEN}✅ Docker setup verification complete!${NC}"
