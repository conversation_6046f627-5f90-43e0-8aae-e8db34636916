version: '3.8'

services:
  # Medical QA Backend Service
  medical-qa:
    build:
      context: ./backend/medical-qa
      dockerfile: Dockerfile
    container_name: medical-qa-service
    ports:
      - "3001:3001"
    environment:
      - PORT=3001
      - HOST=0.0.0.0
    env_file:
      - ./backend/medical-qa/.env
    volumes:
      - ./backend/medical-qa/uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Content Brief Backend Service
  content-brief:
    build:
      context: ./backend/content-brief
      dockerfile: Dockerfile
    container_name: content-brief-service
    ports:
      - "3002:3002"
    environment:
      - PORT=3002
      - HOST=0.0.0.0
    env_file:
      - ./backend/content-brief/.env
    volumes:
      - ./backend/content-brief/uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Medical Report Backend Service
  medical-report:
    build:
      context: ./backend/medical-report
      dockerfile: Dockerfile
    container_name: medical-report-service
    ports:
      - "3003:3003"
    environment:
      - PORT=3003
      - HOST=0.0.0.0
    env_file:
      - ./backend/medical-report/.env
    volumes:
      - ./backend/medical-report/uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-apps-frontend
    ports:
      - "3000:3000"
    depends_on:
      - medical-qa
      - content-brief
      - medical-report
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

networks:
  default:
    name: ai-apps-network
