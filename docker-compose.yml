version: '3.8'

services:
  medical-qa:
    build:
      context: ./backend/medical-qa
      dockerfile: Dockerfile
    container_name: medical-qa-service
    ports:
      - "3001:3001"
    environment:
      - API_PORT=3001
      - API_HOST=0.0.0.0
    env_file:
      - ./backend/medical-qa/.env
    volumes:
      - ./backend/medical-qa:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai-apps-network

  content-brief:
    build:
      context: ./backend/content-brief
      dockerfile: Dockerfile
    container_name: content-brief-service
    ports:
      - "3002:3002"
    environment:
      - API_PORT=3002
      - API_HOST=0.0.0.0
    env_file:
      - ./backend/content-brief/.env
    volumes:
      - ./backend/content-brief:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai-apps-network

  medical-report:
    build:
      context: ./backend/medical-report
      dockerfile: Dockerfile
    container_name: medical-report-service
    ports:
      - "3003:3003"
    environment:
      - API_PORT=3003
      - API_HOST=0.0.0.0
    env_file:
      - ./backend/medical-report/.env
    volumes:
      - ./backend/medical-report:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai-apps-network

networks:
  ai-apps-network:
    driver: bridge

volumes:
  medical-qa-data:
  content-brief-data:
  medical-report-data:
