#!/bin/bash

# Quick Docker Test Script
# Tests individual services quickly

echo "🔍 Quick Docker Service Test"
echo "============================"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Test function
test_service() {
    local name=$1
    local port=$2
    local endpoint=$3
    
    echo -n "Testing $name on port $port... "
    
    if curl -f -s "http://localhost:$port$endpoint" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

# Test all services
test_service "Medical QA" 3001 "/health"
test_service "Content Brief" 3002 "/health"
test_service "Medical Report" 3003 "/health"
test_service "Frontend" 3000 "/"

echo ""
echo "📊 Container Status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(medical-qa|content-brief|medical-report|frontend|NAMES)"

echo ""
echo "🌐 Access URLs:"
echo "   Frontend: http://localhost:3000"
echo "   Medical QA API: http://localhost:3001/docs"
echo "   Content Brief API: http://localhost:3002/docs"
echo "   Medical Report API: http://localhost:3003/docs"
