
import { useState } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, TrendingUp } from "lucide-react";

const MarketResearch = () => {
  const [file, setFile] = useState<File | null>(null);
  const [industry, setIndustry] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleAnalyze = async () => {
    if (!file || !industry) return;
    
    setIsProcessing(true);
    setTimeout(() => {
      setResult({
        trends: "Market insights appear here",
        competitors: "Competitor analysis appears here",
        stats: "Market statistics appear here"
      });
      setIsProcessing(false);
    }, 3000);
  };

  return (
    <Layout
      title="Market Research (MarketMaven)"
      description="AI-powered market analysis and competitive intelligence platform."
      category="Sales & Marketing"
    >
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload Market Research Data</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Market Research File</Label>
              <div className="mt-2 flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-emerald-400 transition-colors">
                <div className="text-center">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <div className="text-sm text-gray-600">
                    {file ? file.name : "Upload market research file"}
                  </div>
                  <input
                    type="file"
                    accept=".pdf,.csv,.xlsx"
                    onChange={(e) => setFile(e.target.files?.[0] || null)}
                    className="hidden"
                  />
                </div>
              </div>
            </div>
            
            <div>
              <Label htmlFor="industry">Industry Sector</Label>
              <Input
                id="industry"
                value={industry}
                onChange={(e) => setIndustry(e.target.value)}
                placeholder="e.g., Technology, Healthcare, Finance"
                className="mt-2"
              />
            </div>
            
            <Button 
              onClick={handleAnalyze}
              disabled={!file || !industry || isProcessing}
              className="w-full bg-emerald-500 hover:bg-emerald-600"
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Generating market research insights...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4" />
                  <span>Generate Insights</span>
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        {result && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Key Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-4 rounded-lg min-h-[150px] flex items-center justify-center text-gray-500">
                  Market insights appear here
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Competitor Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-4 rounded-lg min-h-[150px] flex items-center justify-center text-gray-500">
                  Competitor analysis appears here
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Market Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-4 rounded-lg min-h-[150px] flex items-center justify-center text-gray-500">
                  Market statistics appear here
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default MarketResearch;
