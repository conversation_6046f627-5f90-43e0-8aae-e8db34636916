import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Upload, MessageSquare, Database, Trash2, AlertCircle, HelpCircle, RefreshCw, ChevronDown, ChevronUp, X } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

const BACKEND_URL = "http://localhost:8000";

interface Document {
  filename: string;
  doc_type: string;
  upload_date: string;
  chunk_count: number;
}

interface ChatMessage {
  question: string;
  answer: string;
  sources: string[];
  queryType: string;
  kbSourcesCount: number;
  webSourcesCount: number;
}

const PharmaKnowledgebase = () => {
  const [file, setFile] = useState<File | null>(null);
  const [question, setQuestion] = useState("");
  const [webSearch, setWebSearch] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [currentAnswer, setCurrentAnswer] = useState<ChatMessage | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [sampleQuestions, setSampleQuestions] = useState<string[]>([]);
  const [isLoadingSamples, setIsLoadingSamples] = useState(false);
  const [isSampleQuestionsExpanded, setIsSampleQuestionsExpanded] = useState(true);
  const [deletingDocuments, setDeletingDocuments] = useState<Set<string>>(new Set());

  // Load documents on component mount
  useEffect(() => {
    loadDocuments();
  }, []);

  // Load sample questions when documents change
  useEffect(() => {
    if (documents.length > 0) {
      loadSampleQuestions();
    } else {
      setSampleQuestions([]);
    }
  }, [documents.length]);

  const loadDocuments = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/documents`);
      if (response.ok) {
        const docs = await response.json();
        setDocuments(docs);
      }
    } catch (error) {
      console.error("Error loading documents:", error);
    }
  };

  const loadSampleQuestions = async () => {
    setIsLoadingSamples(true);
    try {
      const response = await fetch(`${BACKEND_URL}/sample-questions`);
      if (response.ok) {
        const result = await response.json();
        setSampleQuestions(result.questions || []);
      }
    } catch (error) {
      console.error("Error loading sample questions:", error);
    } finally {
      setIsLoadingSamples(false);
    }
  };

  const handleFileUpload = async () => {
    if (!file) return;
    
    setIsUploading(true);
    setError(null);
    setSuccess(null);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("doc_type", "pharmaceutical");

      const response = await fetch(`${BACKEND_URL}/upload-document`, {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        setSuccess(`Successfully uploaded ${file.name}! Added ${result.chunks_added} searchable sections.`);
        setFile(null);
        loadDocuments(); // Reload document list
        
        // Reset file input
        const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        if (fileInput) fileInput.value = '';
      } else {
        const errorData = await response.json();
        setError(errorData.detail || "Upload failed");
      }
    } catch (error) {
      setError("Network error during upload");
    } finally {
      setIsUploading(false);
    }
  };

  const handleQuestion = async () => {
    if (!question.trim()) return;
    
    setIsProcessing(true);
    setError(null);

    try {
      const response = await fetch(`${BACKEND_URL}/search`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: question,
          include_web: webSearch,
          limit: 5,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const newMessage: ChatMessage = {
          question,
          answer: result.final_answer,
          sources: result.sources,
          queryType: result.query_type,
          kbSourcesCount: result.kb_sources_count,
          webSourcesCount: result.web_sources_count,
        };
        
        setCurrentAnswer(newMessage);
        setQuestion("");
      } else {
        const errorData = await response.json();
        setError(errorData.detail || "Search failed");
      }
    } catch (error) {
      setError("Network error during search");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSampleQuestionClick = (sampleQuestion: string) => {
    setQuestion(sampleQuestion);
    // Auto-focus the search input after setting the question
    setTimeout(() => {
      const searchInput = document.querySelector('input[placeholder*="pharmaceutical question"]') as HTMLInputElement;
      if (searchInput) searchInput.focus();
    }, 100);
  };

  const handleDeleteDocument = async (filename: string) => {
    setDeletingDocuments(prev => new Set(prev).add(filename));
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`${BACKEND_URL}/documents/${encodeURIComponent(filename)}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setSuccess(`Document ${filename} deleted successfully`);
        loadDocuments(); // Reload document list
      } else {
        const errorData = await response.json();
        setError(errorData.detail || "Delete failed");
      }
    } catch (error) {
      setError("Network error during delete");
    } finally {
      setDeletingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(filename);
        return newSet;
      });
    }
  };

  const handleClearAllDocuments = async () => {
    if (!confirm("Are you sure you want to clear all documents? This cannot be undone.")) {
      return;
    }

    try {
      const response = await fetch(`${BACKEND_URL}/documents`, {
        method: "DELETE",
      });

      if (response.ok) {
        setSuccess("All documents cleared successfully");
        setDocuments([]);
        setCurrentAnswer(null); // Clear current answer too
        setSampleQuestions([]); // Clear sample questions too
      } else {
        const errorData = await response.json();
        setError(errorData.detail || "Clear failed");
      }
    } catch (error) {
      setError("Network error during clear");
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Helper function to convert source to clickable link
  const formatSourceLink = (source: string) => {
    // Check for different source types and create appropriate URLs
    if (source.startsWith('PMC')) {
      const pmcId = source.match(/PMC\d+/)?.[0];
      if (pmcId) {
        return (
          <a 
            href={`https://www.ncbi.nlm.nih.gov/pmc/articles/${pmcId}/`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            {source}
          </a>
        );
      }
    } else if (source.startsWith('DOI:') || source.includes('doi.org')) {
      const doiMatch = source.match(/(?:DOI:\s*|doi\.org\/)(.+)/i);
      if (doiMatch) {
        return (
          <a 
            href={`https://doi.org/${doiMatch[1]}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            {source}
          </a>
        );
      }
    } else if (source.startsWith('http')) {
      return (
        <a 
          href={source}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-800 underline"
        >
          {source}
        </a>
      );
    }
    
    // Default: return as regular text (including PDFs and local documents)
    return <span className="text-gray-600">{source}</span>;
  };

  const formatResponse = (text: string) => {
    // Helper function to process text and make sources bold
    const processTextWithSources = (textContent: string) => {
      const sourceRegex = /\(Source:\s*[^)]+\)/g;
      const parts = textContent.split(sourceRegex);
      const sources = textContent.match(sourceRegex) || [];
      
      const result = [];
      for (let i = 0; i < parts.length; i++) {
        if (parts[i]) {
          result.push(<span key={`text-${i}`}>{parts[i]}</span>);
        }
        if (sources[i]) {
          const sourceText = sources[i].replace(/^\(Source:\s*/, '').replace(/\)$/, '');
          result.push(
            <strong key={`source-${i}`} className="ml-1 font-bold text-black">
              (Source: {sourceText})
            </strong>
          );
        }
      }
      return result;
    };

    // Split text into paragraphs and process each one
    const paragraphs = text.split('\n\n').filter(p => p.trim());
    
    return (
      <div className="space-y-4">
        {paragraphs.map((paragraph, index) => {
          const trimmedParagraph = paragraph.trim();
          
          // Process bold text within paragraphs
          const parts = trimmedParagraph.split(/(\*\*[^*]+\*\*)/g);
          
          return (
            <div key={index} className="leading-relaxed">
              {parts.map((part, partIndex) => {
                if (part.startsWith('**') && part.endsWith('**')) {
                  // Bold text
                  return (
                    <strong key={partIndex} className="font-semibold text-gray-900">
                      {part.slice(2, -2)}
                    </strong>
                  );
                } else {
                  // Regular text - split by lines and handle numbered lists
                  const lines = part.split('\n').filter(line => line.trim());
                  
                  return lines.map((line, lineIndex) => {
                    const trimmedLine = line.trim();
                    
                    if (trimmedLine.match(/^\d+\.\s/)) {
                      // Numbered list items
                      const listNumber = trimmedLine.match(/^\d+\./)?.[0];
                      const listContent = trimmedLine.replace(/^\d+\.\s*/, '');
                      
                      return (
                        <div key={`${partIndex}-${lineIndex}`} className="mb-2 pl-4">
                          <span className="font-medium text-emerald-700 mr-2">
                            {listNumber}
                          </span>
                          <span>{processTextWithSources(listContent)}</span>
                        </div>
                      );
                    } else if (trimmedLine.startsWith('- ')) {
                      // Bullet points
                      const bulletContent = trimmedLine.substring(2);
                      return (
                        <div key={`${partIndex}-${lineIndex}`} className="mb-1 pl-4 flex">
                          <span className="text-emerald-600 mr-2">•</span>
                          <span>{processTextWithSources(bulletContent)}</span>
                        </div>
                      );
                    } else if (trimmedLine.length > 0) {
                      // Regular text
                      return (
                        <span key={`${partIndex}-${lineIndex}`}>
                          {processTextWithSources(trimmedLine)}
                          {lineIndex < lines.length - 1 && <br />}
                        </span>
                      );
                    }
                    return null;
                  });
                }
              })}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Layout
      title="Pharma Knowledgebase"
      description="Interactive pharmaceutical information system with intelligent Q&A capabilities."
      category="Pharma & Healthcare"
    >
      <div className="space-y-8">
        {error && (
          <Alert variant="destructive" className="relative">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="pr-8">{error}</AlertDescription>
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-red-100"
              onClick={() => setError(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </Alert>
        )}

        {success && (
          <Alert className="relative">
            <AlertDescription className="pr-8">{success}</AlertDescription>
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100"
              onClick={() => setSuccess(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload to Knowledge Base</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Upload File (PDF, DOCX, TXT)</Label>
              <input
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={(e) => setFile(e.target.files?.[0] || null)}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="mt-2 flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-emerald-400 hover:bg-emerald-50 transition-all cursor-pointer group"
              >
                <div className="text-center">
                  <Upload className="h-8 w-8 text-gray-400 group-hover:text-emerald-500 mx-auto mb-2 transition-colors" />
                  <div className="text-sm text-gray-600 group-hover:text-emerald-700 transition-colors">
                    {file ? file.name : "Upload pharmaceutical document"}
                  </div>
                  <div className="text-xs text-gray-500 group-hover:text-emerald-600 mt-1 transition-colors">
                    Click anywhere in this area to select a file
                  </div>
                </div>
              </label>
            </div>
            <Button 
              onClick={handleFileUpload}
              disabled={!file || isUploading}
              className="w-full bg-emerald-500 hover:bg-emerald-600"
            >
              {isUploading ? (
                <div className="flex items-center">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Uploading...
                </div>
              ) : (
                "Add to Knowledge Base"
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Sample Questions Section */}
        {documents.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  className="flex items-center space-x-2 p-0 h-auto hover:bg-transparent text-lg font-semibold"
                  onClick={() => setIsSampleQuestionsExpanded(!isSampleQuestionsExpanded)}
                >
                  <HelpCircle className="h-5 w-5" />
                  <span>Sample Questions</span>
                  {isSampleQuestionsExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadSampleQuestions}
                  disabled={isLoadingSamples}
                >
                  <RefreshCw className={`h-4 w-4 ${isLoadingSamples ? 'animate-spin' : ''}`} />
                </Button>
              </CardTitle>
            </CardHeader>
            {isSampleQuestionsExpanded && (
              <CardContent className="pt-0">
                {isLoadingSamples ? (
                  <div className="text-center py-4 text-gray-600">
                    <div className="animate-spin h-6 w-6 border-2 border-emerald-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                    Generating sample questions...
                  </div>
                ) : sampleQuestions.length > 0 ? (
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600 mb-3">
                      Click on any question below to use it as your search query:
                    </p>
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {sampleQuestions.map((sampleQuestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          className="w-full text-left h-auto p-3 justify-start hover:bg-emerald-50 hover:border-emerald-300 whitespace-normal"
                          onClick={() => handleSampleQuestionClick(sampleQuestion.replace(/^\d+\.\s*/, ''))}
                        >
                          <div className="text-sm leading-relaxed break-words">
                            <span className="font-medium text-emerald-700 mr-2">{index + 1}.</span>
                            {sampleQuestion.replace(/^\d+\.\s*/, '')}
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    No sample questions available. Upload more documents to generate suggestions.
                  </div>
                )}
              </CardContent>
            )}
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <span>Ask Question</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                checked={webSearch}
                onCheckedChange={setWebSearch}
                id="web-search"
              />
              <Label htmlFor="web-search">Enable Web Search</Label>
            </div>
            
            <div className="flex space-x-2">
              <Input
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                placeholder="Ask a pharmaceutical question..."
                className="flex-1"
                onKeyPress={(e) => e.key === 'Enter' && handleQuestion()}
              />
              <Button 
                onClick={handleQuestion}
                disabled={!question || isProcessing}
                className="bg-emerald-500 hover:bg-emerald-600"
              >
                {isProcessing ? (
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                ) : (
                  "Ask"
                )}
              </Button>
            </div>
            
            {isProcessing && (
              <div className="text-center py-6 text-gray-600">
                <div className="animate-spin h-6 w-6 border-2 border-emerald-500 border-t-transparent rounded-full mx-auto mb-3"></div>
                <div className="font-medium">Searching pharma knowledgebase{webSearch ? " and web" : ""}...</div>
                <div className="text-sm text-gray-500 mt-2">
                  🤖 AI response typically takes 30-60 seconds
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {currentAnswer && (
          <Card>
            <CardHeader>
              <CardTitle>Answer</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="font-medium text-gray-900 mb-3 p-3 bg-emerald-50 rounded-lg border-l-4 border-emerald-500">
                  <span className="text-emerald-700 font-semibold">Q:</span> {currentAnswer.question}
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-gray-800 mb-3">
                    {formatResponse(currentAnswer.answer)}
                  </div>
                  {currentAnswer.sources.length > 0 && (
                    <div className="text-xs text-gray-600 border-t pt-3 mt-3">
                      <div className="font-medium mb-2 text-gray-700">
                        📚 Sources:
                      </div>
                      <ul className="list-disc list-inside space-y-1">
                        {currentAnswer.sources.map((source, i) => (
                          <li key={i} className="text-gray-600">
                            {formatSourceLink(source)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>Manage Knowledge Base ({documents.length} documents)</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {documents.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No documents uploaded yet. Upload your first pharmaceutical document above.
                </div>
              ) : (
                documents.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                      <div className="font-medium">{doc.filename}</div>
                      <div className="text-sm text-gray-500">
                        {doc.chunk_count} chunks • Uploaded: {new Date(doc.upload_date).toLocaleDateString()}
                      </div>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleDeleteDocument(doc.filename)}
                      disabled={deletingDocuments.has(doc.filename)}
                    >
                      {deletingDocuments.has(doc.filename) ? (
                        <div className="animate-spin h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                ))
              )}
              {documents.length > 0 && (
                <Button 
                  variant="outline" 
                  className="w-full mt-4"
                  onClick={handleClearAllDocuments}
                >
                  Clear All Documents
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default PharmaKnowledgebase;
