import { useState, useRef, useEffect } from "react";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, MessageSquare, X, Send, Bot, User, FileText, Settings } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Import API functions and types
import {
  uploadDocument,
  loadSampleQuestions,
  askMedicalQuestion,
  clearDocument,
  type ChatMessage,
  type SampleQuestionsResponse
} from "./api";

const MedicalQA = () => {
  const [currentMessage, setCurrentMessage] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [collectionName, setCollectionName] = useState<string>("");
  const [sampleQuestions, setSampleQuestions] = useState<string[]>([]);
  const [responseStyle, setResponseStyle] = useState("moderate");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Load initial sample questions
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      type: 'system',
      content: 'Welcome to Medical QA! I can help answer medical questions based on uploaded documents or general medical knowledge. Please upload a document or ask a question to get started.',
      timestamp: new Date(),
    };

    setMessages([welcomeMessage]);
  }, []);

  const handleFileUpload = async (selectedFile: File) => {
    if (!selectedFile) return;

    setIsUploading(true);
    try {
      const result = await uploadDocument(selectedFile);
      
      if (result.success) {
        setUploadedFile(selectedFile);
        setCollectionName(result.collection_name || "");
        
        toast({
          title: "Document uploaded successfully",
          description: result.message,
        });

        // Load sample questions for the uploaded document
        if (result.collection_name) {
          await loadSampleQuestionsForCollection(result.collection_name);
        }

        // Add system message about successful upload
        const uploadMessage: ChatMessage = {
          id: Date.now().toString(),
          type: 'system',
          content: `Document "${selectedFile.name}" uploaded successfully. You can now ask questions about this document.`,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, uploadMessage]);
      }
    } catch (error) {
      console.error("Upload failed:", error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload document",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const loadSampleQuestionsForCollection = async (collection?: string) => {
    try {
      const result: SampleQuestionsResponse = await loadSampleQuestions(collection);
      setSampleQuestions(result.questions || []);
    } catch (error) {
      console.error("Failed to load sample questions:", error);
    }
  };

  const handleSubmit = async () => {
    if (!currentMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: currentMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setCurrentMessage("");
    setIsLoading(true);

    try {
      const response = await askMedicalQuestion(
        currentMessage,
        responseStyle,
        collectionName || undefined
      );

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.answer,
        timestamp: new Date(),
        urgent_notice: response.urgent_notice,
        source_info: response.source_info,
        disclaimer: response.disclaimer,
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Failed to get response:", error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Failed to get response'}`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
      
      toast({
        title: "Error",
        description: "Failed to get response. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleClearDocument = async () => {
    if (!collectionName) return;

    try {
      await clearDocument(collectionName);
      setUploadedFile(null);
      setCollectionName("");
      setSampleQuestions([]);
      
      toast({
        title: "Document cleared",
        description: "Document has been removed from the system.",
      });

      // Add system message about clearing
      const clearMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'system',
        content: 'Document cleared. You can now upload a new document or ask general medical questions.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, clearMessage]);
    } catch (error) {
      console.error("Failed to clear document:", error);
      toast({
        title: "Error",
        description: "Failed to clear document",
        variant: "destructive",
      });
    }
  };

  const handleSampleQuestionClick = (question: string) => {
    setCurrentMessage(question);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Medical QA Assistant</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Get accurate medical information and answers to your questions. Upload medical documents for context-specific responses.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Panel - Controls */}
          <div className="lg:col-span-1 space-y-6">
            {/* Document Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Document Upload
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="file-upload">Upload Medical Document</Label>
                  <input
                    ref={fileInputRef}
                    type="file"
                    id="file-upload"
                    accept=".pdf,.txt,.doc,.docx"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload(file);
                    }}
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="w-full mt-2"
                    variant="outline"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {isUploading ? "Uploading..." : "Choose File"}
                  </Button>
                </div>

                {uploadedFile && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">
                          {uploadedFile.name}
                        </span>
                      </div>
                      <Button
                        onClick={handleClearDocument}
                        size="sm"
                        variant="ghost"
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Response Style */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Response Style
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Select value={responseStyle} onValueChange={setResponseStyle}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="concise">Concise</SelectItem>
                    <SelectItem value="moderate">Moderate</SelectItem>
                    <SelectItem value="detailed">Detailed</SelectItem>
                  </SelectContent>
                </Select>
              </CardContent>
            </Card>

            {/* Sample Questions */}
            {sampleQuestions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Sample Questions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {sampleQuestions.slice(0, 5).map((question, index) => (
                      <Button
                        key={index}
                        onClick={() => handleSampleQuestionClick(question)}
                        variant="ghost"
                        className="w-full text-left justify-start h-auto p-3 text-sm"
                      >
                        {question}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Chat */}
          <div className="lg:col-span-2">
            <Card className="h-[600px] flex flex-col">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Medical Q&A Chat
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col p-0">
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex gap-3 ${
                        message.type === 'user' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div
                        className={`flex gap-3 max-w-[80%] ${
                          message.type === 'user' ? 'flex-row-reverse' : 'flex-row'
                        }`}
                      >
                        <div className="flex-shrink-0">
                          {message.type === 'user' ? (
                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                              <User className="h-4 w-4 text-white" />
                            </div>
                          ) : (
                            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                              <Bot className="h-4 w-4 text-white" />
                            </div>
                          )}
                        </div>
                        <div
                          className={`rounded-lg p-3 ${
                            message.type === 'user'
                              ? 'bg-blue-500 text-white'
                              : message.type === 'system'
                              ? 'bg-gray-100 text-gray-800'
                              : 'bg-white border border-gray-200 text-gray-800'
                          }`}
                        >
                          <div className="whitespace-pre-wrap">{message.content}</div>
                          
                          {message.urgent_notice && (
                            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-sm">
                              <strong>Urgent Notice:</strong> {message.urgent_notice}
                            </div>
                          )}
                          
                          {message.source_info && (
                            <div className="mt-2 text-xs text-gray-500">
                              Source: {message.source_info}
                            </div>
                          )}
                          
                          {message.disclaimer && (
                            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-800 text-xs">
                              {message.disclaimer}
                            </div>
                          )}
                          
                          <div className="mt-1 text-xs opacity-70">
                            {message.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input */}
                <div className="border-t p-4">
                  <div className="flex gap-2">
                    <Textarea
                      value={currentMessage}
                      onChange={(e) => setCurrentMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Ask a medical question..."
                      className="flex-1 min-h-[60px] resize-none"
                      disabled={isLoading}
                    />
                    <Button
                      onClick={handleSubmit}
                      disabled={isLoading || !currentMessage.trim()}
                      className="self-end"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default MedicalQA;
