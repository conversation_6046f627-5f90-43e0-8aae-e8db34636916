// Base URL for Medical QA API
const API_BASE_URL = "/api/medical-qa";

// Types for API responses
export interface MedicalResponse {
  answer: string;
  source_type: string;
  source_info: string;
  urgent_notice?: string;
  disclaimer: string;
}

export interface DocumentUploadResponse {
  success: boolean;
  message: string;
  collection_name?: string;
  filename?: string;
  document_id?: string;
}

export interface SampleQuestionsResponse {
  questions: string[];
  document_based: boolean;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  urgent_notice?: string;
  source_info?: string;
  disclaimer?: string;
}

// Upload document file
export const uploadDocument = async (file: File): Promise<DocumentUploadResponse> => {
  try {
    const formData = new FormData();
    formData.append("file", file);

    console.log("Uploading document:", {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });

    const response = await fetch(`${API_BASE_URL}/upload-document`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to upload document");
    }

    return await response.json();
  } catch (error) {
    console.error("Document upload error:", error);
    throw error;
  }
};

// Load sample questions
export const loadSampleQuestions = async (collection?: string): Promise<SampleQuestionsResponse> => {
  try {
    const url = collection
      ? `${API_BASE_URL}/sample-questions?collection_name=${collection}`
      : `${API_BASE_URL}/sample-questions`;

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error("Failed to load sample questions");
    }

    return await response.json();
  } catch (error) {
    console.error("Failed to load sample questions:", error);
    throw error;
  }
};

// Ask medical question
export const askMedicalQuestion = async (
  question: string, 
  responseStyle: string, 
  collection?: string
): Promise<MedicalResponse> => {
  try {
    const requestBody: any = {
      question,
      response_style: responseStyle,
    };

    if (collection) {
      requestBody.collection_name = collection;
    }

    console.log("Asking question:", requestBody);

    const response = await fetch(`${API_BASE_URL}/ask`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to get medical response");
    }

    return await response.json();
  } catch (error) {
    console.error("Medical question error:", error);
    throw error;
  }
};

// Clear document from collection
export const clearDocument = async (collectionName: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/clear-document?collection_name=${collectionName}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to clear document");
    }

    return await response.json();
  } catch (error) {
    console.error("Clear document error:", error);
    throw error;
  }
};
