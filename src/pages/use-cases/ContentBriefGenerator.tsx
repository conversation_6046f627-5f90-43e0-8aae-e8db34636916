
import { useState, useRef } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, FileText, Download, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// API Configuration
const API_BASE_URL = "http://37.27.255.120:3002";

const ContentBriefGenerator = () => {
  const [contentNeed, setContentNeed] = useState("");
  const [files, setFiles] = useState<FileList | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileUpload = async (selectedFiles: FileList) => {
    if (!selectedFiles || selectedFiles.length === 0) return;

    setIsProcessing(true);
    const formData = new FormData();

    Array.from(selectedFiles).forEach((file) => {
      formData.append('files', file);
    });

    try {
      const response = await fetch(`${API_BASE_URL}/upload-documents`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setSessionId(data.session_id);
        setUploadedFiles(data.processed_files);
        setFiles(selectedFiles);
        toast({
          title: "Files uploaded successfully",
          description: `${data.document_count} documents processed`,
        });
      } else {
        throw new Error(data.message || "Upload failed");
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGenerate = async () => {
    if (!contentNeed.trim()) {
      toast({
        title: "Query required",
        description: "Please describe your content brief needs",
        variant: "destructive",
      });
      return;
    }

    if (!sessionId) {
      toast({
        title: "Files required",
        description: "Please upload client communication files first",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const response = await fetch(`${API_BASE_URL}/generate-brief`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: contentNeed,
          session_id: sessionId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Generation failed: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setResult(data);
        toast({
          title: "Content brief generated",
          description: "Your strategic content brief is ready",
        });
      } else {
        throw new Error(data.content_brief || "Generation failed");
      }
    } catch (error) {
      console.error('Generation error:', error);
      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const clearFiles = () => {
    setFiles(null);
    setUploadedFiles([]);
    setSessionId(null);
    setResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const downloadBrief = () => {
    if (!result?.content_brief) return;

    const blob = new Blob([result.content_brief], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `content-brief-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Layout
      title="Content Brief Generator"
      description="Generate comprehensive content briefs from client communications and requirements."
      category="Sales & Marketing"
    >
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Content Requirements</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="content-need">Describe Content Need</Label>
              <Textarea
                id="content-need"
                value={contentNeed}
                onChange={(e) => setContentNeed(e.target.value)}
                placeholder="E.g., 'Create a content brief for our new product launch targeting millennials'"
                className="mt-2 min-h-[120px]"
              />
            </div>

            <div>
              <Label>Upload Client Communications</Label>
              <div
                onClick={() => fileInputRef.current?.click()}
                className="mt-2 flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-emerald-400 transition-colors cursor-pointer"
              >
                <div className="text-center">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <div className="text-sm text-gray-600">
                    {files ? `${files.length} file(s) selected` : "Upload emails, documents, briefs, audio files"}
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.txt,.eml,.mp3,.wav,.ogg,.json,.csv"
                    onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                    className="hidden"
                  />
                </div>
              </div>

              {uploadedFiles.length > 0 && (
                <div className="mt-2 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Uploaded Files:</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFiles}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4 mr-1" />
                      Clear
                    </Button>
                  </div>
                  <div className="space-y-1">
                    {uploadedFiles.map((fileName, index) => (
                      <div key={index} className="flex items-center space-x-2 text-sm text-gray-600">
                        <FileText className="h-4 w-4" />
                        <span>{fileName}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <Button
              onClick={handleGenerate}
              disabled={!contentNeed.trim() || !sessionId || isProcessing}
              className="w-full bg-emerald-500 hover:bg-emerald-600"
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Creating content brief...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Generate Brief</span>
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        {result && (
          <div className="space-y-6">
            {/* Context Intelligence Analysis Section */}
            <Card>
              <CardHeader>
                <CardTitle>Context Intelligence Analysis</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Strategic assessment of your client communications
                </p>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 p-6 rounded-lg min-h-[200px] border border-blue-200">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed text-gray-800">
                    {result.context_analysis || "Context analysis not available"}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Executive Summary and Strategic Brief Section */}
            <Card>
              <CardHeader>
                <CardTitle>Strategic Content Brief</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Generated on {new Date(result.timestamp).toLocaleString()}
                </p>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-6 rounded-lg min-h-[400px] mb-4 border">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed text-gray-800">
                    {result.executive_brief || result.content_brief}
                  </div>
                </div>
                <Button
                  onClick={downloadBrief}
                  className="w-full"
                  variant="outline"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Complete Brief as Text File
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ContentBriefGenerator;
