// Base URL for credit card recommendation API
const API_BASE_URL = 'https://credit-card.demo.aiplanet.com'; // Updated to deployed backend

// Types for API responses
export interface EmailSMS {
  email: string;
  sms: string;
}

export interface Recommendation {
  customer_name: string;
  recommendation: string;
  personalized_messages: EmailSMS;
}

export interface ApiResponse {
  recommendations: Recommendation[];
}

// Upload CSV file and get recommendations directly
export const uploadCSV = async (file: File): Promise<ApiResponse> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    console.log("Uploading CSV file:", {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });
    
    const response = await fetch(`${API_BASE_URL}/api/upload-csv`, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to upload file");
    }
    
    return await response.json();
  } catch (error) {
    console.error("API error:", error);
    throw error;
  }
};
