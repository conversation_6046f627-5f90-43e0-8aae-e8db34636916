
import { useState } from "react";
import Layout from "@/components/Layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, CreditCard, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { uploadCSV } from "./api";
import type { ApiResponse } from "./api";

const CreditCardRecommendation = () => {
  const [file, setFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<ApiResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
      setError(null);
    }
  };

  const triggerFileUpload = () => {
    document.getElementById('cards-file')?.click();
  };

  const handleAnalyze = async () => {
    if (!file) {
      setError("Please select a file first");
      return;
    }
    
    setIsProcessing(true);
    setError(null);
    
    try {
      const data = await uploadCSV(file);
      setResult(data);
    } catch (err) {
      console.error("Error uploading file:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsProcessing(false);
    }
  };

  // Format the recommendation text for better display
  const formatRecommendation = (text: string) => {
    // Replace markdown headers with styled HTML
    let formatted = text
      .replace(/### 👤Customer Details:/g, '<h3 class="text-lg font-bold mt-4 mb-2">👤 Customer Details:</h3>')
      .replace(/### 📈 Customer Analysis/g, '<h3 class="text-lg font-bold mt-4 mb-2">📈 Customer Analysis:</h3>')
      .replace(/###💡Tailored Recommendation/g, '<h3 class="text-lg font-bold mt-4 mb-2">💡 Tailored Recommendation:</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold text
      .replace(/\n\n/g, '<br/><br/>') // Line breaks
      .replace(/\n/g, '<br/>'); // Line breaks
    
    return formatted;
  };

  return (
    <Layout
      title="Credit Card Recommendation"
      description="Intelligent credit card matching based on user profiles and spending patterns."
      category="Financial Banking"
    >
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload Customer Data</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div 
              onClick={triggerFileUpload}
              className="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-emerald-400 transition-colors cursor-pointer"
            >
              <div className="text-center">
                <CreditCard className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <div className="text-sm text-gray-600">
                  {file ? file.name : "Upload cards_data.csv"}
                </div>
                <input
                  id="cards-file"
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </div>
            
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <Button 
              onClick={handleAnalyze}
              disabled={!file || isProcessing}
              className="w-full"
              style={{ backgroundColor: '#44924C' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3a7d42'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#44924C'}
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Analyzing credit card data...</span>
                </div>
              ) : (
                "Analyze Credit Options"
              )}
            </Button>
          </CardContent>
        </Card>

        {result && result.recommendations && result.recommendations.length > 0 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recommended Credit Cards</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {result.recommendations.map((recommendation, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-semibold text-lg text-gray-900 mb-3">
                        {recommendation.customer_name}
                      </h3>
                      
                      <div 
                        className="text-sm text-gray-700 mb-4"
                        dangerouslySetInnerHTML={{ __html: formatRecommendation(recommendation.recommendation) }}
                      />
                      
                      <div className="mt-4 border-t pt-4">
                        <h4 className="font-medium text-gray-900 mb-2">Personalized Messages</h4>
                        
                        <div className="bg-gray-50 p-3 rounded-md mb-3">
                          <h5 className="font-medium text-gray-700 mb-1">Email</h5>
                          <p className="text-sm text-gray-600">{recommendation.personalized_messages.email}</p>
                        </div>
                        
                        <div className="bg-gray-50 p-3 rounded-md">
                          <h5 className="font-medium text-gray-700 mb-1">SMS</h5>
                          <p className="text-sm text-gray-600">{recommendation.personalized_messages.sms}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default CreditCardRecommendation;
