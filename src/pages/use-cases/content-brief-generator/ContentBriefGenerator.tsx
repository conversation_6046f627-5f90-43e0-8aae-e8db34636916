import { useState, useRef } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, FileText, Download, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Import API functions and types
import {
  uploadDocuments,
  generateContentBrief,
  downloadContentBrief,
  clearSession,
  type ContentBriefResponse
} from "./api";

const ContentBriefGenerator = () => {
  const [contentNeed, setContentNeed] = useState("");
  const [files, setFiles] = useState<FileList | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<ContentBriefResponse | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileUpload = async (selectedFiles: FileList) => {
    if (!selectedFiles || selectedFiles.length === 0) return;

    setIsProcessing(true);

    try {
      const data = await uploadDocuments(selectedFiles);

      if (data.success) {
        setSessionId(data.session_id);
        setUploadedFiles(data.processed_files);
        setFiles(selectedFiles);
        toast({
          title: "Files uploaded successfully",
          description: `${data.processed_files.length} files processed`,
        });
      } else {
        throw new Error(data.message || "Upload failed");
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGenerate = async () => {
    if (!contentNeed.trim()) {
      toast({
        title: "Query required",
        description: "Please describe your content brief needs",
        variant: "destructive",
      });
      return;
    }

    if (!sessionId) {
      toast({
        title: "Files required",
        description: "Please upload client communication files first",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const data = await generateContentBrief(contentNeed, sessionId);

      if (data.success) {
        setResult(data);
        toast({
          title: "Content brief generated",
          description: "Your strategic content brief is ready",
        });
      } else {
        throw new Error("Generation failed");
      }
    } catch (error) {
      console.error('Generation error:', error);
      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const clearFiles = async () => {
    if (sessionId) {
      try {
        await clearSession(sessionId);
      } catch (error) {
        console.error('Clear session error:', error);
      }
    }
    
    setFiles(null);
    setUploadedFiles([]);
    setSessionId(null);
    setResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const downloadBrief = async () => {
    if (!sessionId) return;

    try {
      const data = await downloadContentBrief(sessionId);
      
      if (data.success && data.download_url) {
        // Open download URL in new tab
        window.open(data.download_url, '_blank');
      } else {
        // Fallback: create text file from result
        if (result) {
          const content = `CONTEXT INTELLIGENCE ANALYSIS\n\n${result.context_intelligence_analysis}\n\nEXECUTIVE SUMMARY\n\n${result.executive_summary}`;
          const blob = new Blob([content], { type: 'text/plain' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `content-brief-${new Date().toISOString().split('T')[0]}.txt`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      }
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "Download failed",
        description: error instanceof Error ? error.message : "Failed to download brief",
        variant: "destructive",
      });
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Content Brief Generator</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Transform client communications into strategic content briefs. Upload client emails, documents, and requirements to generate comprehensive content strategies.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Input */}
          <div className="space-y-6">
            {/* File Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Upload Client Communications
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="file-upload">Select Files (PDF, DOC, TXT, Email)</Label>
                  <input
                    ref={fileInputRef}
                    type="file"
                    id="file-upload"
                    multiple
                    accept=".pdf,.doc,.docx,.txt,.eml,.msg"
                    onChange={(e) => {
                      const selectedFiles = e.target.files;
                      if (selectedFiles) handleFileUpload(selectedFiles);
                    }}
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isProcessing}
                    className="w-full mt-2"
                    variant="outline"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {isProcessing ? "Processing..." : "Choose Files"}
                  </Button>
                </div>

                {uploadedFiles.length > 0 && (
                  <div className="space-y-2">
                    <Label>Uploaded Files:</Label>
                    <div className="space-y-1">
                      {uploadedFiles.map((filename, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-green-600" />
                            <span className="text-sm text-green-800">{filename}</span>
                          </div>
                        </div>
                      ))}
                      <Button
                        onClick={clearFiles}
                        size="sm"
                        variant="outline"
                        className="mt-2"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Clear Files
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Content Requirements */}
            <Card>
              <CardHeader>
                <CardTitle>Content Brief Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="content-need">Describe your content needs</Label>
                    <Textarea
                      id="content-need"
                      value={contentNeed}
                      onChange={(e) => setContentNeed(e.target.value)}
                      placeholder="Example: Create a comprehensive content strategy for our new product launch targeting enterprise customers. Include blog posts, social media content, and email campaigns focusing on ROI and efficiency benefits."
                      className="min-h-[120px] mt-2"
                    />
                  </div>

                  <Button
                    onClick={handleGenerate}
                    disabled={isProcessing || !contentNeed.trim() || !sessionId}
                    className="w-full"
                  >
                    {isProcessing ? "Generating..." : "Generate Content Brief"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Results */}
          <div className="space-y-6">
            {result && (
              <>
                {/* Context Intelligence Analysis */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Context Intelligence Analysis</CardTitle>
                    <Button
                      onClick={downloadBrief}
                      size="sm"
                      variant="outline"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      <div className="whitespace-pre-wrap text-sm text-gray-700 bg-gray-50 p-4 rounded-lg border">
                        {result.context_intelligence_analysis}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Executive Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Executive Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      <div className="whitespace-pre-wrap text-sm text-gray-700 bg-gray-50 p-4 rounded-lg border">
                        {result.executive_summary}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}

            {!result && (
              <Card>
                <CardContent className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Content Brief Generated</h3>
                  <p className="text-gray-600">
                    Upload client communications and describe your content needs to generate a strategic brief.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ContentBriefGenerator;
