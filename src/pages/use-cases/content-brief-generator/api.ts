// Base URL for Content Brief Generator API
const API_BASE_URL = "/api/content-brief";

// Types for API responses
export interface DocumentUploadResponse {
  success: boolean;
  message: string;
  session_id: string;
  processed_files: string[];
}

export interface ContentBriefResponse {
  success: boolean;
  context_intelligence_analysis: string;
  executive_summary: string;
  session_id: string;
}

export interface DownloadResponse {
  success: boolean;
  download_url?: string;
  message: string;
}

// Upload documents for content brief generation
export const uploadDocuments = async (files: FileList): Promise<DocumentUploadResponse> => {
  try {
    const formData = new FormData();
    
    Array.from(files).forEach((file) => {
      formData.append('files', file);
    });

    console.log("Uploading documents:", {
      fileCount: files.length,
      fileNames: Array.from(files).map(f => f.name)
    });

    const response = await fetch(`${API_BASE_URL}/upload-documents`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Upload failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Document upload error:", error);
    throw error;
  }
};

// Generate content brief
export const generateContentBrief = async (
  contentNeed: string, 
  sessionId?: string
): Promise<ContentBriefResponse> => {
  try {
    const requestBody: any = {
      content_need: contentNeed,
    };

    if (sessionId) {
      requestBody.session_id = sessionId;
    }

    console.log("Generating content brief:", requestBody);

    const response = await fetch(`${API_BASE_URL}/generate-brief`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Generation failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Content brief generation error:", error);
    throw error;
  }
};

// Download content brief as document
export const downloadContentBrief = async (sessionId: string): Promise<DownloadResponse> => {
  try {
    console.log("Downloading content brief for session:", sessionId);

    const response = await fetch(`${API_BASE_URL}/download-brief?session_id=${sessionId}`, {
      method: 'GET',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Download failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Content brief download error:", error);
    throw error;
  }
};

// Clear session data
export const clearSession = async (sessionId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/clear-session?session_id=${sessionId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to clear session");
    }

    return await response.json();
  } catch (error) {
    console.error("Clear session error:", error);
    throw error;
  }
};
