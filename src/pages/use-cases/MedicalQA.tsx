
import { useState, useRef, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";

import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, <PERSON>H<PERSON>er, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, MessageSquare, X, Send, Bot, User, FileText, Settings } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// API Configuration
const API_BASE_URL = "/api/medical-qa";

// Types
interface MedicalResponse {
  answer: string;
  source_type: string;
  source_info: string;
  urgent_notice?: string;
  disclaimer: string;
}

interface DocumentUploadResponse {
  success: boolean;
  message: string;
  collection_name?: string;
  filename?: string;
  document_id?: string;
}

interface SampleQuestionsResponse {
  questions: string[];
  document_based: boolean;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  urgent_notice?: string;
  source_info?: string;
  disclaimer?: string;
}

const MedicalQA = () => {
  const [currentMessage, setCurrentMessage] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [file, setFile] = useState<File | null>(null);
  const [userType, setUserType] = useState("Patient");
  const [urgency, setUrgency] = useState("Medium");
  const [responseStyle, setResponseStyle] = useState("Moderate");
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [collectionName, setCollectionName] = useState<string | null>(null);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [sampleQuestions, setSampleQuestions] = useState<string[]>([]);
  const [showSettings, setShowSettings] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Load initial welcome message only
  useEffect(() => {
    // Add welcome message
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      type: 'assistant',
      content: `Hello! I'm your Medical Q&A Assistant. I can help answer medical questions and provide health information.

Please note that I provide general medical information for educational purposes only and should not replace professional medical advice. Always consult with qualified healthcare professionals for medical concerns.

You can upload a medical document (PDF) to get context-specific answers, or ask general medical questions.

How can I help you today?`,
      timestamp: new Date(),
      source_info: "AI Medical Assistant"
    };

    setMessages([welcomeMessage]);
  }, []);

  const handleFileUpload = async (selectedFile: File) => {
    if (!selectedFile) return;

    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", selectedFile);

    try {
      const response = await fetch(`${API_BASE_URL}/upload-document`, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const data: DocumentUploadResponse = await response.json();

      if (data.success) {
        setCollectionName(data.collection_name || null);
        setUploadedFileName(data.filename || null);
        setFile(selectedFile);

        // Load sample questions for the uploaded document
        if (data.collection_name) {
          await loadSampleQuestions(data.collection_name);

          // Add system message about document upload
          const systemMessage: ChatMessage = {
            id: Date.now().toString(),
            type: 'system',
            content: `Document "${data.filename}" has been uploaded and processed successfully! You can now ask questions about this document. Check the sample questions below for ideas.`,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, systemMessage]);
        }

        toast({
          title: "Document uploaded successfully",
          description: `${data.filename} is ready for questions!`,
        });
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload document",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const loadSampleQuestions = async (collection?: string) => {
    try {
      const url = collection
        ? `${API_BASE_URL}/sample-questions?collection_name=${collection}`
        : `${API_BASE_URL}/sample-questions`;

      const response = await fetch(url);
      if (response.ok) {
        const data: SampleQuestionsResponse = await response.json();
        setSampleQuestions(data.questions);
      }
    } catch (error) {
      console.error("Failed to load sample questions:", error);
    }
  };

  const handleSubmit = async () => {
    if (!currentMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: currentMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setCurrentMessage("");
    setIsProcessing(true);

    try {
      const requestBody = {
        question: userMessage.content,
        user_type: userType,
        urgency: urgency,
        style: responseStyle,
        collection_name: collectionName
      };

      const response = await fetch(`${API_BASE_URL}/ask-question`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Request failed: ${response.statusText}`);
      }

      const data: MedicalResponse = await response.json();

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: data.answer,
        timestamp: new Date(),
        urgent_notice: data.urgent_notice || undefined,
        source_info: data.source_info,
        disclaimer: data.disclaimer
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error) {
      console.error("Question processing error:", error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'system',
        content: `Error: ${error instanceof Error ? error.message : "Failed to process question"}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Processing failed",
        description: error instanceof Error ? error.message : "Failed to process question",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const clearDocument = async () => {
    if (!collectionName) return;

    try {
      const response = await fetch(`${API_BASE_URL}/clear-document?collection_name=${collectionName}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setCollectionName(null);
        setUploadedFileName(null);
        setFile(null);
        setSampleQuestions([]);
        setMessages([]);

        const systemMessage: ChatMessage = {
          id: Date.now().toString(),
          type: 'system',
          content: "Document cleared. Starting fresh with general medical knowledge.",
          timestamp: new Date()
        };
        setMessages([systemMessage]);

        toast({
          title: "Document cleared",
          description: "Starting fresh with general medical knowledge",
        });
      }
    } catch (error) {
      console.error("Clear document error:", error);
      toast({
        title: "Clear failed",
        description: "Failed to clear document",
        variant: "destructive",
      });
    }
  };

  const handleSampleQuestion = (sampleQuestion: string) => {
    setCurrentMessage(sampleQuestion);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <Layout
      title="Medical Q&A Assistant"
      description="Intelligent medical information assistant for healthcare professionals."
      category="Pharma & Healthcare"
    >
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header with Settings */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-emerald-600" />
                <CardTitle>Medical Q&A Chat Assistant</CardTitle>
              </div>
              <div className="flex items-center space-x-2">
                {uploadedFileName && (
                  <div className="flex items-center space-x-2 px-3 py-1 bg-emerald-50 rounded-full">
                    <FileText className="h-4 w-4 text-emerald-600" />
                    <span className="text-sm text-emerald-700">{uploadedFileName}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearDocument}
                      className="h-6 w-6 p-0 text-emerald-700 hover:text-emerald-800"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSettings(!showSettings)}
                  className="flex items-center space-x-1"
                >
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Settings Panel */}
        {showSettings && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Advanced Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>User Type</Label>
                  <Select value={userType} onValueChange={setUserType}>
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Patient">Patient</SelectItem>
                      <SelectItem value="Healthcare Professional">Healthcare Professional</SelectItem>
                      <SelectItem value="Student">Student</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Urgency Level</Label>
                  <Select value={urgency} onValueChange={setUrgency}>
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Document Upload */}
              <div>
                <Label>Upload Medical Document (Optional)</Label>
                <div
                  className="mt-2 flex items-center justify-center w-full h-24 border-2 border-dashed border-gray-300 rounded-xl hover:border-emerald-400 transition-colors cursor-pointer"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <div className="text-center">
                    <Upload className="h-6 w-6 text-gray-400 mx-auto mb-1" />
                    <div className="text-sm text-gray-600">
                      {isUploading ? "Uploading..." : file ? file.name : "Upload PDF document"}
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".pdf"
                      onChange={(e) => {
                        const selectedFile = e.target.files?.[0];
                        if (selectedFile) {
                          handleFileUpload(selectedFile);
                        }
                      }}
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Response Style Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Response Style</CardTitle>
            <p className="text-sm text-gray-600 mt-1">Choose response length:</p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <Button
                variant={responseStyle === "Concise" ? "default" : "outline"}
                className={`h-auto p-4 flex flex-col items-start space-y-1 ${
                  responseStyle === "Concise" ? "bg-emerald-500 hover:bg-emerald-600" : "hover:bg-emerald-50"
                }`}
                onClick={() => setResponseStyle("Concise")}
              >
                <span className="font-medium">Concise</span>
                <span className="text-xs opacity-75">Brief, essential information only</span>
              </Button>
              <Button
                variant={responseStyle === "Moderate" ? "default" : "outline"}
                className={`h-auto p-4 flex flex-col items-start space-y-1 ${
                  responseStyle === "Moderate" ? "bg-emerald-500 hover:bg-emerald-600" : "hover:bg-emerald-50"
                }`}
                onClick={() => setResponseStyle("Moderate")}
              >
                <span className="font-medium">Moderate</span>
                <span className="text-xs opacity-75">Balanced detail with explanations</span>
              </Button>
              <Button
                variant={responseStyle === "Prolonged" ? "default" : "outline"}
                className={`h-auto p-4 flex flex-col items-start space-y-1 ${
                  responseStyle === "Prolonged" ? "bg-emerald-500 hover:bg-emerald-600" : "hover:bg-emerald-50"
                }`}
                onClick={() => setResponseStyle("Prolonged")}
              >
                <span className="font-medium">Prolonged</span>
                <span className="text-xs opacity-75">Comprehensive, detailed response</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Sample Questions - Only show when document is uploaded */}
        {sampleQuestions.length > 0 && uploadedFileName && (
          <Card>
            <CardHeader>
              <CardTitle>Document-Based Sample Questions</CardTitle>
              <p className="text-sm text-gray-600 mt-1">Based on your uploaded document:</p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {sampleQuestions.map((sampleQuestion: string, index: number) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="text-left h-auto p-3 justify-start hover:bg-emerald-50"
                    onClick={() => handleSampleQuestion(sampleQuestion)}
                  >
                    <span className="text-sm">{sampleQuestion}</span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Chat Messages */}
        <Card className="flex flex-col">
          <CardHeader className="pb-3 border-b">
            <CardTitle className="text-lg">Medical Consultation</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col p-0">
            {/* Messages Container */}
            <div className="min-h-[400px] max-h-[600px] overflow-y-auto space-y-4 p-4 bg-gray-50">
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Bot className="h-12 w-12 mx-auto mb-3 text-emerald-500" />
                    <p className="text-lg font-medium">Welcome to Medical Q&A</p>
                    <p className="text-sm">Ask me any medical question to get started</p>
                  </div>
                </div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[85%] rounded-lg p-4 shadow-sm ${
                        message.type === 'user'
                          ? 'bg-emerald-500 text-white'
                          : message.type === 'system'
                          ? 'bg-yellow-50 border border-yellow-200 text-yellow-800'
                          : 'bg-white border border-gray-200 text-gray-800'
                      }`}
                    >
                      <div className="flex items-start space-x-2">
                        {message.type === 'user' ? (
                          <User className="h-5 w-5 mt-0.5 flex-shrink-0" />
                        ) : message.type === 'assistant' ? (
                          <Bot className="h-5 w-5 mt-0.5 flex-shrink-0 text-emerald-600" />
                        ) : null}
                        <div className="flex-1">
                          {message.urgent_notice && (
                            <div className="mb-3 p-2 bg-red-100 border border-red-300 rounded text-red-800 text-sm">
                              <strong>⚠️ Urgent Notice:</strong> {message.urgent_notice}
                            </div>
                          )}
                          <div className="whitespace-pre-wrap text-sm leading-relaxed">
                            {message.content}
                          </div>
                          {message.source_info && (
                            <div className="mt-2 text-xs opacity-75">
                              Source: {message.source_info}
                            </div>
                          )}
                          {message.disclaimer && (
                            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                              <strong>Disclaimer:</strong> {message.disclaimer}
                            </div>
                          )}
                          <div className="mt-1 text-xs opacity-50">
                            {message.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="border-t p-4 bg-white">
              <div className="flex space-x-2">
                <Textarea
                  value={currentMessage}
                  onChange={(e) => setCurrentMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your medical question here... (Press Enter to send, Shift+Enter for new line)"
                  className="flex-1 min-h-[60px] max-h-[120px] resize-none border-gray-300 focus:border-emerald-500"
                  disabled={isProcessing}
                />
                <Button
                  onClick={handleSubmit}
                  disabled={!currentMessage.trim() || isProcessing}
                  className="bg-emerald-500 hover:bg-emerald-600 px-4 self-end"
                >
                  {isProcessing ? (
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

      </div>
    </Layout>
  );
};

export default MedicalQA;
