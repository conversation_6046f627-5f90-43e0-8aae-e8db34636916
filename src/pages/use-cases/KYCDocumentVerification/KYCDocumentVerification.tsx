import { useState, useRef, useEffect } from "react";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload, Shield, FileText, CheckCircle, AlertCircle, Loader2, Clock } from "lucide-react";

const API_BASE_URL = "https://kycverify.demo.aiplanet.com";

const KYCDocumentVerification = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isPolling, setIsPolling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationResult, setValidationResult] = useState<any>(null);
  const [generationId, setGenerationId] = useState<string | null>(null);
  const [pollingProgress, setPollingProgress] = useState<string>("");
  
  // Documents
  const [idDocument, setIdDocument] = useState<File | null>(null);
  const [kycForm, setKycForm] = useState<File | null>(null);
  
  // File input refs
  const idDocumentRef = useRef<HTMLInputElement>(null);
  const kycFormRef = useRef<HTMLInputElement>(null);

  // Polling for AI Planet results
  const pollForResults = async (genId: string) => {
    setIsPolling(true);
    let attempts = 0;
    const maxAttempts = 30; // 5 minutes max (30 * 10 seconds)
    
    const poll = async () => {
      attempts++;
      setPollingProgress(`Checking AI processing status... (${attempts}/${maxAttempts})`);
      
      try {
        // Check AI Planet API directly for results
        const response = await fetch(`https://app.aiplanet.com/api/v1/9fc09eb9-385a-4ac7-b7b3-a5714072f670/generations/${genId}`, {
          headers: {
            'Accept': 'application/json',
            'Authorization': import.meta.env.VITE_AIPLANET_API_KEY || 'sk-91eyDASRTkizuwS_4B2sXvDLBdPs_PIoW9j3evHWJl0'
          }
        });
        
        if (response.ok) {
          const result = await response.json();
          
                     if (result.status === 'completed' && result.outputs?.documents?.[0]?.page_content) {
             // AI processing complete! Extract the validation report
             const aiReport = result.outputs.documents[0].page_content;
             
             // Clean up markdown formatting for better display
             const cleanedReport = aiReport
               .replace(/\*\*(.*?)\*\*/g, '$1')  // Remove **bold** formatting
               .replace(/### /g, '')              // Remove ### headers
               .replace(/- \*\*/g, '• ')          // Replace - ** with bullet points
               .replace(/\*\*/g, '')              // Remove any remaining **
               .trim();
             
             setValidationResult({
               session_id: genId,
               status: "success",
               validation_report: cleanedReport,
               extracted_data: {
                 id_document: `AI Planet Generation: ${genId}`,
                 kyc_form: `Processing completed at ${new Date().toLocaleTimeString()}`
               },
               ai_processing_time: `${attempts * 10} seconds`,
               final_status: (cleanedReport.toLowerCase().includes('overall pass/fail status: fail') || cleanedReport.toLowerCase().includes('status: fail') || aiReport.toLowerCase().includes('**fail**')) ? 'FAILED' : 'PASSED'
             });
            
            setIsPolling(false);
            setPollingProgress("");
            return;
          } else if (result.status === 'failed') {
            throw new Error('AI processing failed');
          }
          // Still pending, continue polling
        } else {
          console.warn('Polling request failed:', response.status);
        }
      } catch (err) {
        console.warn('Polling error:', err);
      }
      
      // Continue polling if not complete and under max attempts
      if (attempts < maxAttempts) {
        setTimeout(poll, 10000); // Poll every 10 seconds
      } else {
        setError('AI processing timeout. Please try again.');
        setIsPolling(false);
        setPollingProgress("");
      }
    };
    
    // Start polling after a short delay
    setTimeout(poll, 5000); // First check after 5 seconds
  };

  const validateDocuments = async () => {
    if (!idDocument || !kycForm) {
      setError("Please upload both ID document and KYC form");
      return;
    }

    setIsLoading(true);
    setError(null);
    setValidationResult(null);
    setGenerationId(null);
    
    try {
      const formData = new FormData();
      formData.append('id_document', idDocument);
      formData.append('kyc_form', kycForm);
      
      const response = await fetch(`${API_BASE_URL}/validate-kyc`, {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error(`Validation failed: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      // Extract generation ID from the response
      const idDocumentText = result.extracted_data?.id_document || "";
      const genIdMatch = idDocumentText.match(/Generation ID: ([a-f0-9-]+)/);
      
      if (genIdMatch) {
        const genId = genIdMatch[1];
        setGenerationId(genId);
        setPollingProgress("Documents uploaded successfully! AI processing started...");
        
        // Start polling for results
        pollForResults(genId);
      } else {
        // Fallback to immediate result if no generation ID found
        setValidationResult(result);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during validation');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setIdDocument(null);
    setKycForm(null);
    setValidationResult(null);
    setError(null);
    setGenerationId(null);
    setIsPolling(false);
    setPollingProgress("");
  };

  return (
    <Layout
      title="KYC Document Verification"
      description="Upload ID document and KYC form for automated compliance validation."
      category="Financial Banking"
    >
      <div className="space-y-8">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Processing Status */}
        {(isLoading || isPolling) && (
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-center space-x-4">
                <Loader2 className="h-8 w-8 animate-spin text-emerald-500" />
                <div className="text-center">
                  <div className="font-medium text-lg">
                    {isLoading ? "Uploading Documents..." : "AI Processing in Progress"}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {isLoading ? "Submitting to AI Planet API..." : pollingProgress}
                  </div>
                  {isPolling && (
                    <div className="text-xs text-gray-500 mt-2 flex items-center justify-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>AI typically takes 30-60 seconds to process documents</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Document Upload Section */}
        {!validationResult && !isPolling && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Document Upload</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* ID Document Upload */}
                <div>
                  <h3 className="font-medium mb-2">ID Document</h3>
                  <div 
                    className="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-emerald-400 transition-colors cursor-pointer"
                    onClick={() => idDocumentRef.current?.click()}
                  >
                    <div className="text-center">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <div className="text-sm text-gray-600">
                        {idDocument ? idDocument.name : "Upload Passport/Aadhaar/PAN"}
                      </div>
                      <input
                        ref={idDocumentRef}
                        type="file"
                        accept=".jpg,.png,.jpeg,.pdf"
                        onChange={(e) => setIdDocument(e.target.files?.[0] || null)}
                        className="hidden"
                      />
                    </div>
                  </div>
                </div>

                {/* KYC Form Upload */}
                <div>
                  <h3 className="font-medium mb-2">KYC Onboarding Form</h3>
                  <div 
                    className="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-emerald-400 transition-colors cursor-pointer"
                    onClick={() => kycFormRef.current?.click()}
                  >
                    <div className="text-center">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <div className="text-sm text-gray-600">
                        {kycForm ? kycForm.name : "Upload KYC Application Form"}
                      </div>
                      <input
                        ref={kycFormRef}
                        type="file"
                        accept=".jpg,.png,.jpeg,.pdf"
                        onChange={(e) => setKycForm(e.target.files?.[0] || null)}
                        className="hidden"
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <Button 
                onClick={validateDocuments}
                disabled={!idDocument || !kycForm || isLoading}
                className="w-full"
              style={{ backgroundColor: '#44924C' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3a7d42'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#44924C'}
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Validating Documents...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <span>Validate KYC Documents</span>
                  </div>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Validation Results */}
        {validationResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Validation Report</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Status Header */}
              <div className="flex items-center justify-center p-6 border rounded-lg">
                {validationResult.final_status === 'PASSED' ? (
                  <div className="text-center">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-2xl font-bold text-green-700">✅ KYC Validation Passed</h3>
                    <p className="text-sm text-green-600 mt-2">All compliance requirements met</p>
                  </div>
                ) : (
                  <div className="text-center">
                    <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
                    <h3 className="text-2xl font-bold text-red-700">❌ KYC Validation Failed</h3>
                    <p className="text-sm text-red-600 mt-2">Multiple compliance issues found</p>
                    {validationResult.ai_processing_time && (
                      <p className="text-xs text-gray-500 mt-1">
                        Processed in {validationResult.ai_processing_time} by AI Planet
                      </p>
                    )}
                  </div>
                )}
              </div>

              {/* Validation Report */}
              <div>
                <h4 className="font-semibold mb-3">Detailed Validation Report:</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="whitespace-pre-wrap text-sm font-mono">
                    {validationResult.validation_report}
                  </pre>
                </div>
              </div>

              {/* Extracted Data */}
              {validationResult.extracted_data && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">ID Document Data:</h4>
                    <div className="bg-blue-50 p-3 rounded text-sm">
                      <pre className="whitespace-pre-wrap">
                        {JSON.stringify(validationResult.extracted_data.id_document, null, 2)}
                      </pre>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">KYC Form Data:</h4>
                    <div className="bg-green-50 p-3 rounded text-sm">
                      <pre className="whitespace-pre-wrap">
                        {JSON.stringify(validationResult.extracted_data.kyc_form, null, 2)}
                      </pre>
                    </div>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div>
                <Button 
              onClick={resetForm} 
              className="w-full"
              style={{ backgroundColor: '#44924C' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3a7d42'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#44924C'}
            >
                  Validate New Documents
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </Layout>
  );
};

export default KYCDocumentVerification;
