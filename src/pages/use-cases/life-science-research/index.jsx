import React, { useState, useEffect } from 'react';
import { 
  Box, Button, Container, FormControl, FormLabel, Select, 
  Heading, Text, VStack, HStack, Spinner, Alert, AlertIcon,
  Tabs, TabList, TabPanels, Tab, TabPanel, Badge, Divider,
  Card, CardHeader, CardBody, useToast
} from '@chakra-ui/react';
import { UploadIcon, CheckIcon, WarningIcon } from '@chakra-ui/icons';

const LifeScienceResearch = () => {
  const [file, setFile] = useState(null);
  const [targetAudience, setTargetAudience] = useState('researchers');
  const [summaryDepth, setSummaryDepth] = useState('detailed');
  const [taskId, setTaskId] = useState(null);
  const [status, setStatus] = useState(null);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pollingInterval, setPollingInterval] = useState(null);
  const toast = useToast();

  // Clean up polling on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) clearInterval(pollingInterval);
    };
  }, [pollingInterval]);

  const handleFileChange = (e) => {
    if (e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleSubmit = async () => {
    if (!file) {
      setError('Please select a PDF file');
      return;
    }

    if (!file.name.toLowerCase().endsWith('.pdf')) {
      setError('Only PDF files are supported');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('target_audience', targetAudience);
      formData.append('summary_depth', summaryDepth);

      const response = await fetch('/api/summarize', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to submit paper');
      }

      const data = await response.json();
      setTaskId(data.task_id);
      setStatus(data.status);
      
      // Start polling for status
      const interval = setInterval(() => checkStatus(data.task_id), 5000);
      setPollingInterval(interval);
      
      toast({
        title: 'Paper submitted',
        description: 'Your paper is being processed',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (err) {
      setError(err.message);
      toast({
        title: 'Submission failed',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const checkStatus = async (id) => {
    try {
      const response = await fetch(`/api/status/${id}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to check status');
      }
      
      const data = await response.json();
      setStatus(data.status);
      
      if (data.status === 'completed') {
        // Stop polling and fetch results
        clearInterval(pollingInterval);
        setPollingInterval(null);
        fetchResult(id);
        
        toast({
          title: 'Processing complete',
          description: 'Your paper has been successfully analyzed',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      } else if (data.status === 'failed') {
        // Stop polling on failure
        clearInterval(pollingInterval);
        setPollingInterval(null);
        setError(data.message);
        
        toast({
          title: 'Processing failed',
          description: data.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (err) {
      setError(err.message);
    }
  };

  const fetchResult = async (id) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/result/${id}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to fetch results');
      }
      
      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError(err.message);
      toast({
        title: 'Failed to fetch results',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const deleteResult = async () => {
    if (!taskId) return;
    
    try {
      setLoading(true);
      const response = await fetch(`/api/result/${taskId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to delete result');
      }
      
      setTaskId(null);
      setStatus(null);
      setResult(null);
      setFile(null);
      
      toast({
        title: 'Result deleted',
        description: 'The analysis has been deleted successfully',
        status: 'info',
        duration: 5000,
        isClosable: true,
      });
    } catch (err) {
      setError(err.message);
      toast({
        title: 'Deletion failed',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Heading as="h1" size="xl">Life Science Research Paper Analyzer</Heading>
        <Text fontSize="lg">
          Upload a scientific research paper to generate audience-specific insights and summaries.
        </Text>

        {error && (
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            {error}
          </Alert>
        )}

        {!result && (
          <Card>
            <CardHeader>
              <Heading size="md">Upload Research Paper</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={6} align="stretch">
                <FormControl>
                  <FormLabel>Upload PDF File</FormLabel>
                  <input
                    type="file"
                    accept=".pdf"
                    onChange={handleFileChange}
                    style={{ 
                      border: '1px dashed gray', 
                      padding: '20px',
                      borderRadius: '5px',
                      width: '100%' 
                    }}
                  />
                  {file && <Text mt={2}>Selected: {file.name}</Text>}
                </FormControl>

                <FormControl>
                  <FormLabel>Target Audience</FormLabel>
                  <Select 
                    value={targetAudience} 
                    onChange={(e) => setTargetAudience(e.target.value)}
                  >
                    <option value="researchers">Researchers</option>
                    <option value="clinicians">Clinicians</option>
                    <option value="regulatory">Regulatory</option>
                  </Select>
                </FormControl>

                <FormControl>
                  <FormLabel>Summary Depth</FormLabel>
                  <Select 
                    value={summaryDepth} 
                    onChange={(e) => setSummaryDepth(e.target.value)}
                  >
                    <option value="brief">Brief</option>
                    <option value="detailed">Detailed</option>
                    <option value="comprehensive">Comprehensive</option>
                  </Select>
                </FormControl>

                <Button 
                  colorScheme="blue" 
                  onClick={handleSubmit} 
                  isLoading={loading}
                  leftIcon={<UploadIcon />}
                  isDisabled={!file || status === 'processing'}
                >
                  Submit Paper
                </Button>
              </VStack>
            </CardBody>
          </Card>
        )}

        {status === 'processing' && (
          <Card>
            <CardBody>
              <VStack spacing={4}>
                <Spinner size="xl" />
                <Text>Processing your research paper. This may take several minutes...</Text>
              </VStack>
            </CardBody>
          </Card>
        )}

        {result && (
          <Box>
            <HStack justify="space-between" mb={4}>
              <Heading size="md">Analysis Results</Heading>
              <Button colorScheme="red" size="sm" onClick={deleteResult}>
                Delete Results
              </Button>
            </HStack>

            <Tabs variant="enclosed" colorScheme="blue">
              <TabList>
                <Tab>Executive Summaries</Tab>
                <Tab>Key Insights</Tab>
                <Tab>Metadata</Tab>
              </TabList>

              <TabPanels>
                <TabPanel>
                  <VStack spacing={4} align="stretch">
                    {result.executive_summaries.map((summary, index) => (
                      <Card key={index}>
                        <CardHeader>
                          <HStack justify="space-between">
                            <Heading size="sm" textTransform="capitalize">
                              {summary.section}
                            </Heading>
                            <Badge colorScheme="blue">{summary.chunk_index}</Badge>
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <Text>{summary.executive_summary}</Text>
                        </CardBody>
                      </Card>
                    ))}
                  </VStack>
                </TabPanel>

                <TabPanel>
                  <VStack spacing={4} align="stretch">
                    <Card>
                      <CardHeader>
                        <Heading size="sm">Key Findings</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text>{result.key_findings}</Text>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Methodology Highlights</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text>{result.methodology_highlights}</Text>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Clinical Implications</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text>{result.clinical_implications}</Text>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Research Gaps</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text>{result.research_gaps}</Text>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Future Directions</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text>{result.future_directions}</Text>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Comparative Analysis</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text>{result.comparative_analysis}</Text>
                      </CardBody>
                    </Card>
                  </VStack>
                </TabPanel>

                <TabPanel>
                  <Card>
                    <CardHeader>
                      <Heading size="sm">Processing Metadata</Heading>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={3} align="stretch">
                        <Box>
                          <Text fontWeight="bold">File Information</Text>
                          <Text>Path: {result.processing_metadata.file_path}</Text>
                          <Text>Total Length: {result.processing_metadata.document_metadata.total_length} characters</Text>
                          <Text>Estimated Pages: {result.processing_metadata.document_metadata.estimated_pages}</Text>
                        </Box>
                        
                        <Divider />
                        
                        <Box>
                          <Text fontWeight="bold">Content Analysis</Text>
                          <Text>Has Tables: {result.processing_metadata.document_metadata.has_tables ? 'Yes' : 'No'}</Text>
                          <Text>Has Figures: {result.processing_metadata.document_metadata.has_figures ? 'Yes' : 'No'}</Text>
                          <Text>Has Equations: {result.processing_metadata.document_metadata.has_equations ? 'Yes' : 'No'}</Text>
                          <Text>Reference Count: {result.processing_metadata.document_metadata.reference_count}</Text>
                        </Box>
                        
                        <Divider />
                        
                        <Box>
                          <Text fontWeight="bold">Processing Details</Text>
                          <Text>Parsing Method: {result.processing_metadata.parsing_method}</Text>
                          <Text>Total Chunks: {result.processing_metadata.total_chunks}</Text>
                          <Text>Target Audience: {result.processing_metadata.target_audience}</Text>
                          <Text>Summary Depth: {result.processing_metadata.summary_depth}</Text>
                          <Text>Processing Status: {result.processing_metadata.processing_status}</Text>
                        </Box>
                        
                        <Divider />
                        
                        <Box>
                          <Text fontWeight="bold">Sections Found</Text>
                          <HStack flexWrap="wrap" spacing={2} mt={1}>
                            {result.processing_metadata.sections_found.map((section, index) => (
                              <Badge key={index} colorScheme="green" m={1}>
                                {section}
                              </Badge>
                            ))}
                          </HStack>
                        </Box>
                      </VStack>
                    </CardBody>
                  </Card>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Box>
        )}
      </VStack>
    </Container>
  );
};

export default LifeScienceResearch;