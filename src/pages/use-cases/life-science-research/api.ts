// Base URL for all API calls
const API_BASE_URL = 'http://0.0.0.0:8003';

// Helper function to safely parse JSON with debugging
const safeJsonParse = (text: string) => {
  try {
    return JSON.parse(text);
  } catch (e) {
    console.error("JSON Parse Error:", e);
    console.error("Raw response text:", text);
    console.error("Text length:", text.length);
    if (text.length > 0) {
      console.error("First 100 chars:", text.substring(0, 100));
      console.error("Last 100 chars:", text.substring(text.length - 100));
    }
    throw e;
  }
};

// Submit a research paper for analysis
export const submitPaper = async (file: File, targetAudience: string = 'researchers', summaryDepth: string = 'detailed') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('target_audience', targetAudience);
  formData.append('summary_depth', summaryDepth);

  try {
    console.log("Submitting paper:", {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      targetAudience,
      summaryDepth
    });

    const response = await fetch(`${API_BASE_URL}/api/summarize`, {
      method: 'POST',
      body: formData,
    });

    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries([...response.headers.entries()]));

    const text = await response.text(); 
    console.log("Raw response text:", text);
    console.log("Response text length:", text.length);

    if (!response.ok) {
      let errorMessage = 'Failed to submit paper';
      
      if (text) {
        try {
          const errorData = safeJsonParse(text);
          errorMessage = errorData.detail || errorMessage;
        } catch (e) {
          // If JSON parsing fails, use the raw text if available
          if (text) errorMessage = text;
        }
      }
      
      throw new Error(errorMessage);
    }

    if (!text || text.trim() === '') {
      console.error("Empty response received from server");
      throw new Error('Empty response received from server');
    }
    
    return safeJsonParse(text);
  } catch (error) {
    console.error("API error:", error);
    throw error;
  }
};

// Check the status of a processing task
export const checkStatus = async (taskId: string) => {
  try {
    console.log("Checking status for task:", taskId);
    
    const response = await fetch(`${API_BASE_URL}/api/status/${taskId}`);
    console.log("Status response code:", response.status);
    console.log("Status response headers:", Object.fromEntries([...response.headers.entries()]));
    
    const text = await response.text();
    console.log("Raw status response:", text);
    console.log("Status response length:", text.length);
    
    if (!response.ok) {
      let errorMessage = 'Failed to check status';
      
      if (text) {
        try {
          const errorData = safeJsonParse(text);
          errorMessage = errorData.detail || errorMessage;
        } catch (e) {
          if (text) errorMessage = text;
        }
      }
      
      throw new Error(errorMessage);
    }
    
    if (!text || text.trim() === '') {
      console.error("Empty status response received");
      throw new Error('Empty response received from server');
    }
    
    return safeJsonParse(text);
  } catch (error) {
    console.error("Status check error:", error);
    throw error;
  }
};

// Get the results of a completed analysis
export const getResult = async (taskId: string) => {
  try {
    console.log("Fetching results for task:", taskId);
    
    const response = await fetch(`${API_BASE_URL}/api/result/${taskId}`);
    console.log("Result response code:", response.status);
    console.log("Result response headers:", Object.fromEntries([...response.headers.entries()]));
    
    const text = await response.text();
    console.log("Raw result response:", text);
    console.log("Result response length:", text.length);
    
    if (!response.ok) {
      let errorMessage = 'Failed to fetch results';
      
      if (text) {
        try {
          const errorData = safeJsonParse(text);
          errorMessage = errorData.detail || errorMessage;
        } catch (e) {
          if (text) errorMessage = text;
        }
      }
      
      throw new Error(errorMessage);
    }
    
    if (!text || text.trim() === '') {
      console.error("Empty result response received");
      throw new Error('Empty response received from server');
    }
    
    return safeJsonParse(text);
  } catch (error) {
    console.error("Result fetch error:", error);
    throw error;
  }
};

// Delete a result
export const deleteResult = async (taskId: string) => {
  try {
    console.log("Deleting result for task:", taskId);
    
    const response = await fetch(`${API_BASE_URL}/api/result/${taskId}`, {
      method: 'DELETE',
    });
    console.log("Delete response code:", response.status);
    console.log("Delete response headers:", Object.fromEntries([...response.headers.entries()]));
    
    const text = await response.text();
    console.log("Raw delete response:", text);
    console.log("Delete response length:", text.length);
    
    if (!response.ok) {
      let errorMessage = 'Failed to delete result';
      
      if (text) {
        try {
          const errorData = safeJsonParse(text);
          errorMessage = errorData.detail || errorMessage;
        } catch (e) {
          if (text) errorMessage = text;
        }
      }
      
      throw new Error(errorMessage);
    }
    
    if (!text || text.trim() === '') {
      console.log("Empty but successful delete response");
      return { message: "Result deleted successfully" };
    }
    
    try {
      return safeJsonParse(text);
    } catch (e) {
      // If parsing fails but response was OK, return a success message
      console.warn("Delete response parsing failed but operation was successful");
      return { message: "Result deleted successfully" };
    }
  } catch (error) {
    console.error("Delete error:", error);
    throw error;
  }
};




