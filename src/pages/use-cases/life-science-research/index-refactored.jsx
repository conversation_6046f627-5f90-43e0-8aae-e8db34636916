import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, But<PERSON>, Container, <PERSON><PERSON>, AlertIcon,
  Heading, Text, VStack, <PERSON><PERSON><PERSON><PERSON>, Spinner
} from '@chakra-ui/react';
import { useToast } from '@chakra-ui/react';

// Import components
import UploadForm from './components/UploadForm';
import ResultTabs from './components/ResultTabs';

// Import API functions
import { submitPaper, checkStatus, getResult, deleteResult } from './api';

const LifeScienceResearch = () => {
  const [file, setFile] = useState(null);
  const [targetAudience, setTargetAudience] = useState('researchers');
  const [summaryDepth, setSummaryDepth] = useState('detailed');
  const [taskId, setTaskId] = useState(null);
  const [status, setStatus] = useState(null);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pollingInterval, setPollingInterval] = useState(null);
  const toast = useToast();

  // Clean up polling on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) clearInterval(pollingInterval);
    };
  }, [pollingInterval]);

  const handleFileChange = (e) => {
    if (e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleSubmit = async () => {
    if (!file) {
      setError('Please select a PDF file');
      return;
    }

    if (!file.name.toLowerCase().endsWith('.pdf')) {
      setError('Only PDF files are supported');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const data = await submitPaper(file, targetAudience, summaryDepth);
      setTaskId(data.task_id);
      setStatus(data.status);
      
      // Start polling for status
      const interval = setInterval(() => handleCheckStatus(data.task_id), 5000);
      setPollingInterval(interval);
      
      toast({
        title: 'Paper submitted',
        description: 'Your paper is being processed',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (err) {
      setError(err.message);
      toast({
        title: 'Submission failed',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCheckStatus = async (id) => {
    try {
      const data = await checkStatus(id);
      setStatus(data.status);
      
      if (data.status === 'completed') {
        // Stop polling and fetch results
        clearInterval(pollingInterval);
        setPollingInterval(null);
        handleFetchResult(id);
        
        toast({
          title: 'Processing complete',
          description: 'Your paper has been successfully analyzed',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      } else if (data.status === 'failed') {
        // Stop polling on failure
        clearInterval(pollingInterval);
        setPollingInterval(null);
        setError(data.message);
        
        toast({
          title: 'Processing failed',
          description: data.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (err) {
      setError(err.message);
    }
  };

  const handleFetchResult = async (id) => {
    try {
      setLoading(true);
      const data = await getResult(id);
      setResult(data);
    } catch (err) {
      setError(err.message);
      toast({
        title: 'Failed to fetch results',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteResult = async () => {
    if (!taskId) return;
    
    try {
      setLoading(true);
      await deleteResult(taskId);
      
      setTaskId(null);
      setStatus(null);
      setResult(null);
      setFile(null);
      
      toast({
        title: 'Result deleted',
        description: 'The analysis has been deleted successfully',
        status: 'info',
        duration: 5000,
        isClosable: true,
      });
    } catch (err) {
      setError(err.message);
      toast({
        title: 'Deletion failed',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Heading as="h1" size="xl">Life Science Research Paper Analyzer</Heading>
        <Text fontSize="lg">
          Upload a scientific research paper to generate audience-specific insights and summaries.
        </Text>

        {error && (
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            {error}
          </Alert>
        )}

        {!result && (
          <UploadForm
            file={file}
            targetAudience={targetAudience}
            summaryDepth={summaryDepth}
            loading={loading}
            status={status}
            onFileChange={handleFileChange}
            onTargetAudienceChange={(e) => setTargetAudience(e.target.value)}
            onSummaryDepthChange={(e) => setSummaryDepth(e.target.value)}
            onSubmit={handleSubmit}
          />
        )}

        {status === 'processing' && (
          <Box p={6} borderRadius="md" borderWidth="1px" textAlign="center">
            <VStack spacing={4}>
              <Spinner size="xl" />
              <Text>Processing your research paper. This may take several minutes...</Text>
            </VStack>
          </Box>
        )}

        {result && (
          <>
            <HStack justify="space-between" mb={4}>
              <Heading size="md">Analysis Results</Heading>
              <Button colorScheme="red" size="sm" onClick={handleDeleteResult}>
                Delete Results
              </Button>
            </HStack>
            <ResultTabs result={result} />
          </>
        )}
      </VStack>
    </Container>
  );
};

export default LifeScienceResearch;
