
import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Upload, FileText, BarChart3, Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { submitPaper, checkStatus, getResult, deleteResult } from "./api";

// Define types for our state
interface ResultType {
  executive_summaries: Array<{
    section: string;
    executive_summary: string;
    chunk_index: string;
  }>;
  key_findings: string;
  methodology_highlights: string;
  clinical_implications: string;
  research_gaps: string;
  processing_metadata: {
    file_path: string;
    document_metadata: {
      total_length: number;
      estimated_pages: number;
    };
    parsing_method: string;
    total_chunks: number;
    target_audience: string;
    summary_depth: string;
    processing_status: string;
    sections_found: string[];
  };
}

const LifeScienceResearch = () => {
  const [file, setFile] = useState<File | null>(null);
  const [audience, setAudience] = useState("researchers");
  const [summaryLength, setSummaryLength] = useState("detailed");
  const [isGenerating, setIsGenerating] = useState(false);
  const [results, setResults] = useState<ResultType | null>(null);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  // Clean up polling on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) clearInterval(pollingInterval);
    };
  }, [pollingInterval]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const triggerFileUpload = () => {
    document.getElementById('research-file')?.click();
  };

  const handleGenerate = async () => {
    if (!file || !audience || !summaryLength) {
      toast({
        title: "Missing information",
        description: "Please select a file, audience, and summary length",
        variant: "destructive"
      });
      return;
    }
    
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      toast({
        title: "Invalid file type",
        description: "Only PDF files are supported",
        variant: "destructive"
      });
      return;
    }
    
    setIsGenerating(true);
    setError(null);
    setResults(null);
    
    try {
      const data = await submitPaper(file, audience, summaryLength);
      setTaskId(data.task_id);
      setStatus(data.status);
      
      // Start polling for status
      const interval = setInterval(() => handleCheckStatus(data.task_id), 5000);
      setPollingInterval(interval);
      
      toast({
        title: "Paper submitted",
        description: "Your paper is being processed"
      });
    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Submission failed",
        description: err.message,
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCheckStatus = async (id: string) => {
    try {
      const data = await checkStatus(id);
      setStatus(data.status);
      
      if (data.status === 'completed') {
        // Stop polling and fetch results
        if (pollingInterval) clearInterval(pollingInterval);
        setPollingInterval(null);
        handleFetchResult(id);
        
        toast({
          title: "Processing complete",
          description: "Your paper has been successfully analyzed"
        });
      } else if (data.status === 'failed') {
        // Stop polling on failure
        if (pollingInterval) clearInterval(pollingInterval);
        setPollingInterval(null);
        setError(data.message);
        
        toast({
          title: "Processing failed",
          description: data.message,
          variant: "destructive"
        });
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleFetchResult = async (id: string) => {
    try {
      setIsGenerating(true);
      const data = await getResult(id);
      setResults(data);
    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Failed to fetch results",
        description: err.message,
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDeleteResult = async () => {
    if (!taskId) return;
    
    try {
      setIsGenerating(true);
      await deleteResult(taskId);
      
      setTaskId(null);
      setStatus(null);
      setResults(null);
      setFile(null);
      
      toast({
        title: "Result deleted",
        description: "The analysis has been deleted successfully"
      });
    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Deletion failed",
        description: err.message,
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Layout
      title="Life Science Research Summarization"
      description="Transform complex research papers into digestible summaries for different audiences."
      category="Pharma & Healthcare"
    >
      <div className="space-y-8">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}
        
        {/* Input Section */}
        {!results && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Upload className="h-5 w-5" />
                <span>Upload Research Document</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="research-file">Research Paper (PDF)</Label>
                <div 
                  onClick={triggerFileUpload}
                  className="mt-2 flex items-center justify-center w-full h-32 border-2 border-dashed border-slate-300 rounded-lg hover:border-slate-400 transition-colors cursor-pointer"
                >
                  <div className="text-center">
                    <FileText className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                    <div className="text-sm text-slate-600">
                      {file ? file.name : "Upload biology_research.pdf or similar"}
                    </div>
                    <input
                      id="research-file"
                      type="file"
                      accept=".pdf"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="audience">Target Audience</Label>
                  <Select value={audience} onValueChange={setAudience}>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="Select audience" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="researchers">Researchers</SelectItem>
                      <SelectItem value="clinicians">Clinicians</SelectItem>
                      <SelectItem value="regulatory">Regulatory</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="summary-length">Summary Length</Label>
                  <Select value={summaryLength} onValueChange={setSummaryLength}>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="Select length" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="brief">Brief</SelectItem>
                      <SelectItem value="detailed">Detailed</SelectItem>
                      <SelectItem value="comprehensive">Comprehensive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <Button 
                onClick={handleGenerate}
                disabled={!file || !audience || !summaryLength || isGenerating}
                className="w-full bg-slate-900 hover:bg-slate-800"
              >
                {isGenerating || status === 'processing' ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Processing...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4" />
                    <span>Generate Summary</span>
                  </div>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Processing Indicator */}
        {status === 'processing' && (
          <Card>
            <CardContent className="py-6">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Loader2 className="h-12 w-12 animate-spin text-slate-700" />
                <p className="text-slate-600">Processing your research paper. This may take several minutes...</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Results Section */}
        {results && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-semibold">Analysis Results</h3>
              <Button 
                variant="destructive" 
                size="sm" 
                onClick={handleDeleteResult}
                disabled={isGenerating}
              >
                Delete Results
              </Button>
            </div>
            
            <Accordion type="single" collapsible className="w-full space-y-4">
              {/* Executive Summaries */}
              <AccordionItem value="summaries" className="border border-slate-200 rounded-lg px-6">
                <AccordionTrigger className="text-lg font-semibold hover:no-underline">
                  Executive Summaries
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 pt-4">
                    {results.executive_summaries.map((summary, index) => (
                      <div key={index} className="border border-slate-200 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium text-slate-900 capitalize">{summary.section}</h4>
                          <span className="text-xs bg-slate-100 px-2 py-1 rounded-full">{summary.chunk_index}</span>
                        </div>
                        <p className="text-slate-700">{summary.executive_summary}</p>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              {/* Key Insights */}
              <AccordionItem value="insights" className="border border-slate-200 rounded-lg px-6">
                <AccordionTrigger className="text-lg font-semibold hover:no-underline">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5" />
                    <span>Key Insights</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-6 pt-4">
                    <div>
                      <h4 className="font-medium text-slate-900 mb-2">Key Findings</h4>
                      <p className="text-slate-700">{results.key_findings}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-slate-900 mb-2">Methodology Highlights</h4>
                      <p className="text-slate-700">{results.methodology_highlights}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-slate-900 mb-2">Clinical Implications</h4>
                      <p className="text-slate-700">{results.clinical_implications}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-slate-900 mb-2">Research Gaps</h4>
                      <p className="text-slate-700">{results.research_gaps}</p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              {/* Metadata */}
              <AccordionItem value="metadata" className="border border-slate-200 rounded-lg px-6">
                <AccordionTrigger className="text-lg font-semibold hover:no-underline">
                  Processing Metadata
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 pt-4">
                    <div>
                      <h4 className="font-medium text-slate-900 mb-2">File Information</h4>
                      <p className="text-slate-700">Path: {results.processing_metadata.file_path}</p>
                      <p className="text-slate-700">Total Length: {results.processing_metadata.document_metadata.total_length} characters</p>
                      <p className="text-slate-700">Estimated Pages: {results.processing_metadata.document_metadata.estimated_pages}</p>
                    </div>
                    
                    <div className="border-t border-slate-200 pt-4">
                      <h4 className="font-medium text-slate-900 mb-2">Processing Details</h4>
                      <p className="text-slate-700">Parsing Method: {results.processing_metadata.parsing_method}</p>
                      <p className="text-slate-700">Total Chunks: {results.processing_metadata.total_chunks}</p>
                      <p className="text-slate-700">Target Audience: {results.processing_metadata.target_audience}</p>
                      <p className="text-slate-700">Summary Depth: {results.processing_metadata.summary_depth}</p>
                      <p className="text-slate-700">Processing Status: {results.processing_metadata.processing_status}</p>
                    </div>
                    
                    <div className="border-t border-slate-200 pt-4">
                      <h4 className="font-medium text-slate-900 mb-2">Sections Found</h4>
                      <div className="flex flex-wrap gap-2">
                        {results.processing_metadata.sections_found.map((section, index) => (
                          <span key={index} className="bg-slate-100 text-slate-700 px-2 py-1 rounded-full text-xs">
                            {section}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default LifeScienceResearch;
