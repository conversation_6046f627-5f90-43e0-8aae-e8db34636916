import React from 'react';
import {
  Box, Heading, Text, VStack, Badge, 
  Card, CardHeader, CardBody, HStack
} from '@chakra-ui/react';

const SummarySection = ({ title, content, badge = null }) => {
  return (
    <Card>
      <CardHeader>
        <HStack justify="space-between">
          <Heading size="sm">{title}</Heading>
          {badge && <Badge colorScheme="blue">{badge}</Badge>}
        </HStack>
      </CardHeader>
      <CardBody>
        <Text>{content}</Text>
      </CardBody>
    </Card>
  );
};

export default SummarySection;