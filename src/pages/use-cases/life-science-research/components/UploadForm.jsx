import React from 'react';
import {
  Box, Button, FormControl, FormLabel, Select,
  Text, VStack, Card, CardHeader, CardBody, Heading
} from '@chakra-ui/react';
import { UploadIcon } from '@chakra-ui/icons';

const UploadForm = ({
  file,
  targetAudience,
  summaryDepth,
  loading,
  status,
  onFileChange,
  onTargetAudienceChange,
  onSummaryDepthChange,
  onSubmit
}) => {
  return (
    <Card>
      <CardHeader>
        <Heading size="md">Upload Research Paper</Heading>
      </CardHeader>
      <CardBody>
        <VStack spacing={6} align="stretch">
          <FormControl>
            <FormLabel>Upload PDF File</FormLabel>
            <input
              type="file"
              accept=".pdf"
              onChange={onFileChange}
              style={{ 
                border: '1px dashed gray', 
                padding: '20px',
                borderRadius: '5px',
                width: '100%' 
              }}
            />
            {file && <Text mt={2}>Selected: {file.name}</Text>}
          </FormControl>

          <FormControl>
            <FormLabel>Target Audience</FormLabel>
            <Select 
              value={targetAudience} 
              onChange={onTargetAudienceChange}
            >
              <option value="researchers">Researchers</option>
              <option value="clinicians">Clinicians</option>
              <option value="regulatory">Regulatory</option>
            </Select>
          </FormControl>

          <FormControl>
            <FormLabel>Summary Depth</FormLabel>
            <Select 
              value={summaryDepth} 
              onChange={onSummaryDepthChange}
            >
              <option value="brief">Brief</option>
              <option value="detailed">Detailed</option>
              <option value="comprehensive">Comprehensive</option>
            </Select>
          </FormControl>

          <Button 
            colorScheme="blue" 
            onClick={onSubmit} 
            isLoading={loading}
            leftIcon={<UploadIcon />}
            isDisabled={!file || status === 'processing'}
          >
            Submit Paper
          </Button>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default UploadForm;