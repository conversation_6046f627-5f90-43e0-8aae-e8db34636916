import React from 'react';
import {
  Box, Text, VS<PERSON>ck, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,
  Ta<PERSON>, <PERSON>b<PERSON>ist, TabPanels, Tab, TabPanel,
  Card, CardHeader, CardBody, Heading, HStack
} from '@chakra-ui/react';
import SummarySection from './SummarySection';

const ResultTabs = ({ result }) => {
  if (!result) return null;

  return (
    <Tabs variant="enclosed" colorScheme="blue">
      <TabList>
        <Tab>Executive Summaries</Tab>
        <Tab>Key Insights</Tab>
        <Tab>Metadata</Tab>
      </TabList>

      <TabPanels>
        <TabPanel>
          <VStack spacing={4} align="stretch">
            {result.executive_summaries.map((summary, index) => (
              <SummarySection
                key={index}
                title={summary.section}
                content={summary.executive_summary}
                badge={summary.chunk_index}
              />
            ))}
          </VStack>
        </TabPanel>

        <TabPanel>
          <VStack spacing={4} align="stretch">
            <SummarySection title="Key Findings" content={result.key_findings} />
            <SummarySection title="Methodology Highlights" content={result.methodology_highlights} />
            <SummarySection title="Clinical Implications" content={result.clinical_implications} />
            <SummarySection title="Research Gaps" content={result.research_gaps} />
            <SummarySection title="Future Directions" content={result.future_directions} />
            <SummarySection title="Comparative Analysis" content={result.comparative_analysis} />
          </VStack>
        </TabPanel>

        <TabPanel>
          <Card>
            <CardHeader>
              <Heading size="sm">Processing Metadata</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={3} align="stretch">
                <Box>
                  <Text fontWeight="bold">File Information</Text>
                  <Text>Path: {result.processing_metadata.file_path}</Text>
                  <Text>Total Length: {result.processing_metadata.document_metadata.total_length} characters</Text>
                  <Text>Estimated Pages: {result.processing_metadata.document_metadata.estimated_pages}</Text>
                </Box>
                
                <Divider />
                
                <Box>
                  <Text fontWeight="bold">Content Analysis</Text>
                  <Text>Has Tables: {result.processing_metadata.document_metadata.has_tables ? 'Yes' : 'No'}</Text>
                  <Text>Has Figures: {result.processing_metadata.document_metadata.has_figures ? 'Yes' : 'No'}</Text>
                  <Text>Has Equations: {result.processing_metadata.document_metadata.has_equations ? 'Yes' : 'No'}</Text>
                  <Text>Reference Count: {result.processing_metadata.document_metadata.reference_count}</Text>
                </Box>
                
                <Divider />
                
                <Box>
                  <Text fontWeight="bold">Processing Details</Text>
                  <Text>Parsing Method: {result.processing_metadata.parsing_method}</Text>
                  <Text>Total Chunks: {result.processing_metadata.total_chunks}</Text>
                  <Text>Target Audience: {result.processing_metadata.target_audience}</Text>
                  <Text>Summary Depth: {result.processing_metadata.summary_depth}</Text>
                  <Text>Processing Status: {result.processing_metadata.processing_status}</Text>
                </Box>
                
                <Divider />
                
                <Box>
                  <Text fontWeight="bold">Sections Found</Text>
                  <HStack flexWrap="wrap" spacing={2} mt={1}>
                    {result.processing_metadata.sections_found.map((section, index) => (
                      <Badge key={index} colorScheme="green" m={1}>
                        {section}
                      </Badge>
                    ))}
                  </HStack>
                </Box>
              </VStack>
            </CardBody>
          </Card>
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};

export default ResultTabs;