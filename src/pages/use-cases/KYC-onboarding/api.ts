// API client for KYC Onboarding service

const API_BASE_URL = 'http://localhost:8007'; // Update this to match your backend server

export interface KycFormData {
  name: string;
  dob: string;
  father_name: string;
  ovd_type: string;
  address: string;
  contact: string;
  region: string;
  state: string;
  csm: string;
  asm: string;
}

export interface KycSessionResponse {
  success: boolean;
  session_id: string;
  data: KycFormData;
}

export interface DocumentUploadResponse {
  success: boolean;
  document_path: string;
  extracted_data: any;
}

export interface PhotoUploadResponse {
  success: boolean;
  photo_data: string;
}

export interface LivePhotoResponse {
  success: boolean;
  message: string;
}

export interface VerificationResponse {
  success: boolean;
  verification_result: {
    status: string;
    reasoning: string;
    face_match_score: number;
    timestamp: string;
  };
}

// Submit KYC form data
export const submitKycForm = async (formData: KycFormData): Promise<KycSessionResponse> => {
  try {
    const form = new FormData();
    
    // Add all form fields
    Object.entries(formData).forEach(([key, value]) => {
      form.append(key, value);
    });
    
    const response = await fetch(`${API_BASE_URL}/api/v1/kyc/form`, {
      method: 'POST',
      body: form,
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to submit KYC form');
    }
    
    return await response.json();
  } catch (error) {
    console.error('API error:', error);
    throw error;
  }
};

// Upload document
export const uploadDocument = async (sessionId: string, document: File): Promise<DocumentUploadResponse> => {
  try {
    const form = new FormData();
    form.append('session_id', sessionId);
    form.append('document', document);
    
    const response = await fetch(`${API_BASE_URL}/api/v1/kyc/document/upload`, {
      method: 'POST',
      body: form,
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to upload document');
    }
    
    return await response.json();
  } catch (error) {
    console.error('API error:', error);
    throw error;
  }
};

// Upload ID photo
export const uploadIdPhoto = async (sessionId: string, photo: File): Promise<PhotoUploadResponse> => {
  try {
    const form = new FormData();
    form.append('session_id', sessionId);
    form.append('photo', photo);
    
    const response = await fetch(`${API_BASE_URL}/api/v1/kyc/photo/id`, {
      method: 'POST',
      body: form,
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to upload ID photo');
    }
    
    return await response.json();
  } catch (error) {
    console.error('API error:', error);
    throw error;
  }
};

// Submit live photo
export const submitLivePhoto = async (sessionId: string, photoData: string): Promise<LivePhotoResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/kyc/photo/live`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_id: sessionId,
        photo_data: photoData,
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to submit live photo');
    }
    
    return await response.json();
  } catch (error: any) {
    console.error('API error:', error);
    // Properly format the error message
    const errorMessage = error.message || String(error);
    throw new Error(`Failed to submit live photo: ${errorMessage}`);
  }
};

// Complete verification
export const completeVerification = async (sessionId: string): Promise<VerificationResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/kyc/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_id: sessionId,
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Verification error response:', errorData);
      // Extract the detail field from the error response
      if (errorData.detail) {
        if (Array.isArray(errorData.detail)) {
          throw new Error(errorData.detail[0].msg || 'Validation error');
        } else {
          throw new Error(errorData.detail);
        }
      } else {
        throw new Error('Failed to complete verification');
      }
    }
    
    return await response.json();
  } catch (error: any) {
    console.error('API error:', error);
    // Properly format the error message
    const errorMessage = error.message || 'Unknown error';
    throw new Error(`Failed to complete verification: ${errorMessage}`);
  }
};

// Clean up session
export const cleanupSession = async (sessionId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/session/${sessionId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to clean up session');
    }
    
    return await response.json();
  } catch (error) {
    console.error('API error:', error);
    throw error;
  }
};

// Debug session data
export const debugSession = async (sessionId: string): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/debug/session/${sessionId}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      if (errorData.detail) {
        throw new Error(errorData.detail);
      } else {
        throw new Error('Failed to debug session');
      }
    }
    
    return await response.json();
  } catch (error: any) {
    console.error('API error:', error);
    throw error;
  }
};




