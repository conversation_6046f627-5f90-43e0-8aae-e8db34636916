
import { useState, useRef } from "react";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Upload, Camera, Shield, AlertCircle } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  submitKycForm,
  uploadDocument,
  uploadIdPhoto,
  submitLivePhoto,
  completeVerification,
  cleanupSession,
  debugSession,
  KycFormData
} from "./api";

const KYCOnboarding = () => {
  // Form data state
  const [kycData, setKycData] = useState<KycFormData>({
    name: "",
    dob: "",
    father_name: "",
    ovd_type: "Aadhaar Card",
    address: "",
    contact: "",
    region: "North",
    state: "Delhi",
    csm: "Tarun Jain",
    asm: "Gurram Prudhvi"
  });

  // File upload states
  const [kycDocument, setKycDocument] = useState<File | null>(null);
  const [kycPhoto, setKycPhoto] = useState<File | null>(null);
  const [livePhotoBase64, setLivePhotoBase64] = useState<string | null>(null);
  
  // Processing states
  const [isProcessingKyc, setIsProcessingKyc] = useState(false);
  const [currentStep, setCurrentStep] = useState<string>("form");
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  
  // Results
  const [kycResult, setKycResult] = useState<any>(null);
  const [extractedData, setExtractedData] = useState<any>(null);
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const photoInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setKycData(prev => ({ ...prev, [id]: value }));
  };
  
  // Handle select changes
  const handleSelectChange = (id: string, value: string) => {
    setKycData(prev => ({ ...prev, [id]: value }));
  };
  
  // Handle document file upload
  const handleDocumentUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setKycDocument(e.target.files[0]);
    }
  };
  
  // Handle ID photo upload
  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setKycPhoto(e.target.files[0]);
    }
  };
  
  // Trigger file input click
  const triggerFileInput = (inputRef: React.RefObject<HTMLInputElement>) => {
    inputRef.current?.click();
  };
  
  // Start webcam
  const startWebcam = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      setError("Failed to access webcam. Please ensure you have granted camera permissions.");
    }
  };
  
  // Capture photo from webcam
  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      
      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      // Draw video frame to canvas
      context?.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      // Convert to base64
      const photoData = canvas.toDataURL('image/jpeg').split(',')[1];
      setLivePhotoBase64(photoData);
      
      // Stop webcam
      const stream = video.srcObject as MediaStream;
      stream?.getTracks().forEach(track => track.stop());
      video.srcObject = null;
    }
  };
  
  // Submit form and start KYC process
  const handleFormSubmit = async () => {
    try {
      setError(null);
      setIsProcessingKyc(true);
      setProgress(10);
      
      // Submit form data
      const formResponse = await submitKycForm(kycData);
      setSessionId(formResponse.session_id);
      console.log("Session created:", formResponse.session_id);
      setProgress(30);
      
      // Upload document
      if (kycDocument && formResponse.session_id) {
        const documentResponse = await uploadDocument(formResponse.session_id, kycDocument);
        setExtractedData(documentResponse.extracted_data);
        setProgress(50);
      }
      
      // Upload ID photo
      if (kycPhoto && formResponse.session_id) {
        await uploadIdPhoto(formResponse.session_id, kycPhoto);
        setProgress(70);
      }
      
      // Move to photo capture step
      setCurrentStep("photo");
      setIsProcessingKyc(false);
      setProgress(0);
      
      // Start webcam
      startWebcam();
      
    } catch (err: any) {
      setError(err.message || "An error occurred during KYC submission");
      setIsProcessingKyc(false);
      setProgress(0);
    }
  };
  
  // Complete verification process
  const handleCompleteVerification = async () => {
    if (!sessionId || !livePhotoBase64) {
      setError("Missing session ID or live photo");
      return;
    }
    
    try {
      setError(null);
      setIsProcessingKyc(true);
      setProgress(10);
      
      console.log("Submitting live photo for session:", sessionId);
      console.log("Live photo base64 length:", livePhotoBase64.length);
      
      // Submit live photo
      try {
        const livePhotoResponse = await submitLivePhoto(sessionId, livePhotoBase64);
        console.log("Live photo response:", livePhotoResponse);
        setProgress(50);
      } catch (e: any) {
        if (e.message && e.message.includes("Session not found")) {
          throw new Error("Your session has expired. Please start over.");
        }
        throw e;
      }
      
      console.log("Completing verification for session:", sessionId);
      
      // Complete verification
      try {
        const verificationResponse = await completeVerification(sessionId);
        console.log("Verification response:", verificationResponse);
        setProgress(90);
        
        // Set result
        setKycResult({
          status: verificationResponse.verification_result.status,
          confidence: verificationResponse.verification_result.face_match_score,
          message: verificationResponse.verification_result.reasoning
        });
        
        // Move to result step
        setCurrentStep("result");
        setIsProcessingKyc(false);
        setProgress(100);
        
        // Clean up session after 5 minutes
        setTimeout(() => {
          if (sessionId) {
            cleanupSession(sessionId).catch(console.error);
          }
        }, 5 * 60 * 1000);
      } catch (e: any) {
        if (e.message && e.message.includes("Session not found")) {
          throw new Error("Your session has expired. Please start over.");
        }
        throw e;
      }
      
    } catch (err: any) {
      console.error("Verification error:", err);
      setError(err.message || "An error occurred during verification");
      setIsProcessingKyc(false);
      setProgress(0);
      
      // If session expired, go back to form
      if (err.message && err.message.includes("expired")) {
        setCurrentStep("form");
      }
    }
  };
  
  // Reset the form
  const handleReset = () => {
    setKycData({
      name: "",
      dob: "",
      father_name: "",
      ovd_type: "Aadhaar Card",
      address: "",
      contact: "",
      region: "North",
      state: "Delhi",
      csm: "Tarun Jain",
      asm: "Gurram Prudhvi"
    });
    setKycDocument(null);
    setKycPhoto(null);
    setLivePhotoBase64(null);
    setSessionId(null);
    setError(null);
    setKycResult(null);
    setExtractedData(null);
    setCurrentStep("form");
    setProgress(0);
    
    // Stop webcam if active
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream?.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
  };

  return (
    <Layout
      title="KYC Onboarding & Face Matching"
      description="Streamline customer verification with AI-powered identity validation and tampering detection."
      category="Financial Banking"
    >
      <Tabs defaultValue="kyc" className="w-full">
        <TabsList className="w-full mb-8">
          <TabsTrigger value="kyc" className="w-full">KYC Onboarding</TabsTrigger>
        </TabsList>
        
        <TabsContent value="kyc" className="space-y-8">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {isProcessingKyc && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}
          
          {currentStep === "form" && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Personal Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        value={kycData.name}
                        onChange={handleInputChange}
                        placeholder="Enter full name"
                        className="mt-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="dob">Date of Birth</Label>
                      <Input
                        id="dob"
                        type="date"
                        value={kycData.dob}
                        onChange={handleInputChange}
                        className="mt-2"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="father_name">Father's Name</Label>
                      <Input
                        id="father_name"
                        value={kycData.father_name}
                        onChange={handleInputChange}
                        placeholder="Enter father's name"
                        className="mt-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="ovd_type">Type of OVD</Label>
                      <Select 
                        value={kycData.ovd_type} 
                        onValueChange={(value) => handleSelectChange("ovd_type", value)}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Select document type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Aadhaar Card">Aadhaar Card</SelectItem>
                          <SelectItem value="Voter ID">Voter ID</SelectItem>
                          <SelectItem value="PAN Card">PAN Card</SelectItem>
                          <SelectItem value="Driving License">Driving License</SelectItem>
                          <SelectItem value="Passport">Passport</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={kycData.address}
                      onChange={handleInputChange}
                      placeholder="Enter complete address"
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="contact">Contact Number</Label>
                    <Input
                      id="contact"
                      value={kycData.contact}
                      onChange={handleInputChange}
                      placeholder="Enter contact number"
                      className="mt-2"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="region">Region</Label>
                      <Select 
                        value={kycData.region} 
                        onValueChange={(value) => handleSelectChange("region", value)}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Select region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="North">North</SelectItem>
                          <SelectItem value="South">South</SelectItem>
                          <SelectItem value="East">East</SelectItem>
                          <SelectItem value="West">West</SelectItem>
                          <SelectItem value="Central">Central</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="state">State</Label>
                      <Select 
                        value={kycData.state} 
                        onValueChange={(value) => handleSelectChange("state", value)}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Select state" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Delhi">Delhi</SelectItem>
                          <SelectItem value="Maharashtra">Maharashtra</SelectItem>
                          <SelectItem value="Tamil Nadu">Tamil Nadu</SelectItem>
                          <SelectItem value="West Bengal">West Bengal</SelectItem>
                          <SelectItem value="Karnataka">Karnataka</SelectItem>
                          <SelectItem value="Uttar Pradesh">Uttar Pradesh</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="csm">CSM Name</Label>
                      <Input
                        id="csm"
                        value={kycData.csm}
                        onChange={handleInputChange}
                        placeholder="Enter CSM name"
                        className="mt-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="asm">ASM Name</Label>
                      <Input
                        id="asm"
                        value={kycData.asm}
                        onChange={handleInputChange}
                        placeholder="Enter ASM name"
                        className="mt-2"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Document Upload</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Upload ID Document</Label>
                    <div 
                      className="mt-2 flex items-center justify-center w-full h-32 border-2 border-dashed border-slate-300 rounded-lg hover:border-slate-400 transition-colors cursor-pointer"
                      onClick={() => triggerFileInput(fileInputRef)}
                    >
                      <div className="text-center">
                        <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                        <div className="text-sm text-slate-600">
                          {kycDocument ? kycDocument.name : "Upload passport, license, or ID card"}
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={handleDocumentUpload}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <Label>Upload ID Photo</Label>
                    <div 
                      className="mt-2 flex items-center justify-center w-full h-32 border-2 border-dashed border-slate-300 rounded-lg hover:border-slate-400 transition-colors cursor-pointer"
                      onClick={() => triggerFileInput(photoInputRef)}
                    >
                      <div className="text-center">
                        <Camera className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                        <div className="text-sm text-slate-600">
                          {kycPhoto ? kycPhoto.name : "Upload photo from ID document"}
                        </div>
                        <input
                          ref={photoInputRef}
                          type="file"
                          accept=".jpg,.jpeg,.png"
                          onChange={handlePhotoUpload}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Button 
                onClick={handleFormSubmit}
                disabled={
                  !kycData.name || 
                  !kycData.dob || 
                  !kycData.father_name || 
                  !kycData.address || 
                  !kycData.contact || 
                  !kycDocument || 
                  !kycPhoto || 
                  isProcessingKyc
                }
                className="w-full bg-slate-900 hover:bg-slate-800"
              >
                {isProcessingKyc ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                    <span>Processing form submission...</span>
                  </div>
                ) : (
                  "Continue to Photo Capture"
                )}
              </Button>
            </>
          )}
          
          {currentStep === "photo" && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Live Photo Capture</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col items-center space-y-4">
                    {!livePhotoBase64 ? (
                      <>
                        <div className="relative w-full max-w-md h-64 bg-slate-100 rounded-lg overflow-hidden">
                          <video 
                            ref={videoRef} 
                            autoPlay 
                            playsInline 
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <Button onClick={capturePhoto}>
                          Capture Photo
                        </Button>
                      </>
                    ) : (
                      <>
                        <div className="w-full max-w-md h-64 bg-slate-100 rounded-lg overflow-hidden">
                          <img 
                            src={`data:image/jpeg;base64,${livePhotoBase64}`} 
                            alt="Captured" 
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex space-x-4">
                          <Button variant="outline" onClick={() => {
                            setLivePhotoBase64(null);
                            startWebcam();
                          }}>
                            Retake Photo
                          </Button>
                          <Button onClick={handleCompleteVerification}>
                            Complete Verification
                          </Button>
                        </div>
                      </>
                    )}
                    
                    {/* Hidden canvas for capturing photos */}
                    <canvas ref={canvasRef} className="hidden" />
                  </div>
                </CardContent>
              </Card>
              
              <Button 
                variant="outline" 
                onClick={() => setCurrentStep("form")}
                className="w-full"
              >
                Back to Form
              </Button>
            </>
          )}
          
          {currentStep === "result" && kycResult && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>KYC Verification Result</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Status:</span>
                    <span className={`font-semibold capitalize ${
                      kycResult.status === "Approved" ? "text-green-600" : 
                      kycResult.status === "Rejected" ? "text-red-600" : "text-orange-500"
                    }`}>
                      {kycResult.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Face Match Confidence:</span>
                    <span className="font-semibold">{kycResult.confidence}%</span>
                  </div>
                  <div className="mt-4">
                    <h3 className="font-medium mb-2">Verification Details:</h3>
                    <div className="p-4 bg-slate-50 rounded-lg text-slate-700 whitespace-pre-line">
                      {kycResult.message}
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Button 
                onClick={handleReset}
                className="w-full"
              >
                Start New Verification
              </Button>
            </>
          )}
        </TabsContent>
      </Tabs>
    </Layout>
  );
};

export default KYCOnboarding;
