// Base URL for Medical Report Analysis API
const API_BASE_URL = "/api/medical-report";

// Types for API responses
export interface DocumentUploadResponse {
  success: boolean;
  message: string;
  filename?: string;
  document_id?: string;
  extracted_text?: string;
  comprehensive_analysis?: string;
  key_findings?: string;
  abnormal_findings?: string;
}

export interface FollowUpQuestionResponse {
  answer: string;
  question: string;
  timestamp: string;
  disclaimer: string;
}

export interface AnalysisResult {
  extracted_text: string;
  comprehensive_analysis: string;
  key_findings: string;
  abnormal_findings: string;
  filename: string;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

// Analyze medical report document
export const analyzeMedicalReport = async (file: File): Promise<DocumentUploadResponse> => {
  try {
    const formData = new FormData();
    formData.append("file", file);

    console.log("Analyzing medical report:", {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });

    const response = await fetch(`${API_BASE_URL}/analyze-report`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to analyze medical report");
    }

    return await response.json();
  } catch (error) {
    console.error("Medical report analysis error:", error);
    throw error;
  }
};

// Ask follow-up question about the analyzed report
export const askFollowUpQuestion = async (
  question: string,
  documentId: string
): Promise<FollowUpQuestionResponse> => {
  try {
    const requestBody = {
      question,
      document_id: documentId,
    };

    console.log("Asking follow-up question:", requestBody);

    const response = await fetch(`${API_BASE_URL}/follow-up-question`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to get follow-up answer");
    }

    return await response.json();
  } catch (error) {
    console.error("Follow-up question error:", error);
    throw error;
  }
};

// Clear analyzed document
export const clearAnalyzedDocument = async (documentId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/clear-document?document_id=${documentId}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || "Failed to clear document");
    }

    return await response.json();
  } catch (error) {
    console.error("Clear document error:", error);
    throw error;
  }
};
