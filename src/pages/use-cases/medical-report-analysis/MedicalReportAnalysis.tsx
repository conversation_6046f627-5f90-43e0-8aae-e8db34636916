
import { useState, useRef } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Upload, FileText, MessageSquare, Bot, User } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// API Configuration
const API_BASE_URL = "http://localhost:8003";

// Types
interface DocumentUploadResponse {
  success: boolean;
  message: string;
  filename?: string;
  document_id?: string;
  extracted_text?: string;
  comprehensive_analysis?: string;
  key_findings?: string;
  abnormal_findings?: string;
}

interface FollowUpQuestionResponse {
  answer: string;
  question: string;
  timestamp: string;
  disclaimer: string;
}

interface AnalysisResult {
  extracted_text: string;
  comprehensive_analysis: string;
  key_findings: string;
  abnormal_findings: string;
  filename: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const MedicalReportAnalysis = () => {
  const [file, setFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [followUpQuestion, setFollowUpQuestion] = useState("");
  const [isAskingQuestion, setIsAskingQuestion] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleAnalyze = async () => {
    if (!file) return;

    setIsProcessing(true);
    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await fetch(`${API_BASE_URL}/upload-report`, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const data: DocumentUploadResponse = await response.json();

      if (data.success && data.extracted_text && data.comprehensive_analysis && data.key_findings && data.abnormal_findings) {
        setResult({
          extracted_text: data.extracted_text,
          comprehensive_analysis: data.comprehensive_analysis,
          key_findings: data.key_findings,
          abnormal_findings: data.abnormal_findings,
          filename: data.filename || file.name
        });

        toast({
          title: "Report analyzed successfully",
          description: `${data.filename} has been processed and analyzed!`,
        });
      } else {
        throw new Error(data.message || "Failed to analyze report");
      }
    } catch (error) {
      console.error("Analysis error:", error);
      toast({
        title: "Analysis failed",
        description: error instanceof Error ? error.message : "Failed to analyze medical report",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFollowUp = async () => {
    if (!followUpQuestion.trim() || !result) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: followUpQuestion.trim(),
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setFollowUpQuestion("");
    setIsAskingQuestion(true);

    try {
      const requestBody = {
        question: userMessage.content,
        extracted_text: result.extracted_text
      };

      const response = await fetch(`${API_BASE_URL}/ask-question`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Request failed: ${response.statusText}`);
      }

      const data: FollowUpQuestionResponse = await response.json();

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: data.answer,
        timestamp: new Date()
      };

      setChatMessages(prev => [...prev, assistantMessage]);

    } catch (error) {
      console.error("Question processing error:", error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `Error: ${error instanceof Error ? error.message : "Failed to process question"}`,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Question failed",
        description: error instanceof Error ? error.message : "Failed to process question",
        variant: "destructive",
      });
    } finally {
      setIsAskingQuestion(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleFollowUp();
    }
  };

  return (
    <Layout
      title="Medical Report Analysis"
      description="Automated medical document analysis with intelligent insights extraction."
      category="Pharma & Healthcare"
    >
      <div className="space-y-8">
        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload Medical Report</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Medical Report File</Label>
              <div
                className="mt-2 flex items-center justify-center w-full h-32 border-2 border-dashed border-slate-300 rounded-lg hover:border-slate-400 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
              >
                <div className="text-center">
                  <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                  <div className="text-sm text-slate-600">
                    {file ? file.name : "Upload medical report (.pdf, .jpg, .png, .tiff, .bmp, .tif)"}
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".pdf,.jpg,.png,.jpeg,.tiff,.bmp,.tif"
                    onChange={(e) => setFile(e.target.files?.[0] || null)}
                    className="hidden"
                  />
                </div>
              </div>
            </div>

            <Button
              onClick={handleAnalyze}
              disabled={!file || isProcessing}
              className="w-full bg-emerald-500 hover:bg-emerald-600"
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Analyzing medical report...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Analyze Report</span>
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        {result && (
          <div className="space-y-6">
            {/* Extracted Text */}
            <Card>
              <CardHeader>
                <CardTitle>Extracted Text</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-slate-50 p-4 rounded-lg max-h-[200px] overflow-y-auto">
                  <div className="whitespace-pre-wrap text-sm text-slate-700 leading-relaxed">
                    {result.extracted_text}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Comprehensive Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Comprehensive Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-slate-200">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed text-slate-700">
                    {result.comprehensive_analysis}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Key Findings */}
            <Card>
              <CardHeader>
                <CardTitle>Key Findings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-slate-50 p-4 rounded-lg border border-slate-200">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed text-slate-700">
                    {result.key_findings}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Abnormal Findings */}
            <Card>
              <CardHeader>
                <CardTitle>Abnormal Findings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-slate-50 p-4 rounded-lg border border-slate-200">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed text-slate-700">
                    {result.abnormal_findings}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Follow-up Questions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5" />
                  <span>Ask Follow-Up Question</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Chat Messages */}
                {chatMessages.length > 0 && (
                  <div className="max-h-[300px] overflow-y-auto space-y-3 p-3 bg-slate-50 rounded-lg">
                    {chatMessages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[85%] rounded-lg p-3 shadow-sm ${
                            message.type === 'user'
                              ? 'bg-emerald-500 text-white'
                              : 'bg-white border border-slate-200 text-slate-800'
                          }`}
                        >
                          <div className="flex items-start space-x-2">
                            {message.type === 'user' ? (
                              <User className="h-4 w-4 mt-0.5 flex-shrink-0" />
                            ) : (
                              <Bot className="h-4 w-4 mt-0.5 flex-shrink-0 text-slate-600" />
                            )}
                            <div className="flex-1">
                              <div className="whitespace-pre-wrap text-sm leading-relaxed">
                                {message.content}
                              </div>
                              <div className="mt-1 text-xs opacity-50">
                                {message.timestamp.toLocaleTimeString()}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div>
                  <Label htmlFor="followup">Follow-Up Question</Label>
                  <Textarea
                    id="followup"
                    value={followUpQuestion}
                    onChange={(e) => setFollowUpQuestion(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Ask a specific question about the medical report... (Press Enter to send)"
                    className="mt-2 min-h-[100px]"
                    disabled={isAskingQuestion}
                  />
                </div>
                <Button
                  onClick={handleFollowUp}
                  disabled={!followUpQuestion.trim() || isAskingQuestion}
                  className="bg-emerald-500 hover:bg-emerald-600"
                >
                  {isAskingQuestion ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                      <span>Processing...</span>
                    </div>
                  ) : (
                    "Ask Question"
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Disclaimer */}
            <Card>
              <CardContent className="pt-6">
                <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
                  <p className="text-sm text-slate-600">
                    <strong>Disclaimer:</strong> This tool is for educational purposes only. Always consult healthcare professionals for medical advice.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default MedicalReportAnalysis;
