import { useState, useRef } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, FileText, MessageSquare, Bot, User } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Import API functions and types
import {
  analyzeMedicalReport,
  askFollowUpQuestion,
  clearAnalyzedDocument,
  type AnalysisResult,
  type ChatMessage
} from "./api";

const MedicalReportAnalysis = () => {
  const [file, setFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [documentId, setDocumentId] = useState<string>("");
  const [followUpQuestion, setFollowUpQuestion] = useState("");
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleAnalyze = async () => {
    if (!file) return;

    setIsProcessing(true);

    try {
      const data = await analyzeMedicalReport(file);

      if (data.success && data.extracted_text && data.comprehensive_analysis) {
        const analysisResult: AnalysisResult = {
          extracted_text: data.extracted_text,
          comprehensive_analysis: data.comprehensive_analysis,
          key_findings: data.key_findings || "",
          abnormal_findings: data.abnormal_findings || "",
          filename: data.filename || file.name,
        };

        setResult(analysisResult);
        setDocumentId(data.document_id || "");
        setChatMessages([]); // Clear previous chat

        toast({
          title: "Analysis complete",
          description: "Medical report has been analyzed successfully",
        });
      } else {
        throw new Error(data.message || "Analysis failed");
      }
    } catch (error) {
      console.error('Analysis error:', error);
      toast({
        title: "Analysis failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFollowUp = async () => {
    if (!followUpQuestion.trim() || !result) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: followUpQuestion,
      timestamp: new Date(),
    };

    setChatMessages(prev => [...prev, userMessage]);
    setFollowUpQuestion("");
    setIsProcessing(true);

    try {
      const response = await askFollowUpQuestion(followUpQuestion, documentId);

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.answer,
        timestamp: new Date(),
      };

      setChatMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Follow-up error:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `Error: ${error instanceof Error ? error.message : 'Failed to get response'}`,
        timestamp: new Date(),
      };
      setChatMessages(prev => [...prev, errorMessage]);
      
      toast({
        title: "Error",
        description: "Failed to get follow-up answer",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const clearAnalysis = async () => {
    if (documentId) {
      try {
        await clearAnalyzedDocument(documentId);
      } catch (error) {
        console.error('Clear document error:', error);
      }
    }

    setFile(null);
    setResult(null);
    setDocumentId("");
    setChatMessages([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleFollowUp();
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Medical Report Analysis</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Upload medical reports for comprehensive AI-powered analysis. Get detailed insights, key findings, and ask follow-up questions about your medical documents.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Upload and Analysis */}
          <div className="space-y-6">
            {/* File Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Upload Medical Report
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="file-upload">Select Medical Report (PDF, DOC, TXT)</Label>
                  <input
                    ref={fileInputRef}
                    type="file"
                    id="file-upload"
                    accept=".pdf,.doc,.docx,.txt"
                    onChange={(e) => {
                      const selectedFile = e.target.files?.[0];
                      if (selectedFile) setFile(selectedFile);
                    }}
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isProcessing}
                    className="w-full mt-2"
                    variant="outline"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                  </Button>
                </div>

                {file && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">{file.name}</span>
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleAnalyze}
                  disabled={!file || isProcessing}
                  className="w-full"
                >
                  {isProcessing ? "Analyzing..." : "Analyze Report"}
                </Button>

                {result && (
                  <Button
                    onClick={clearAnalysis}
                    variant="outline"
                    className="w-full"
                  >
                    Clear Analysis
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Analysis Results */}
            {result && (
              <Card>
                <CardHeader>
                  <CardTitle>Analysis Results</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Comprehensive Analysis</h4>
                    <div className="p-3 bg-gray-50 border rounded-lg text-sm text-gray-700 max-h-40 overflow-y-auto">
                      {result.comprehensive_analysis}
                    </div>
                  </div>

                  {result.key_findings && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Key Findings</h4>
                      <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-sm text-green-800">
                        {result.key_findings}
                      </div>
                    </div>
                  )}

                  {result.abnormal_findings && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Abnormal Findings</h4>
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-800">
                        {result.abnormal_findings}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Follow-up Questions */}
          <div className="space-y-6">
            {result && (
              <Card className="h-[600px] flex flex-col">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Follow-up Questions
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col p-0">
                  {/* Chat Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {chatMessages.length === 0 && (
                      <div className="text-center text-gray-500 py-8">
                        <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>Ask questions about the analyzed medical report</p>
                      </div>
                    )}
                    
                    {chatMessages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex gap-3 ${
                          message.type === 'user' ? 'justify-end' : 'justify-start'
                        }`}
                      >
                        <div
                          className={`flex gap-3 max-w-[80%] ${
                            message.type === 'user' ? 'flex-row-reverse' : 'flex-row'
                          }`}
                        >
                          <div className="flex-shrink-0">
                            {message.type === 'user' ? (
                              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <User className="h-4 w-4 text-white" />
                              </div>
                            ) : (
                              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <Bot className="h-4 w-4 text-white" />
                              </div>
                            )}
                          </div>
                          <div
                            className={`rounded-lg p-3 ${
                              message.type === 'user'
                                ? 'bg-blue-500 text-white'
                                : 'bg-white border border-gray-200 text-gray-800'
                            }`}
                          >
                            <div className="whitespace-pre-wrap">{message.content}</div>
                            <div className="mt-1 text-xs opacity-70">
                              {message.timestamp.toLocaleTimeString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Input */}
                  <div className="border-t p-4">
                    <div className="flex gap-2">
                      <Textarea
                        value={followUpQuestion}
                        onChange={(e) => setFollowUpQuestion(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Ask a question about the medical report..."
                        className="flex-1 min-h-[60px] resize-none"
                        disabled={isProcessing}
                      />
                      <Button
                        onClick={handleFollowUp}
                        disabled={isProcessing || !followUpQuestion.trim()}
                        className="self-end"
                      >
                        Send
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {!result && (
              <Card>
                <CardContent className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Report Analyzed</h3>
                  <p className="text-gray-600">
                    Upload a medical report to get started with AI-powered analysis and insights.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default MedicalReportAnalysis;
