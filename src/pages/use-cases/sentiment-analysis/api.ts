import axios from 'axios';

// Define the API base URL
const API_BASE_URL = 'https://sentiment.demo.aiplanet.com'; // Updated to deployed backend

// Define types based on the API response
export interface SentimentItem {
  speaker: string;
  text: string;
  sentiment: string;
  start: number | null;
  end: number | null;
}

export interface CallSummary {
  summary: string;
  topic: string;
  product: string;
  resolved: string;
  callback: string;
  politeness: string;
  customer_sentiment: string;
  key_points: string[];
  action_items: string[];
}

export interface AgentProfile {
  id: string;
  name: string;
  categories: string[];
  expertise_level: string;
  department: string;
}

export interface AgentAnalysis {
  success: boolean;
  agent_name: string;
  agent_profile: AgentProfile;
  category: string;
  is_authorized: boolean;
  authorization_details: string;
  evaluation: {
    standards_met: Record<string, string>;
    strengths: string[];
    areas_for_improvement: string[];
    overall_rating: number;
    category_expertise: string;
    customer_pain_points: string;
    resolution_quality: string;
    resolution_satisfaction: number;
    agent_empathy: number;
    would_recommend: string;
    next_best_actions: string[];
  };
  timestamp: number;
}

export interface CustomerNeeds {
  success: boolean;
  predictions: {
    underlying_needs: string[];
    next_best_actions: string[];
  };
}

export interface AnalysisResponse {
  success: boolean;
  call_summaries: CallSummary[];
  agent_analysis: AgentAnalysis;
  sentiment_data: SentimentItem[][];
  customer_needs?: CustomerNeeds;
  transcripts?: string[];
  error?: string;
}

// Function to analyze call recordings
export const analyzeCall = async (
  files: File[],
  notificationEmail?: string
): Promise<AnalysisResponse> => {
  try {
    const formData = new FormData();
    
    // Append each file to the form data
    files.forEach(file => {
      formData.append('files', file);
    });
    
    // Add notification email if provided
    if (notificationEmail) {
      formData.append('notification_email', notificationEmail);
    }
    
    console.log('Submitting call for analysis:', {
      fileCount: files.length,
      fileNames: files.map(f => f.name),
      notificationEmail
    });
    
    const response = await axios.post(
      `${API_BASE_URL}/analyze-calls`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    console.log('Analysis response status:', response.status);
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to analyze call');
    }
    
    return response.data;
  } catch (error) {
    console.error('Error analyzing call:', error);
    throw error;
  }
};

// Function to get agent profiles
export const getAgentProfiles = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/agent-profiles`);
    return response.data;
  } catch (error) {
    console.error('Error fetching agent profiles:', error);
    throw error;
  }
};

// Function to get conversation categories
export const getConversationCategories = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/conversation-categories`);
    return response.data;
  } catch (error) {
    console.error('Error fetching conversation categories:', error);
    throw error;
  }
};
