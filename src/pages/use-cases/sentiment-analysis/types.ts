// Define types for the sentiment analysis feature

export interface SentimentItem {
  speaker: string;
  text: string;
  sentiment: string;
  start: number | null;
  end: number | null;
}

export interface CallSummary {
  summary: string;
  topic: string;
  product: string;
  resolved: string;
  callback: string;
  politeness: string;
  customer_sentiment: string;
  key_points: string[];
  action_items: string[];
}

export interface AgentProfile {
  id: string;
  name: string;
  categories: string[];
  expertise_level: string;
  department: string;
}

export interface AgentAnalysis {
  success: boolean;
  agent_name: string;
  agent_profile: AgentProfile;
  category: string;
  is_authorized: boolean;
  authorization_details: string;
  evaluation: {
    standards_met: Record<string, string>;
    strengths: string[];
    areas_for_improvement: string[];
    overall_rating: number;
    category_expertise: string;
    customer_pain_points: string;
    resolution_quality: string;
    resolution_satisfaction: number;
    agent_empathy: number;
    would_recommend: string;
    next_best_actions: string[];
  };
  timestamp: number;
}

export interface CustomerNeeds {
  success: boolean;
  predictions: {
    underlying_needs: string[];
    next_best_actions: string[];
  };
}

export interface AnalysisResponse {
  success: boolean;
  call_summaries: CallSummary[];
  agent_analysis: AgentAnalysis;
  sentiment_data: SentimentItem[][];
  customer_needs?: CustomerNeeds;
  transcripts?: string[];
  error?: string;
}

export interface TranscriptItem {
  text: string;
  speaker?: number;
  start?: number;
  end?: number;
}

export interface AgentPerformanceMetrics {
  overall_rating: number;
  resolution_rate: number;
  customer_satisfaction: number;
  response_time: number;
  empathy_score: number;
  knowledge_score: number;
  communication_score: number;
}

export interface SentimentAnalysisFilters {
  dateRange?: [Date, Date];
  agents?: string[];
  sentimentType?: string[];
  resolutionStatus?: string[];
  productCategory?: string[];
}

export interface SentimentTrend {
  date: string;
  positive: number;
  neutral: number;
  negative: number;
}
