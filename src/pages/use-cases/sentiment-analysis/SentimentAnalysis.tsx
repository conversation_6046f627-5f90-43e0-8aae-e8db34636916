import { useState } from "react";
import Layout from "@/components/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Upload, Play, User, Mail, AlertTriangle } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from "recharts";
import { analyzeCall, AnalysisResponse, SentimentItem } from "./api";

const agents = [
  {
    name: "<PERSON>",
    id: "AK001",
    department: "Technical Support",
    level: "Senior",
    specialties: ["Technical Support", "Product Information"]
  },
  {
    name: "<PERSON>",
    id: "SM002",
    department: "Customer Service", 
    level: "Mid-Level",
    specialties: ["Billing", "Account Management"]
  },
  {
    name: "<PERSON>",
    id: "MJ003",
    department: "Sales",
    level: "Senior", 
    specialties: ["Sales", "Product Upsell"]
  },
  {
    name: "Lisa P",
    id: "LP004",
    department: "Customer Service",
    level: "Junior",
    specialties: ["Returns", "Order Management"]
  }
];

const SentimentAnalysis = () => {
  const [file, setFile] = useState<File | null>(null);
  const [email, setEmail] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [results, setResults] = useState<AnalysisResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("call-summary");

  // Process sentiment data for the chart
  const processSentimentData = () => {
    if (!results?.sentiment_data?.[0]) return [];
    
    const sentimentItems = results.sentiment_data[0];
    const chartData = [];
    
    // Group by time quarters for the chart
    const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
    const itemsPerQuarter = Math.ceil(sentimentItems.length / 4);
    
    quarters.forEach((quarter, index) => {
      const startIndex = index * itemsPerQuarter;
      const endIndex = Math.min(startIndex + itemsPerQuarter, sentimentItems.length);
      const quarterItems = sentimentItems.slice(startIndex, endIndex);
      
      // Calculate average sentiment scores
      const customerItems = quarterItems.filter(item => item.speaker === "Speaker B");
      const agentItems = quarterItems.filter(item => item.speaker === "Speaker A");
      
      const getSentimentScore = (sentiment: string) => {
        switch(sentiment) {
          case 'positive': return 1;
          case 'neutral': return 0;
          case 'negative': return -1;
          default: return 0;
        }
      };
      
      const customerScore = customerItems.length > 0 
        ? customerItems.reduce((sum, item) => sum + getSentimentScore(item.sentiment), 0) / customerItems.length
        : 0;
        
      const agentScore = agentItems.length > 0
        ? agentItems.reduce((sum, item) => sum + getSentimentScore(item.sentiment), 0) / agentItems.length
        : 0;
      
      chartData.push({
        quarter,
        Customer: customerScore,
        Agent: agentScore
      });
    });
    
    return chartData;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const triggerFileUpload = () => {
    document.getElementById('audio-file')?.click();
  };

  const handleAnalyze = async () => {
    if (!file || !email) return;
    
    setIsAnalyzing(true);
    setError(null);
    
    try {
      // Call the API service to analyze the call
      const response = await analyzeCall([file], email);
      setResults(response);
    } catch (err: any) {
      console.error('Error analyzing call:', err);
      setError(err.message || 'Failed to analyze call. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const chartData = processSentimentData();

  return (
    <Layout
      title="Sentiment Analysis"
      description="Analyze customer calls and feedback for emotional insights and agent performance."
      category="Sales & Marketing"
    >
      <div className="space-y-8">
        {/* Input Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload Audio File</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="audio-file">Audio File (.mp3, .wav)</Label>
              <div 
                onClick={triggerFileUpload}
                className="mt-2 flex items-center justify-center w-full h-32 border-2 border-dashed border-slate-300 rounded-lg hover:border-slate-400 transition-colors cursor-pointer"
              >
                <div className="text-center">
                  <Upload className="h-8 w-8 mx-auto text-slate-400" />
                  <p className="mt-2 text-sm text-slate-600">Click to upload or drag and drop</p>
                  <p className="text-xs text-slate-500">MP3, WAV up to 10MB</p>
                </div>
              </div>
              <input
                id="audio-file"
                type="file"
                accept=".mp3,.wav"
                className="hidden"
                onChange={handleFileChange}
              />
              {file && (
                <p className="mt-2 text-sm text-slate-600">
                  Selected file: {file.name}
                </p>
              )}
            </div>
            
            <div>
              <Label htmlFor="email">Notification Email</Label>
              <div className="flex items-center mt-2">
                <Mail className="h-4 w-4 mr-2 text-slate-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>
            
            <Button 
              onClick={handleAnalyze}
              disabled={!file || !email || isAnalyzing}
              className="w-full"
              style={{ backgroundColor: '#44924C' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3a7d42'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#44924C'}
            >
              {isAnalyzing ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Analyzing audio recording...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Play className="h-4 w-4" />
                  <span>Run Sentiment Analysis</span>
                </div>
              )}
            </Button>
            
            {error && (
              <div className="bg-red-50 p-3 rounded-md flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Results Section */}
        {results && (
          <div className="space-y-6">
            <Tabs defaultValue="call-summary" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="call-summary">Call Summary</TabsTrigger>
                <TabsTrigger value="agent-analysis">Agent Analysis</TabsTrigger>
                <TabsTrigger value="sentiment-analysis">Sentiment Analysis</TabsTrigger>
              </TabsList>
              
              {/* Call Summary Tab */}
              <TabsContent value="call-summary" className="space-y-6">
                {results.call_summaries.map((summary, index) => (
                  <div key={index} className="space-y-6">
                    {/* Hero Summary Card */}
                    <Card className="border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50 to-white">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-3 text-xl">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <Play className="h-5 w-5 text-blue-600" />
                          </div>
                          Call Summary
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-slate-700 leading-relaxed text-lg">{summary.summary}</p>
                      </CardContent>
                    </Card>
                    
                    {/* Overview Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card className="hover:shadow-lg transition-shadow duration-200">
                        <CardHeader className="pb-3">
                          <CardTitle className="flex items-center gap-2 text-lg">
                            <div className="p-1.5 bg-green-100 rounded-md">
                              <User className="h-4 w-4 text-green-600" />
                            </div>
                            Overview
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="space-y-4">
                            <div className="p-4 bg-slate-50 rounded-lg border border-slate-100">
                              <Label className="text-xs font-medium text-slate-500 uppercase tracking-wide">Topic</Label>
                              <p className="text-lg font-semibold text-slate-900 mt-1">{summary.topic}</p>
                            </div>
                            <div className="p-4 bg-slate-50 rounded-lg border border-slate-100">
                              <Label className="text-xs font-medium text-slate-500 uppercase tracking-wide">Product</Label>
                              <p className="text-lg font-semibold text-slate-900 mt-1">{summary.product}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card className="hover:shadow-lg transition-shadow duration-200">
                        <CardHeader className="pb-3">
                          <CardTitle className="flex items-center gap-2 text-lg">
                            <div className="p-1.5 bg-amber-100 rounded-md">
                              <AlertTriangle className="h-4 w-4 text-amber-600" />
                            </div>
                            Call Details
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-2 gap-3">
                            <div className="p-3 bg-slate-50 rounded-lg">
                              <Label className="text-xs font-medium text-slate-500 uppercase tracking-wide">Resolved</Label>
                              <div className="flex items-center gap-2 mt-1">
                                <div className={`h-2 w-2 rounded-full ${
                                  summary.resolved === "Yes" ? "bg-green-400" : 
                                  summary.resolved === "Partial" ? "bg-amber-400" : "bg-red-400"
                                }`}></div>
                                <p className="text-sm font-semibold">{summary.resolved}</p>
                              </div>
                            </div>
                            <div className="p-3 bg-slate-50 rounded-lg">
                              <Label className="text-xs font-medium text-slate-500 uppercase tracking-wide">Callback</Label>
                              <div className="flex items-center gap-2 mt-1">
                                <div className={`h-2 w-2 rounded-full ${
                                  summary.callback === "No" ? "bg-green-400" : "bg-amber-400"
                                }`}></div>
                                <p className="text-sm font-semibold">{summary.callback}</p>
                              </div>
                            </div>
                            <div className="p-3 bg-slate-50 rounded-lg">
                              <Label className="text-xs font-medium text-slate-500 uppercase tracking-wide">Politeness</Label>
                              <div className="flex items-center gap-2 mt-1">
                                <div className={`h-2 w-2 rounded-full ${
                                  summary.politeness === "High" ? "bg-green-400" : 
                                  summary.politeness === "Medium" ? "bg-amber-400" : "bg-red-400"
                                }`}></div>
                                <p className="text-sm font-semibold">{summary.politeness}</p>
                              </div>
                            </div>
                            <div className="p-3 bg-slate-50 rounded-lg">
                              <Label className="text-xs font-medium text-slate-500 uppercase tracking-wide">Customer Sentiment</Label>
                              <div className="flex items-center gap-2 mt-1">
                                <div className={`h-2 w-2 rounded-full ${
                                  summary.customer_sentiment === "Positive" ? "bg-green-400" : 
                                  summary.customer_sentiment === "Negative" ? "bg-red-400" : "bg-amber-400"
                                }`}></div>
                                <p className={`text-sm font-semibold ${
                                  summary.customer_sentiment === "Positive" ? "text-green-600" : 
                                  summary.customer_sentiment === "Negative" ? "text-red-600" : "text-amber-600"
                                }`}>{summary.customer_sentiment}</p>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    {/* Action Taken Card */}
                    <Card className="hover:shadow-lg transition-shadow duration-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-2 text-lg">
                          <div className="p-1.5 bg-purple-100 rounded-md">
                            <Play className="h-4 w-4 text-purple-600" />
                          </div>
                          Actions Taken
                          <Badge variant="secondary" className="ml-auto">{summary.key_points.length} items</Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-3">
                          {summary.key_points.map((point, i) => (
                            <div key={i} className="flex items-start gap-3 p-3 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors">
                              <div className="flex-shrink-0 h-7 w-7 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white flex items-center justify-center text-sm font-bold shadow-sm">
                                {i + 1}
                              </div>
                              <span className="text-slate-700 leading-relaxed">{point}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                    
                    {/* Quick Insights Card */}
                    <Card className="border-l-4 border-l-amber-500 bg-gradient-to-r from-amber-50 to-white hover:shadow-lg transition-shadow duration-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-2 text-lg">
                          <div className="p-1.5 bg-amber-100 rounded-md">
                            <AlertTriangle className="h-4 w-4 text-amber-600" />
                          </div>
                          Quick Insights
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-slate-700 leading-relaxed text-base">{results.agent_analysis.evaluation.customer_pain_points}</p>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </TabsContent>
              
              {/* Agent Analysis Tab */}
          
<TabsContent value="agent-analysis" className="space-y-6">
  {results.agent_analysis && (
    <>
      {/* Agent Hero Card */}
      <Card className="border-l-4 border-l-indigo-500 bg-gradient-to-r from-indigo-50 to-white hover:shadow-lg transition-shadow duration-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <User className="h-5 w-5 text-indigo-600" />
            </div>
            Agent Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-6">
            <div className="h-16 w-16 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
              <User className="h-8 w-8 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-slate-900">{results.agent_analysis.agent_name}</h3>
              <p className="text-slate-600 text-lg">
                {results.agent_analysis.agent_profile.department} • {results.agent_analysis.agent_profile.expertise_level}
              </p>
              <div className="flex gap-2 mt-2">
                {results.agent_analysis.is_authorized && (
                  <Badge className="bg-green-100 text-green-700 border-green-200">
                    ✓ Authorized for {results.agent_analysis.category}
                  </Badge>
                )}
                <Badge variant="outline" className="text-slate-600">
                  ID: {results.agent_analysis.agent_profile.id}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Performance Rating Card */}
      <Card className="hover:shadow-lg transition-shadow duration-200">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-xl">
            <div className="p-2 bg-green-100 rounded-lg">
              <Play className="h-5 w-5 text-green-600" />
            </div>
            Performance Rating
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Overall Rating */}
          <div className="text-center p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
            <div className="flex items-center justify-center gap-4 mb-4">
              <span className="text-lg font-medium text-slate-700">Overall Rating</span>
              <div className="text-5xl font-bold text-green-600">
                {results.agent_analysis.evaluation.overall_rating}<span className="text-2xl text-slate-400">/10</span>
              </div>
            </div>
            <Progress value={results.agent_analysis.evaluation.overall_rating * 10} className="h-3 bg-green-100" />
            <p className="text-sm text-slate-600 mt-2">Excellent performance rating</p>
          </div>
          
          {/* Performance Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-100">
              <Label className="text-xs font-medium text-blue-600 uppercase tracking-wide">Category Expertise</Label>
              <p className="text-xl font-bold text-blue-700 mt-2">{results.agent_analysis.evaluation.category_expertise}</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg border border-purple-100">
              <Label className="text-xs font-medium text-purple-600 uppercase tracking-wide">Resolution Quality</Label>
              <p className="text-xl font-bold text-purple-700 mt-2">{results.agent_analysis.evaluation.resolution_quality}</p>
            </div>
            <div className="p-4 bg-rose-50 rounded-lg border border-rose-100">
              <Label className="text-xs font-medium text-rose-600 uppercase tracking-wide">Agent Empathy</Label>
              <p className="text-xl font-bold text-rose-700 mt-2">{results.agent_analysis.evaluation.agent_empathy}/10</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Strengths & Improvements Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-l-4 border-l-green-500 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="p-1.5 bg-green-100 rounded-md">
                <div className="h-4 w-4 bg-green-500 rounded-full"></div>
              </div>
              Strengths
              <Badge variant="secondary" className="ml-auto bg-green-100 text-green-700">
                {results.agent_analysis.evaluation.strengths.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {results.agent_analysis.evaluation.strengths.map((strength, i) => (
                <div key={i} className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-500 flex items-center justify-center mt-0.5">
                    <div className="h-2 w-2 bg-white rounded-full"></div>
                  </div>
                  <span className="text-slate-700 leading-relaxed">{strength}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-amber-500 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="p-1.5 bg-amber-100 rounded-md">
                <div className="h-4 w-4 bg-amber-500 rounded-full"></div>
              </div>
              Areas for Improvement
              <Badge variant="secondary" className="ml-auto bg-amber-100 text-amber-700">
                {results.agent_analysis.evaluation.areas_for_improvement.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {results.agent_analysis.evaluation.areas_for_improvement.map((area, i) => (
                <div key={i} className="flex items-start gap-3 p-3 bg-amber-50 rounded-lg">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-amber-500 flex items-center justify-center mt-0.5">
                    <div className="h-2 w-2 bg-white rounded-full"></div>
                  </div>
                  <span className="text-slate-700 leading-relaxed">{area}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Customer Pain Points Card */}
      <Card className="border-l-4 border-l-red-500 bg-gradient-to-r from-red-50 to-white hover:shadow-lg transition-shadow duration-200">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <div className="p-1.5 bg-red-100 rounded-md">
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </div>
            Customer Pain Points
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-slate-700 leading-relaxed text-base bg-white p-4 rounded-lg border border-red-100">
            {results.agent_analysis.evaluation.customer_pain_points}
          </p>
        </CardContent>
      </Card>
      
      {/* Next Best Actions Card */}
      <Card className="border-l-4 border-l-blue-500 hover:shadow-lg transition-shadow duration-200">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <div className="p-1.5 bg-blue-100 rounded-md">
              <Play className="h-4 w-4 text-blue-600" />
            </div>
            Next Best Actions
            <Badge variant="secondary" className="ml-auto bg-blue-100 text-blue-700">
              {results.agent_analysis.evaluation.next_best_actions.length} actions
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {results.agent_analysis.evaluation.next_best_actions.map((action, i) => (
              <div key={i} className="flex items-start gap-3 p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white flex items-center justify-center text-sm font-bold shadow-sm">
                  {i + 1}
                </div>
                <span className="text-slate-700 leading-relaxed font-medium">{action}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Customer Needs Card */}
      {results.customer_needs && (
        <Card className="border-l-4 border-l-purple-500 bg-gradient-to-r from-purple-50 to-white hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="p-1.5 bg-purple-100 rounded-md">
                <User className="h-4 w-4 text-purple-600" />
              </div>
              Customer Needs Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="p-4 bg-white rounded-lg border border-purple-100">
                <h4 className="font-semibold mb-3 text-purple-900 flex items-center gap-2">
                  <div className="h-3 w-3 bg-purple-500 rounded-full"></div>
                  Underlying Needs
                </h4>
                <div className="space-y-2">
                  {results.customer_needs.predictions.underlying_needs.map((need, i) => (
                    <div key={i} className="flex items-start gap-2 p-2 bg-purple-50 rounded">
                      <div className="flex-shrink-0 h-1.5 w-1.5 rounded-full bg-purple-400 mt-2"></div>
                      <span className="text-sm text-slate-700">{need}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div className="p-4 bg-white rounded-lg border border-green-100">
                <h4 className="font-semibold mb-3 text-green-900 flex items-center gap-2">
                  <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                  Recommended Actions
                </h4>
                <div className="space-y-2">
                  {results.customer_needs.predictions.next_best_actions.map((action, i) => (
                    <div key={i} className="flex items-start gap-2 p-2 bg-green-50 rounded">
                      <div className="flex-shrink-0 h-1.5 w-1.5 rounded-full bg-green-400 mt-2"></div>
                      <span className="text-sm text-slate-700">{action}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  )}
</TabsContent>
              
              {/* Sentiment Analysis Tab - IMPROVED */}
              <TabsContent value="sentiment-analysis" className="space-y-6">
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-900">Sentiment Analysis</h2>
                  
                  {/* Sentiment Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <span>Sentiment</span>
                        <div className="w-4 h-4 bg-gray-200 rounded"></div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80 w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                            <XAxis 
                              dataKey="quarter" 
                              axisLine={false}
                              tickLine={false}
                              tick={{ fontSize: 12, fill: '#666' }}
                            />
                            <YAxis 
                              domain={[-1, 1]}
                              tickFormatter={(value) => {
                                if (value === 1) return 'Positive';
                                if (value === 0) return 'Neutral';
                                if (value === -1) return 'Negative';
                                return '';
                              }}
                              axisLine={false}
                              tickLine={false}
                              tick={{ fontSize: 12, fill: '#666' }}
                            />
                            <Tooltip 
                              formatter={(value, name) => {
                                const numValue = typeof value === 'number' ? value : 0;
                                return [
                                  numValue > 0 ? 'Positive' : numValue < 0 ? 'Negative' : 'Neutral',
                                  name
                                ];
                              }}
                              labelFormatter={(label) => `Call Quarter: ${label}`}
                              contentStyle={{
                                backgroundColor: 'white',
                                border: '1px solid #e5e7eb',
                                borderRadius: '8px',
                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                              }}
                            />
                            <Legend 
                              wrapperStyle={{ paddingTop: '20px' }}
                              iconType="line"
                            />
                            <Line 
                              type="monotone" 
                              dataKey="Customer" 
                              stroke="#8b5cf6" 
                              strokeWidth={3}
                              dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 6 }}
                              activeDot={{ r: 8, stroke: '#8b5cf6', strokeWidth: 2 }}
                            />
                            <Line 
                              type="monotone" 
                              dataKey="Agent" 
                              stroke="#f59e0b" 
                              strokeWidth={3}
                              dot={{ fill: '#f59e0b', strokeWidth: 2, r: 6 }}
                              activeDot={{ r: 8, stroke: '#f59e0b', strokeWidth: 2 }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Sentiment Timeline */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Sentiment Timeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {results.sentiment_data[0] && (
                        <div className="space-y-4">
                          {results.sentiment_data[0].map((item, i) => (
                            <div 
                              key={i} 
                              className={`p-3 rounded-lg ${
                                item.sentiment === "positive" ? "bg-green-50 border-l-4 border-green-400" : 
                                item.sentiment === "negative" ? "bg-red-50 border-l-4 border-red-400" : 
                                "bg-slate-50 border-l-4 border-slate-300"
                              }`}
                            >
                              <div className="flex justify-between mb-1">
                                <span className="font-medium">
                                  {item.speaker === "Speaker A" ? "Agent" : "Customer"}
                                </span>
                                <Badge variant="outline" className={
                                  item.sentiment === "positive" ? "text-green-600 border-green-200 bg-green-50" : 
                                  item.sentiment === "negative" ? "text-red-600 border-red-200 bg-red-50" : 
                                  "text-slate-600 border-slate-200 bg-slate-50"
                                }>
                                  {item.sentiment}
                                </Badge>
                              </div>
                              <p className="text-slate-700">{item.text}</p>
                            </div>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                  
                  {/* Sentiment Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Sentiment Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div>
                          <h4 className="font-semibold mb-2">Agent Sentiment</h4>
                          <div className="space-y-2">
                            {(() => {
                              const agentSentiments = results.sentiment_data[0]?.filter(item => item.speaker === "Speaker A") || [];
                              const positive = agentSentiments.filter(item => item.sentiment === "positive").length;
                              const neutral = agentSentiments.filter(item => item.sentiment === "neutral").length;
                              const negative = agentSentiments.filter(item => item.sentiment === "negative").length;
                              const total = agentSentiments.length || 1;
                              
                              return (
                                <>
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm">Positive</span>
                                    <span className="text-sm font-medium">{positive} ({Math.round(positive/total*100)}%)</span>
                                  </div>
                                  <Progress value={positive/total*100} className="h-2 bg-slate-100" />
                                  
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm">Neutral</span>
                                    <span className="text-sm font-medium">{neutral} ({Math.round(neutral/total*100)}%)</span>
                                  </div>
                                  <Progress value={neutral/total*100} className="h-2 bg-slate-100" />
                                  
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm">Negative</span>
                                    <span className="text-sm font-medium">{negative} ({Math.round(negative/total*100)}%)</span>
                                  </div>
                                  <Progress value={negative/total*100} className="h-2 bg-slate-100" />
                                </>
                              );
                            })()}
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold mb-2">Customer Sentiment</h4>
                          <div className="space-y-2">
                            {(() => {
                              const customerSentiments = results.sentiment_data[0]?.filter(item => item.speaker === "Speaker B") || [];
                              const positive = customerSentiments.filter(item => item.sentiment === "positive").length;
                              const neutral = customerSentiments.filter(item => item.sentiment === "neutral").length;
                              const negative = customerSentiments.filter(item => item.sentiment === "negative").length;
                              const total = customerSentiments.length || 1;
                              
                              return (
                                <>
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm">Positive</span>
                                    <span className="text-sm font-medium">{positive} ({Math.round(positive/total*100)}%)</span>
                                  </div>
                                  <Progress value={positive/total*100} className="h-2 bg-slate-100" />
                                  
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm">Neutral</span>
                                    <span className="text-sm font-medium">{neutral} ({Math.round(neutral/total*100)}%)</span>
                                  </div>
                                  <Progress value={neutral/total*100} className="h-2 bg-slate-100" />
                                  
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm">Negative</span>
                                    <span className="text-sm font-medium">{negative} ({Math.round(negative/total*100)}%)</span>
                                  </div>
                                  <Progress value={negative/total*100} className="h-2 bg-slate-100" />
                                </>
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Agent Directory */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Available Agents</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {agents.map((agent, index) => (
                <div key={index} className="border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-semibold text-slate-900">{agent.name}</h3>
                        <Badge variant="outline" className="text-slate-600">
                          {agent.level}
                        </Badge>
                      </div>
                      <p className="text-sm text-slate-600 mb-2">
                        {agent.department} • ID: {agent.id}
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {agent.specialties.map((specialty, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default SentimentAnalysis;