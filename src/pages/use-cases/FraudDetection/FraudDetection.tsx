import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Shield, Upload, AlertTriangle, FileText, Mic, RefreshCw, ChevronDown, ChevronUp, Settings } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const API_BASE_URL = "https://fraud-detection.demo.aiplanet.com";

interface TranscriptionResult {
  transcript: string; 
  confidence: number;
  language: string;
}

interface AnalysisResult {
  verification_status: string;
  confidence: number;
  risk_level: string;
  missing_information: string[];
  suspicious_elements: string[];
  recommended_action: string;
  explanation: string;
}

interface TemplateInfo {
  current_template: string;
  is_default: boolean;
  template_length: number;
  default_available: boolean;
}

interface TemplateResponse {
  success: boolean;
  message: string;
  template_info: TemplateInfo;
}

interface AudioUploadResponse {
  success: boolean;
  message: string;
  transcription: TranscriptionResult;
  analysis: AnalysisResult;
}

interface TextAnalysisResponse {
  success: boolean;
  message: string;
  analysis: AnalysisResult;
}

const FraudDetection = () => {
  const [inputType, setInputType] = useState("text");
  const [textInput, setTextInput] = useState("");
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcription, setTranscription] = useState<TranscriptionResult | null>(null);
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);
  const [isLoadingDemo, setIsLoadingDemo] = useState(false);
  const [templateInfo, setTemplateInfo] = useState<TemplateInfo | null>(null);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
  const [isUpdatingTemplate, setIsUpdatingTemplate] = useState(false);
  const [editedTemplate, setEditedTemplate] = useState<string>("");
  const [isEditingTemplate, setIsEditingTemplate] = useState(false);
  const [isTemplateSettingsOpen, setIsTemplateSettingsOpen] = useState(false);
  const { toast } = useToast();

  // Load current template
  const loadTemplate = async () => {
    setIsLoadingTemplate(true);
    try {
      const response = await fetch(`${API_BASE_URL}/template`);
      if (!response.ok) throw new Error('Failed to load template');
      
      const data: TemplateResponse = await response.json();
      setTemplateInfo(data.template_info);
      setEditedTemplate(data.template_info.current_template);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load template settings",
        variant: "destructive",
      });
    } finally {
      setIsLoadingTemplate(false);
    }
  };

  // Update template
  const updateTemplate = async () => {
    if (!editedTemplate.trim()) {
      toast({
        title: "Error",
        description: "Template cannot be empty",
        variant: "destructive",
      });
      return;
    }

    setIsUpdatingTemplate(true);
    try {
      const response = await fetch(`${API_BASE_URL}/template`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template: editedTemplate,
        }),
      });

      if (!response.ok) throw new Error('Failed to update template');
      
      const data: TemplateResponse = await response.json();
      setTemplateInfo(data.template_info);
      setIsEditingTemplate(false);
      
      toast({
        title: "Template Updated",
        description: "Analysis template updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update template",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingTemplate(false);
    }
  };

  // Reset template to default
  const resetTemplate = async () => {
    setIsUpdatingTemplate(true);
    try {
      const response = await fetch(`${API_BASE_URL}/template/reset`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Failed to reset template');
      
      const data: TemplateResponse = await response.json();
      setTemplateInfo(data.template_info);
      setEditedTemplate(data.template_info.current_template);
      setIsEditingTemplate(false);
      
      toast({
        title: "Template Reset",
        description: "Analysis template reset to default successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reset template",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingTemplate(false);
    }
  };

  // Load template when settings are opened for the first time
  useEffect(() => {
    if (isTemplateSettingsOpen && !templateInfo && !isLoadingTemplate) {
      loadTemplate();
    }
  }, [isTemplateSettingsOpen]);

  // Load demo transcript
  const loadDemoTranscript = async () => {
    setIsLoadingDemo(true);
    try {
      const response = await fetch(`${API_BASE_URL}/demo-transcript`);
      if (!response.ok) throw new Error('Failed to load demo transcript');
      
      const data = await response.json();
      setTextInput(data.demo_transcript);
      setInputType("text");
      
      toast({
        title: "Demo Loaded",
        description: "Sample bank verification transcript loaded successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load demo transcript",
        variant: "destructive",
      });
    } finally {
      setIsLoadingDemo(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file type
      const allowedTypes = ['.wav', '.mp3', '.m4a', '.flac', '.ogg'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      if (!allowedTypes.includes(fileExtension)) {
        toast({
          title: "Invalid File Type",
          description: "Please upload WAV, MP3, M4A, FLAC, or OGG files only",
          variant: "destructive",
        });
        return;
      }

      setAudioFile(file);
      // Clear previous results when new file is selected
      setTranscription(null);
      setAnalysis(null);
    }
  };

  const cleanTextForAnalysis = (text: string) => {
    // Clean text to prevent JSON parsing errors
    return text
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/\r\n/g, '\n') // Normalize line endings
      .replace(/\r/g, '\n') // Convert remaining \r to \n
      .trim();
  };

  const handleAnalyzeText = async () => {
    if (!textInput.trim()) return;
    
    setIsProcessing(true);
    setAnalysis(null);
    
    try {
      const cleanedText = cleanTextForAnalysis(textInput);
      
      const response = await fetch(`${API_BASE_URL}/analyze-text`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transcript: cleanedText,
          template: null
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: TextAnalysisResponse = await response.json();
      
      if (data.success) {
        setAnalysis(data.analysis);
        toast({
          title: "Analysis Complete",
          description: "Text analyzed successfully for fraud detection",
        });
      } else {
        throw new Error(data.message || 'Analysis failed');
      }
    } catch (error) {
      console.error('Error analyzing text:', error);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to analyze text",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAnalyzeAudio = async () => {
    if (!audioFile) return;
    
    setIsProcessing(true);
    setTranscription(null);
    setAnalysis(null);
    
    try {
      const formData = new FormData();
      formData.append('file', audioFile);

      const response = await fetch(`${API_BASE_URL}/analyze-audio`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: AudioUploadResponse = await response.json();
      
      if (data.success) {
        setTranscription(data.transcription);
        setAnalysis(data.analysis);
        toast({
          title: "Analysis Complete",
          description: "Audio transcribed and analyzed successfully",
        });
      } else {
        throw new Error(data.message || 'Analysis failed');
      }
    } catch (error) {
      console.error('Error analyzing audio:', error);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to analyze audio",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAnalyze = () => {
    if (inputType === "text") {
      handleAnalyzeText();
    } else {
      handleAnalyzeAudio();
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case 'high': return 'text-red-600';
      case 'medium': return 'text-orange-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'verified': return 'text-green-600';
      case 'suspicious': return 'text-red-600';
      case 'needs_review': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const formatAnalysisText = (text: string) => {
    return text
      // Convert ### headers to bold text
      .replace(/### (.*?)(\n|$)/g, '**$1**\n\n')
      // Convert **text** to bold spans
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Convert bullet points
      .replace(/^- (.*?)$/gm, '• $1')
      // Convert numbered lists
      .replace(/^(\d+)\. (.*?)$/gm, '$1. $2')
      // Add proper line breaks
      .replace(/\n\n/g, '<br/><br/>')
      .replace(/\n/g, '<br/>');
  };

  const toTitleCase = (text: string) => {
    if (!text) return text;
    return text
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <Layout
      title="Fraud Detection"
      description="AI-powered fraud analysis for bank callback verification calls and text communications."
      category="Financial Banking"
    >
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Fraud Analysis Input</span>
              </div>
              <Button
                onClick={loadDemoTranscript}
                disabled={isLoadingDemo}
                variant="outline"
                size="sm"
              >
                {isLoadingDemo ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin h-3 w-3 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                    <span>Loading...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <FileText className="h-3 w-3" />
                    <span>Load Demo</span>
                  </div>
                )}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={inputType} onValueChange={setInputType} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="text" className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Text Input</span>
                </TabsTrigger>
                <TabsTrigger value="audio" className="flex items-center space-x-2">
                  <Mic className="h-4 w-4" />
                  <span>Audio File</span>
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="text" className="space-y-4">
                <div>
                  <Label htmlFor="text-input">Enter Bank Verification Call Transcript</Label>
                  <Textarea
                    id="text-input"
                    value={textInput}
                    onChange={(e) => setTextInput(e.target.value)}
                    placeholder="Paste bank callback verification transcript to analyze for fraud patterns..."
                    className="mt-2 min-h-[200px]"
                  />
                  <div className="text-sm text-gray-500 mt-2">
                    Enter a transcript of a bank verification call to analyze for fraud patterns and compliance.
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="audio" className="space-y-4">
                <div>
                  <Label>Upload Audio File</Label>
                  <div 
                    className="mt-2 flex items-center justify-center w-full h-40 border-2 border-dashed border-gray-300 rounded-xl hover:border-emerald-400 hover:bg-emerald-50 transition-all cursor-pointer"
                    onClick={() => document.getElementById('audio-upload')?.click()}
                  >
                    <div className="text-center">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <div className="text-sm text-gray-600 mb-1">
                        {audioFile ? (
                          <div>
                            <div className="font-medium text-emerald-600">{audioFile.name}</div>
                            <div className="text-xs text-gray-500">
                              {(audioFile.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                          </div>
                        ) : (
                          "Click anywhere in this area to select an audio file"
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        Supported: WAV, MP3, M4A, FLAC, OGG (Max 25MB)
                      </div>
                      <input
                        id="audio-upload"
                        type="file"
                        accept=".mp3,.wav,.m4a,.flac,.ogg"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
            
            <Button 
              onClick={handleAnalyze}
              disabled={
                (inputType === "text" && !textInput.trim()) || 
                (inputType === "audio" && !audioFile) || 
                isProcessing
              }
                              className="w-full mt-6"
                style={{ backgroundColor: '#44924C' }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3a7d42'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#44924C'}
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>
                    {inputType === "audio" ? "Transcribing and analyzing..." : "Analyzing for fraud..."}
                  </span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4" />
                  <span>Analyze for Fraud</span>
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Transcription Results */}
        {transcription && inputType === "audio" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Audio Transcription</span>
                <div className="ml-auto text-sm text-gray-500">
                  Confidence: {(transcription.confidence * 100).toFixed(1)}%
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-lg border">
                <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                  {transcription.transcript}
                </pre>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Analysis Results */}
        {analysis && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  <span>Fraud Analysis Results</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Status and Risk Overview */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600 mb-1">Verification Status</div>
                      <div className={`text-lg font-bold ${getStatusColor(analysis.verification_status)}`}>
                        {analysis.verification_status.replace('_', ' ').toUpperCase()}
                      </div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600 mb-1">Risk Level</div>
                      <div className={`text-lg font-bold ${getRiskColor(analysis.risk_level)}`}>
                        {analysis.risk_level.toUpperCase()}
                      </div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-600 mb-1">Confidence Score</div>
                      <div className="text-lg font-bold text-blue-600">
                        {(analysis.confidence * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>

                  {/* Detailed Analysis */}
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Analysis Explanation</h4>
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <div 
                          className="text-sm leading-relaxed"
                          dangerouslySetInnerHTML={{ __html: formatAnalysisText(analysis.explanation) }}
                        />
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Recommended Action</h4>
                      <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
                        <p className="text-sm font-medium text-emerald-800">{toTitleCase(analysis.recommended_action)}</p>
                      </div>
                    </div>

                    {analysis.suspicious_elements.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Suspicious Elements</h4>
                        <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                          <ul className="space-y-1">
                            {analysis.suspicious_elements.map((element, index) => (
                              <li key={index} className="text-sm text-red-800 flex items-start">
                                <span className="text-red-500 mr-2">•</span>
                                {element}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}

                    {analysis.missing_information.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Missing Information</h4>
                        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                          <ul className="space-y-1">
                            {analysis.missing_information.map((info, index) => (
                              <li key={index} className="text-sm text-orange-800 flex items-start">
                                <span className="text-orange-500 mr-2">•</span>
                                {info}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Template Settings */}
        <Card>
          <CardHeader>
            <CardTitle 
              className="flex items-center justify-between cursor-pointer hover:text-purple-600 transition-colors"
              onClick={() => setIsTemplateSettingsOpen(!isTemplateSettingsOpen)}
            >
              <div className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-purple-600" />
                <span>Analysis Template Settings</span>
                {templateInfo ? (
                  <div className="text-sm text-gray-500 font-normal">
                    ({templateInfo.is_default ? "Default" : "Custom"})
                  </div>
                ) : (
                  <div className="text-sm text-gray-500 font-normal">
                    (Click to configure)
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500 font-normal">
                  {isTemplateSettingsOpen ? "Hide Settings" : "Show Settings"}
                </span>
                {isTemplateSettingsOpen ? (
                  <ChevronUp className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                )}
              </div>
            </CardTitle>
          </CardHeader>
          {isTemplateSettingsOpen && (
            <CardContent>
            {isLoadingTemplate ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin h-6 w-6 border-2 border-purple-500 border-t-transparent rounded-full"></div>
                <span className="ml-2">Loading template...</span>
              </div>
            ) : templateInfo ? (
              <div className="space-y-4">
                <div className="flex items-start justify-between gap-4">
                  <div className="text-sm text-gray-600 flex-1">
                    Customize the analysis template to adjust how the AI analyzes fraud patterns.
                  </div>
                  <div className="flex space-x-2 flex-shrink-0">
                    {!isEditingTemplate ? (
                      <Button
                        onClick={() => setIsEditingTemplate(true)}
                        variant="outline"
                        size="sm"
                      >
                        Edit Template
                      </Button>
                    ) : (
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => {
                            setIsEditingTemplate(false);
                            setEditedTemplate(templateInfo.current_template);
                          }}
                          variant="outline"
                          size="sm"
                          disabled={isUpdatingTemplate}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={updateTemplate}
                          size="sm"
                          disabled={isUpdatingTemplate}
                          className=""
                style={{ backgroundColor: '#44924C' }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3a7d42'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#44924C'}
                        >
                          {isUpdatingTemplate ? (
                            <div className="flex items-center space-x-2">
                              <div className="animate-spin h-3 w-3 border-2 border-white border-t-transparent rounded-full"></div>
                              <span>Saving...</span>
                            </div>
                          ) : (
                            "Save Template"
                          )}
                        </Button>
                      </div>
                    )}
                    <Button
                      onClick={resetTemplate}
                      variant="outline"
                      size="sm"
                      disabled={isUpdatingTemplate || templateInfo.is_default}
                    >
                      {isUpdatingTemplate ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin h-3 w-3 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                          <span>Resetting...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <RefreshCw className="h-3 w-3" />
                          <span>Reset to Default</span>
                        </div>
                      )}
                    </Button>
                  </div>
                </div>

                <div>
                  {isEditingTemplate ? (
                    <div className="space-y-2">
                      <Label htmlFor="template-editor">Analysis Template</Label>
                      <Textarea
                        id="template-editor"
                        value={editedTemplate}
                        onChange={(e) => setEditedTemplate(e.target.value)}
                        className="min-h-[300px] font-mono text-sm"
                        placeholder="Enter the analysis template..."
                      />
                      <div className="text-xs text-gray-500">
                        This template guides the AI on how to analyze fraud patterns. 
                        Include specific criteria, questions, and analysis points you want the AI to consider.
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Label>Current Analysis Template</Label>
                      <div className="bg-gray-50 p-4 rounded-lg border max-h-[300px] overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-xs leading-relaxed text-gray-700">
                          {templateInfo.current_template}
                        </pre>
                      </div>
                      <div className="text-xs text-gray-500">
                        This template is used to guide the AI analysis of fraud patterns in bank verification calls.
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Failed to load template settings. Please refresh the page.
              </div>
            )}
            </CardContent>
          )}
        </Card>

      </div>
    </Layout>
  );
};

export default FraudDetection;
