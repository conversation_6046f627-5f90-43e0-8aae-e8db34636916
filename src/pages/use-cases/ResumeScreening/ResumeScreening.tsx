import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Upload, MessageSquare, Send, FileText, User, AlertCircle, X, CheckCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Configure the GenAI Stack backend URL - AI Planet hosted service
const GENAI_BACKEND_URL = import.meta.env.VITE_AIPLANET_BASE_URL || "https://app.aiplanet.com";
const STACK_APP_ID = import.meta.env.VITE_AIPLANET_APP_ID || "e3dfaf27-483e-4306-9918-c8b531947583";
const API_KEY = import.meta.env.VITE_AIPLANET_API_KEY;

interface ChatMessage {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: Date;
}

interface ChatSession {
  id: string;
  created_at: string;
}

interface UploadedFile {
  id: string;
  filename: string;
  status: string;
}

const ResumeScreening = () => {
  console.log("🚀 ResumeScreening component is loading...");
  
  // Early return if environment is not properly configured
  if (typeof window === 'undefined') {
    return <div>Loading...</div>;
  }

  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load existing chat sessions on component mount
  useEffect(() => {
    console.log("API_KEY:", API_KEY ? "Found" : "Not found");
    console.log("Environment variables:", {
      GENAI_BACKEND_URL,
      STACK_APP_ID,
      API_KEY: API_KEY ? "***" + API_KEY.slice(-4) : "undefined"
    });
    
    if (!API_KEY) {
      setError("API key not found. Please add VITE_AIPLANET_API_KEY to your .env.local file in the project root.");
      return;
    }
    loadChatSessions();
  }, []);

  const loadChatSessions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${GENAI_BACKEND_URL}/api/v1/${STACK_APP_ID}/chat-sessions`, {
        headers: {
          'authorization': API_KEY,
          'Content-Type': 'application/json',
        },
      });
      if (response.ok) {
        const sessions = await response.json();
        // Ensure sessions is always an array
        setChatSessions(Array.isArray(sessions) ? sessions : []);
      } else {
        setError("Failed to load chat sessions. Please check your API key and permissions.");
        setChatSessions([]); // Ensure it's an empty array on error
      }
    } catch (error) {
      setError("Cannot connect to AI Planet service. Please check your internet connection and API key.");
      setChatSessions([]); // Ensure it's an empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  const createNewSession = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`${GENAI_BACKEND_URL}/api/v1/${STACK_APP_ID}/chat-sessions`, {
        method: 'POST',
        headers: {
          'authorization': API_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: `Resume Analysis Session ${new Date().toLocaleString()}`,
          description: "New resume analysis conversation"
        })
      });

      if (response.ok) {
        const newSession = await response.json();
        setCurrentSessionId(newSession.id);
        setChatSessions([newSession, ...(chatSessions || [])]);
        setMessages([]);
        setUploadedFiles([]);
        setSuccess("New chat session created successfully!");
      } else {
        setError("Failed to create new chat session");
      }
    } catch (error) {
      setError("Network error while creating session");
    } finally {
      setIsLoading(false);
    }
  };

  const selectSession = async (sessionId: string) => {
    setCurrentSessionId(sessionId);
    setMessages([]); // In a real implementation, you'd load the session's message history
    loadSessionUploads(sessionId);
  };

  const loadSessionUploads = async (sessionId: string) => {
    try {
      const response = await fetch(`${GENAI_BACKEND_URL}/api/v1/${STACK_APP_ID}/chat-sessions/${sessionId}/uploads`, {
        headers: {
          'authorization': API_KEY,
          'Content-Type': 'application/json',
        },
      });
      if (response.ok) {
        const uploads = await response.json();
        setUploadedFiles(Array.isArray(uploads) ? uploads : []);
      }
    } catch (error) {
      console.error("Error loading uploads:", error);
    }
  };

  const handleFileUpload = async (file: File) => {
    console.log('🚀 handleFileUpload called with:', file.name);
    
    if (!currentSessionId) {
      setError("Please create a chat session first");
      return;
    }

    if (!API_KEY) {
      setError("API key not found. Please check your .env.local file.");
      return;
    }

    console.log('Starting file upload:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      sessionId: currentSessionId,
      apiKey: API_KEY ? 'Present' : 'Missing'
    });

    setIsUploading(true);
    setError(null);

    try {
      const uploadUrl = `${GENAI_BACKEND_URL}/api/v1/${STACK_APP_ID}/chat-sessions/${currentSessionId}/uploads`;
      console.log('Upload URL:', uploadUrl);

      // The API requires input_key field - let's try common values
      const inputKeysToTry = ['file', 'document', 'resume', 'upload', 'input', 'pdf'];
      let success = false;
      let lastError = '';

      for (const inputKey of inputKeysToTry) {
        console.log(`Trying with input_key: ${inputKey}`);
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('input_key', inputKey);

        const response = await fetch(uploadUrl, {
          method: 'POST',
          headers: {
            'authorization': API_KEY,
          },
          body: formData,
        });

        console.log(`Response status for ${inputKey}:`, response.status);

        if (response.ok) {
          const uploadResult = await response.json();
          console.log('Upload successful with input_key:', inputKey, uploadResult);
          
          setUploadedFiles([...(uploadedFiles || []), uploadResult]);
          setSuccess(`Successfully uploaded ${file.name}!`);
          
          // Add a system message about the upload
          const systemMessage: ChatMessage = {
            id: Date.now().toString(),
            content: `📄 Uploaded resume: ${file.name}. You can now ask questions about this resume!`,
            type: 'assistant',
            timestamp: new Date()
          };
          setMessages(prev => [...(prev || []), systemMessage]);
          success = true;
          break;
        } else {
          const errorText = await response.text();
          lastError = errorText;
          console.log(`Failed with ${inputKey}:`, response.status, errorText);
          
          // If it's not a pipeline/input_key error, break early
          if (!errorText.includes('pipeline') && !errorText.includes('input_key')) {
            break;
          }
        }
      }

      // If none of the input_keys worked, show the last error
      if (!success) {
        console.error('All input_key attempts failed. Last error:', lastError);
        setError(`Failed to upload file - tried multiple input_key values. Last error: ${lastError.substring(0, 200)}`);
      }
    } catch (error) {
      console.error('Upload network error:', error);
      setError(`Network error during upload: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || !currentSessionId) return;

    console.log('💬 Sending message:', inputMessage);
    console.log('💬 Session ID:', currentSessionId);

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      type: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...(prev || []), userMessage]);
    const messageToSend = inputMessage;
    setInputMessage("");
    setIsSending(true);
    setError(null);

    try {
      // Try multiple endpoints to get app configuration
      console.log('💬 Fetching stack app configuration...');
      const configEndpoints = [
        `${GENAI_BACKEND_URL}/api/v1/${STACK_APP_ID}`,
        `${GENAI_BACKEND_URL}/api/v1/${STACK_APP_ID}/config`,
        `${GENAI_BACKEND_URL}/api/v1/${STACK_APP_ID}/info`,
        `${GENAI_BACKEND_URL}/api/v1/apps/${STACK_APP_ID}`,
      ];

      let formKeysData = null;
      for (const configUrl of configEndpoints) {
        console.log('💬 Trying config URL:', configUrl);
        try {
          const configResponse = await fetch(configUrl, {
            headers: {
              'authorization': API_KEY,
              'Content-Type': 'application/json',
            },
          });

          if (configResponse.ok) {
            const appConfig = await configResponse.json();
            console.log('💬 App configuration:', appConfig);
            formKeysData = appConfig.form_keys_data || appConfig.input_keys || appConfig.keys || appConfig.chatKeys;
            console.log('💬 Form keys data:', formKeysData);
            if (formKeysData) break;
          } else {
            console.log(`💬 Config endpoint ${configUrl} failed:`, configResponse.status);
          }
        } catch (e) {
          console.log(`💬 Config endpoint ${configUrl} error:`, e);
        }
      }

      const chatUrl = `${GENAI_BACKEND_URL}/api/v1/${STACK_APP_ID}/chat-sessions/${currentSessionId}`;
      console.log('💬 Chat URL:', chatUrl);

      let success = false;
      let lastError = '';

      // If we have form_keys_data, use it to construct proper requests
      if (formKeysData && typeof formKeysData === 'object') {
        console.log('💬 Using dynamic form_keys_data');
        
        for (const [chatKey, inputConfig] of Object.entries(formKeysData)) {
          console.log(`💬 Trying dynamic chatKey: ${chatKey}`, inputConfig);
          
          // Add small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Build inputs object based on the configuration
          let inputs: Record<string, any> = {};
          if (typeof inputConfig === 'object' && inputConfig !== null) {
            const config = inputConfig as any; // Type assertion for dynamic config
            
            // If inputConfig has specific field requirements, try to match them
            if (Array.isArray(inputConfig)) {
              // If it's an array of required fields
              for (const field of inputConfig) {
                if (typeof field === 'string') {
                  inputs[field] = messageToSend;
                }
              }
            } else if (config.fields || config.inputs) {
              // If it has a fields or inputs structure
              const fields = config.fields || config.inputs;
              if (Array.isArray(fields)) {
                for (const field of fields) {
                  if (typeof field === 'string') {
                    inputs[field] = messageToSend;
                  } else if (field && typeof field === 'object' && field.name) {
                    inputs[field.name] = messageToSend;
                  }
                }
              }
            } else {
              // If inputConfig is an object with field names as keys, use those
              for (const [fieldName, fieldValue] of Object.entries(config)) {
                inputs[fieldName] = messageToSend;
              }
              // Also add common field names as fallback
              inputs = { ...inputs, message: messageToSend, query: messageToSend, text: messageToSend, question: messageToSend };
            }
          } else {
            // Default inputs
            inputs = { message: messageToSend };
          }

          const requestBody = {
            chatKey: chatKey,
            inputs: inputs,
            stream: false
          };

          console.log(`💬 Request body for ${chatKey}:`, requestBody);

          const response = await fetch(chatUrl, {
            method: 'POST',
            headers: {
              'authorization': API_KEY,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
          });

          console.log(`💬 Response status for ${chatKey}:`, response.status);

                  if (response.ok) {
          // Read response as text first to avoid "body already read" error
          const responseText = await response.text();
          console.log('💬 Raw response text for chatKey:', chatKey, responseText);
          
          try {
            // Handle streaming JSON responses (multiple JSON objects concatenated without newlines)
            let finalMessage = '';
            let hasError = false;
            
            // Split concatenated JSON objects by finding }{ pattern
            const jsonParts = responseText.split(/(?<=})\s*(?={)/);
            
            for (const jsonPart of jsonParts) {
              try {
                const parsed = JSON.parse(jsonPart);
                console.log('💬 Parsed JSON chunk for chatKey:', chatKey, parsed);
                
                // Check for error
                if (parsed.type === 'error' || parsed.message?.includes('Error:')) {
                  console.log('💬 Error detected in response for chatKey:', chatKey, parsed.message);
                  hasError = true;
                  break;
                }
                
                // Extract message content (look for actual AI response, not null messages)
                if (parsed.message && typeof parsed.message === 'string' && parsed.message.trim() && parsed.message !== 'null') {
                  finalMessage = parsed.message;
                }
              } catch (parseError) {
                console.log('💬 Could not parse JSON chunk for chatKey:', chatKey, jsonPart, parseError);
              }
            }
            
            if (hasError) {
              console.log('💬 Response contained error for chatKey, trying next one:', chatKey);
              continue; // Try next chatKey
            }
            
            if (finalMessage) {
              console.log('💬 Chat successful with chatKey (streaming JSON):', chatKey, finalMessage);
              const assistantMessage: ChatMessage = {
                id: (Date.now() + 1).toString(),
                content: finalMessage,
                type: 'assistant',
                timestamp: new Date()
              };
              setMessages(prev => [...(prev || []), assistantMessage]);
              success = true;
              break;
            }
            
            // Fallback: try to parse as single JSON
            const result = JSON.parse(responseText);
            console.log('💬 Chat successful with chatKey (single JSON):', chatKey, result);
            
            const assistantMessage: ChatMessage = {
              id: (Date.now() + 1).toString(),
              content: result.response || result.message || result.output || result.answer || result.text || "I've analyzed your request.",
              type: 'assistant',
              timestamp: new Date()
            };
            setMessages(prev => [...(prev || []), assistantMessage]);
            success = true;
            break;
          } catch (jsonError) {
            // If JSON parsing fails, use the response as plain text
            console.log('💬 Not JSON for chatKey, using as text:', jsonError);
            
            if (responseText && responseText.trim()) {
              const assistantMessage: ChatMessage = {
                id: (Date.now() + 1).toString(),
                content: responseText,
                type: 'assistant',
                timestamp: new Date()
              };
              setMessages(prev => [...(prev || []), assistantMessage]);
              success = true;
              break;
            }
          }
        } else {
          try {
            const errorText = await response.text();
            lastError = errorText;
            console.log(`💬 Failed with ${chatKey}:`, response.status, errorText);
          } catch (textError) {
            console.log('💬 Could not read error response for chatKey:', textError);
            lastError = `HTTP ${response.status}`;
          }
        }
        }
      }

      // Fallback: try extensive patterns if dynamic approach didn't work
      if (!success) {
        console.log('💬 Trying fallback common patterns...');
        const commonPatterns = [
          // Based on the API error analysis - inputs field is required, and try the chatKeys from form_keys_data
          { chatKey: 'inputs', inputs: { question: messageToSend }, stream: false },
          { chatKey: 'input_keys', inputs: { question: messageToSend }, stream: false },
          { chatKey: 'inputs', inputs: { message: messageToSend }, stream: false },
        ];

        for (const pattern of commonPatterns) {
          console.log(`💬 Trying fallback pattern:`, pattern);

          // Add small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));

          const response = await fetch(chatUrl, {
            method: 'POST',
            headers: {
              'authorization': API_KEY,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(pattern)
          });

          if (response.ok) {
            // Read response as text first to avoid "body already read" error
            const responseText = await response.text();
            console.log('💬 Raw response text:', responseText);
            
            try {
                          // Handle streaming JSON responses (multiple JSON objects concatenated without newlines)
            let finalMessage = '';
            let hasError = false;
            
            // Split concatenated JSON objects by finding }{ pattern
            const jsonParts = responseText.split(/(?<=})\s*(?={)/);
            
            for (const jsonPart of jsonParts) {
              try {
                const parsed = JSON.parse(jsonPart);
                console.log('💬 Parsed JSON chunk:', parsed);
                
                // Check for error
                if (parsed.type === 'error' || parsed.message?.includes('Error:')) {
                  console.log('💬 Error detected in response:', parsed.message);
                  hasError = true;
                  break;
                }
                
                // Extract message content (look for actual AI response, not null messages)
                if (parsed.message && typeof parsed.message === 'string' && parsed.message.trim() && parsed.message !== 'null') {
                  finalMessage = parsed.message;
                }
              } catch (parseError) {
                console.log('💬 Could not parse JSON chunk:', jsonPart, parseError);
              }
            }
              
              if (hasError) {
                console.log('💬 Response contained error, trying next pattern');
                continue; // Try next pattern
              }
              
              if (finalMessage) {
                console.log('💬 Chat successful with fallback (streaming JSON):', pattern.chatKey || 'no-chatKey', finalMessage);
                const assistantMessage: ChatMessage = {
                  id: (Date.now() + 1).toString(),
                  content: finalMessage,
                  type: 'assistant',
                  timestamp: new Date()
                };
                setMessages(prev => [...(prev || []), assistantMessage]);
                success = true;
                break;
              }
              
              // Fallback: try to parse as single JSON
              const result = JSON.parse(responseText);
              console.log('💬 Chat successful with fallback (single JSON):', pattern.chatKey || 'no-chatKey', result);
              
              const assistantMessage: ChatMessage = {
                id: (Date.now() + 1).toString(),
                content: result.response || result.message || result.output || result.answer || result.text || "I've analyzed your request.",
                type: 'assistant',
                timestamp: new Date()
              };
              setMessages(prev => [...(prev || []), assistantMessage]);
              success = true;
              break;
            } catch (jsonError) {
              // If JSON parsing fails, use the response as plain text
              console.log('💬 Not JSON, using as text:', jsonError);
              
              if (responseText && responseText.trim()) {
                const assistantMessage: ChatMessage = {
                  id: (Date.now() + 1).toString(),
                  content: responseText,
                  type: 'assistant',
                  timestamp: new Date()
                };
                setMessages(prev => [...(prev || []), assistantMessage]);
                success = true;
                break;
              }
            }
          } else {
            try {
              const errorText = await response.text();
              lastError = errorText;
              console.log(`💬 Fallback failed:`, pattern.chatKey || 'no-chatKey', response.status, errorText);
              
              // If we get a different error (not chatKey related), log it specially
              if (!errorText.includes('reason') && response.status !== 500) {
                console.log('💬 Different error type detected:', errorText);
              }
            } catch (textError) {
              console.log('💬 Could not read error response:', textError);
              lastError = `HTTP ${response.status}`;
            }
          }
        }
      }

      if (!success) {
        console.error('💬 All attempts failed. Last error:', lastError);
        setError(`Unable to send message to AI. Please check the GenAI Stack app configuration. Error: ${lastError.substring(0, 150)}`);
      }
    } catch (error) {
      console.error('💬 Network error:', error);
      setError(`Network error while sending message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <Layout
      title="Resume Analysis"
      description="AI-powered resume analysis with interactive chat interface for detailed candidate evaluation."
      category="Sales & Marketing"
    >
      <div className="space-y-6">
        {error && (
          <Alert variant="destructive" className="relative flex items-start gap-3 p-4 pr-10">
            <AlertCircle className="h-4 w-4 flex-shrink-0 mt-0.5" />
            <div className="flex-1 min-w-0">
              <AlertDescription className="text-sm break-words whitespace-pre-wrap">{error}</AlertDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-7 h-6 w-6 p-0 hover:bg-red-100 rounded-full flex items-center justify-center"
              onClick={() => setError(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </Alert>
        )}

        {success && (
          <Alert className="relative flex items-start gap-3 p-4 pr-10 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 flex-shrink-0 mt-0.5 text-green-600" />
            <div className="flex-1 min-w-0">
              <AlertDescription className="text-sm text-green-800 break-words whitespace-pre-wrap">{success}</AlertDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-7 h-6 w-6 p-0 hover:bg-green-100 rounded-full flex items-center justify-center"
              onClick={() => setSuccess(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sessions Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center space-x-2">
                    <MessageSquare className="h-4 w-4" />
                    <span>Sessions</span>
                  </span>
                  <Button
                    size="sm"
                    onClick={createNewSession}
                    disabled={isLoading}
                    className="bg-emerald-500 hover:bg-emerald-600"
                  >
                    New
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {(chatSessions || []).map((session) => (
                    <Button
                      key={session.id}
                      variant={currentSessionId === session.id ? "default" : "outline"}
                      className="w-full justify-start text-left h-auto p-3"
                      onClick={() => selectSession(session.id)}
                    >
                      <div className="truncate">
                        <div className="text-sm font-medium">Session</div>
                        <div className="text-xs text-gray-500">
                          {new Date(session.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </Button>
                  ))}
                  {(!chatSessions || chatSessions.length === 0) && !isLoading && (
                    <div className="text-center py-4 text-gray-500 text-sm">
                      No sessions yet. Create your first session to start analyzing resumes.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Chat Area */}
          <div className="lg:col-span-3">
            {currentSessionId ? (
              <div className="space-y-4">
                {/* File Upload */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Upload className="h-4 w-4" />
                      <span>Upload Resume</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-4">
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx,.txt"
                        onChange={(e) => {
                          console.log('📁 File input changed:', e.target.files);
                          if (e.target.files?.[0]) {
                            console.log('📁 Selected file:', e.target.files[0].name);
                            handleFileUpload(e.target.files[0]);
                          }
                        }}
                        className="hidden"
                        id="resume-upload"
                      />
                      <label
                        htmlFor="resume-upload"
                        className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer"
                      >
                        <FileText className="h-4 w-4" />
                        <span>Choose File</span>
                      </label>
                      {isUploading && (
                        <div className="flex items-center space-x-2 text-emerald-600">
                          <div className="animate-spin h-4 w-4 border-2 border-emerald-500 border-t-transparent rounded-full"></div>
                          <span className="text-sm">Uploading...</span>
                        </div>
                      )}
                    </div>
                    {(uploadedFiles || []).length > 0 && (
                      <div className="mt-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">Uploaded Files:</div>
                        <div className="space-y-1">
                          {(uploadedFiles || []).map((file) => (
                            <div key={file.id} className="flex items-center space-x-2 text-sm text-gray-600">
                              <FileText className="h-3 w-3" />
                              <span>{file.filename}</span>
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                {file.status}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Chat Messages */}
                <Card className="flex-1">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span>Resume Analysis Chat</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4 max-h-96 overflow-y-auto mb-4">
                      {(!messages || messages.length === 0) && (
                        <div className="text-center py-8 text-gray-500">
                          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p>Upload a resume and start asking questions!</p>
                          <p className="text-sm mt-2">Try: "What are the key skills in this resume?" or "Rate this candidate for a software engineer position"</p>
                        </div>
                      )}
                      {(messages || []).map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div
                            className={`max-w-[80%] px-4 py-2 rounded-lg ${
                              message.type === 'user'
                                ? 'bg-emerald-500 text-white'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            <div className="text-sm">{message.content}</div>
                            <div className={`text-xs mt-1 ${
                              message.type === 'user' ? 'text-emerald-100' : 'text-gray-500'
                            }`}>
                              {message.timestamp.toLocaleTimeString()}
                            </div>
                          </div>
                        </div>
                      ))}
                      {isSending && (
                        <div className="flex justify-start">
                          <div className="bg-gray-100 px-4 py-2 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <div className="animate-spin h-4 w-4 border-2 border-emerald-500 border-t-transparent rounded-full"></div>
                              <span className="text-sm text-gray-600">AI is analyzing...</span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Message Input */}
                    <div className="flex space-x-2">
                      <Input
                        value={inputMessage}
                        onChange={(e) => setInputMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Ask about the resume... (e.g., 'What are the candidate's strengths?')"
                        className="flex-1"
                        disabled={isSending}
                      />
                      <Button
                        onClick={sendMessage}
                        disabled={!inputMessage.trim() || isSending}
                        className="bg-emerald-500 hover:bg-emerald-600"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <MessageSquare className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to Resume Analysis</h3>
                  <p className="text-gray-600 mb-4">
                    Create a new chat session to start analyzing resumes with AI assistance.
                  </p>
                  <Button
                    onClick={createNewSession}
                    disabled={isLoading}
                    className="bg-emerald-500 hover:bg-emerald-600"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Create New Session
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ResumeScreening;
