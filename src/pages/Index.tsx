
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Search, TrendingUp, Shield, Stethoscope, Building2, Code2, Brain } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const categories = [
  "All",
  "Financial Banking", 
  "Manufacturing",
  "Pharma & Healthcare",
  "Sales & Marketing",
  "Developer Tools",
  "Misc"
];

const useCases = [
  {
    id: "sentiment-analysis",
    title: "Sentiment Analysis",
    description: "Analyze customer calls and feedback for emotional insights and agent performance.",
    category: "Sales & Marketing",
    icon: TrendingUp,
    path: "/use-cases/sentiment-analysis"
  },
  {
    id: "life-science-research",
    title: "Life Science Research Summarization", 
    description: "Transform complex research papers into digestible summaries for different audiences.",
    category: "Pharma & Healthcare",
    icon: Stethoscope,
    path: "/use-cases/life-science-research"
  },
  {
    id: "credit-card-recommendation",
    title: "Credit Card Recommendation",
    description: "Intelligent credit card matching based on user profiles and spending patterns.",
    category: "Financial Banking", 
    icon: Building2,
    path: "/use-cases/credit-card-recommendation"
  },
  {
    id: "fraud-detection",
    title: "Fraud Detection",
    description: "Real-time fraud analysis for audio conversations and text communications.",
    category: "Financial Banking",
    icon: Shield,
    path: "/use-cases/fraud-detection"
  },
  {
    id: "pharma-knowledgebase",
    title: "Pharma Knowledgebase",
    description: "Interactive pharmaceutical information system with intelligent Q&A capabilities.",
    category: "Pharma & Healthcare",
    icon: Stethoscope,
    path: "/use-cases/pharma-knowledgebase"
  },
  {
    id: "kyc-document-verification",
    title: "KYC Document Verification",
    description: "Automated document authentication and data extraction for compliance.",
    category: "Financial Banking",
    icon: Shield,
    path: "/use-cases/kyc-document-verification"
  },
  {
    id: "market-research",
    title: "Market Research (MarketMaven)",
    description: "AI-powered market analysis and competitive intelligence platform.",
    category: "Sales & Marketing",
    icon: TrendingUp,
    path: "/use-cases/market-research"
  },
  {
    id: "medical-qa",
    title: "Medical Q&A Assistant",
    description: "Intelligent medical information assistant for healthcare professionals.",
    category: "Pharma & Healthcare",
    icon: Stethoscope,
    path: "/use-cases/medical-qa"
  },
  {
    id: "medical-report-analysis",
    title: "Medical Report Analysis",
    description: "Automated medical document analysis with intelligent insights extraction.",
    category: "Pharma & Healthcare",
    icon: Stethoscope,
    path: "/use-cases/medical-report-analysis"
  },
  {
    id: "content-brief-generator",
    title: "Content Brief Generator",
    description: "Generate comprehensive content briefs from client communications and requirements.",
    category: "Sales & Marketing",
    icon: Code2,
    path: "/use-cases/content-brief-generator"
  }
];

const Index = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");

  const filteredUseCases = useCases.filter(useCase => {
    const matchesSearch = useCase.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         useCase.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "All" || useCase.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-slate-800 py-16">
        {/* Hero Content */}
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl lg:text-6xl font-bold mb-8 tracking-tight text-white">
              AI Planet Use Cases
            </h1>
            
            <p className="text-xl text-gray-300 mb-16 leading-relaxed">
              Explore innovative AI solutions that elevate and transform your business operations.
            </p>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <div className="bg-slate-700/50 backdrop-blur-sm border border-slate-600 rounded-xl p-8 text-center">
                <div className="text-4xl lg:text-5xl font-bold text-green-400 mb-3">38+</div>
                <div className="text-gray-300 font-medium text-lg">AI Applications</div>
              </div>
              <div className="bg-slate-700/50 backdrop-blur-sm border border-slate-600 rounded-xl p-8 text-center">
                <div className="text-4xl lg:text-5xl font-bold text-blue-400 mb-3">6+</div>
                <div className="text-gray-300 font-medium text-lg">Industry Categories</div>
              </div>
              <div className="bg-slate-700/50 backdrop-blur-sm border border-slate-600 rounded-xl p-8 text-center">
                <div className="text-4xl lg:text-5xl font-bold text-purple-400 mb-3">99.9%</div>
                <div className="text-gray-300 font-medium text-lg">Uptime SLA</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Case Explorer */}
      <section id="explorer" className="py-24 bg-slate-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              Transforming Ideas Through AI Use Cases
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Unlock AI's power with custom solutions for greater efficiency and innovation.
            </p>
          </div>

          {/* Search and Filters */}
          <div className="mb-12 space-y-6">
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
                <Input
                  type="text"
                  placeholder="Search AI use cases..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 py-4 text-lg border-slate-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex flex-wrap justify-center gap-3">
              {categories.map((category) => (
                <Badge
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  className={`px-6 py-3 text-sm font-medium cursor-pointer transition-all duration-200 rounded-full ${
                    selectedCategory === category
                      ? "bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600"
                      : "bg-white hover:bg-slate-50 text-slate-600 border-slate-200 hover:border-emerald-300"
                  }`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Badge>
              ))}
            </div>
          </div>

          {/* Use Cases Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredUseCases.map((useCase) => {
              const IconComponent = useCase.icon;
              return (
                <Link key={useCase.id} to={useCase.path}>
                  <Card className="h-full hover:shadow-lg transition-all duration-300 hover:scale-105 hover:-translate-y-2 border-slate-200 group bg-white">
                    <CardContent className="p-8">
                      <div className="flex items-center justify-between mb-6">
                        <div className="p-3 bg-slate-100 rounded-lg group-hover:bg-emerald-100 transition-colors">
                          <IconComponent className="h-6 w-6 text-slate-700 group-hover:text-emerald-600" />
                        </div>
                        <Badge variant="outline" className="text-xs bg-slate-50 text-slate-600 border-slate-200">
                          {useCase.category}
                        </Badge>
                      </div>
                      <h3 className="text-xl font-semibold text-slate-900 mb-3 group-hover:text-slate-700 transition-colors">
                        {useCase.title}
                      </h3>
                      <p className="text-slate-600 leading-relaxed text-sm">
                        {useCase.description}
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>

          {filteredUseCases.length === 0 && (
            <div className="text-center py-16">
              <div className="text-slate-400 mb-4">
                <Search className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-2">No use cases found</h3>
              <p className="text-slate-600">Try adjusting your search or filters.</p>
            </div>
          )}
        </div>
      </section>


    </div>
  );
};

export default Index;
