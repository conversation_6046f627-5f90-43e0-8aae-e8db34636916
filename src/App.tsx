import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import SentimentAnalysis from "./pages/use-cases/sentiment-analysis/SentimentAnalysis";
import LifeScienceResearch from "./pages/use-cases/life-science-research/LifeScienceResearch";
import KYCOnboarding from "./pages/use-cases/KYC-onboarding/KYCOnboarding";
import CreditCardRecommendation from "./pages/use-cases/credit-card-recommendation/CreditCardRecommendation";
import FraudDetection from "./pages/use-cases/FraudDetection/FraudDetection";
import ResumeScreening from "./pages/use-cases/ResumeScreening/ResumeScreening";
import PharmaKnowledgebase from "./pages/use-cases/PharmaKnowledgebase/PharmaKnowledgebase";
import KYCDocumentVerification from "./pages/use-cases/KYCDocumentVerification/KYCDocumentVerification";
import MarketResearch from "./pages/use-cases/MarketResearch";
import MedicalQA from "./pages/use-cases/medical-qa/MedicalQA";
import MedicalReportAnalysis from "./pages/use-cases/medical-report-analysis/MedicalReportAnalysis";
import ContentBriefGenerator from "./pages/use-cases/content-brief-generator/ContentBriefGenerator";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/use-cases/sentiment-analysis" element={<SentimentAnalysis />} />
          <Route path="/use-cases/life-science-research" element={<LifeScienceResearch />} />
          <Route path="/use-cases/kyc-onboarding" element={<KYCOnboarding />} />
          <Route path="/use-cases/credit-card-recommendation" element={<CreditCardRecommendation />} />
          <Route path="/use-cases/fraud-detection" element={<FraudDetection />} />
          <Route path="/use-cases/resume-screening" element={<ResumeScreening />} />
          <Route path="/use-cases/pharma-knowledgebase" element={<PharmaKnowledgebase />} />
          <Route path="/use-cases/kyc-document-verification" element={<KYCDocumentVerification />} />
          <Route path="/use-cases/market-research" element={<MarketResearch />} />
          <Route path="/use-cases/medical-qa" element={<MedicalQA />} />
          <Route path="/use-cases/medical-report-analysis" element={<MedicalReportAnalysis />} />
          <Route path="/use-cases/content-brief-generator" element={<ContentBriefGenerator />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
