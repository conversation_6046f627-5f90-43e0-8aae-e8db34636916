#!/bin/bash

# Docker Deployment Test Script
# This script builds and tests all Docker containers for the AI Planet apps

set -e  # Exit on any error

echo "🚀 Starting Docker Deployment Test..."
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
print_status "Checking Docker status..."
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi
print_success "Docker is running"

# Clean up any existing containers and images
print_status "Cleaning up existing containers and images..."
docker-compose down --remove-orphans 2>/dev/null || true
docker system prune -f 2>/dev/null || true

# Build all services
print_status "Building all Docker images..."
echo "This may take several minutes..."

# Build backend services
print_status "Building Medical QA service..."
docker build -t medical-qa-service ./backend/medical-qa

print_status "Building Content Brief service..."
docker build -t content-brief-service ./backend/content-brief

print_status "Building Medical Report service..."
docker build -t medical-report-service ./backend/medical-report

print_status "Building Frontend service..."
docker build -t ai-apps-frontend .

print_success "All images built successfully!"

# Start services using docker-compose
print_status "Starting all services with docker-compose..."
docker-compose up -d

# Wait for services to start
print_status "Waiting for services to start..."
sleep 30

# Check service health
print_status "Checking service health..."

# Function to check service health
check_service() {
    local service_name=$1
    local port=$2
    local endpoint=$3
    
    print_status "Checking $service_name on port $port..."
    
    # Wait up to 60 seconds for service to be ready
    for i in {1..12}; do
        if curl -f -s "http://localhost:$port$endpoint" > /dev/null 2>&1; then
            print_success "$service_name is healthy"
            return 0
        fi
        print_status "Waiting for $service_name... (attempt $i/12)"
        sleep 5
    done
    
    print_error "$service_name failed health check"
    return 1
}

# Check all services
check_service "Medical QA" 3001 "/health"
check_service "Content Brief" 3002 "/health"
check_service "Medical Report" 3003 "/health"
check_service "Frontend" 3000 "/"

# Show running containers
print_status "Current running containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Show logs for any failed services
print_status "Checking for any service errors..."
for service in medical-qa content-brief medical-report frontend; do
    if ! docker-compose ps $service | grep -q "Up"; then
        print_warning "Service $service may have issues. Showing logs:"
        docker-compose logs --tail=20 $service
    fi
done

print_success "Docker deployment test completed!"
echo ""
echo "🌐 Access URLs:"
echo "   Frontend:        http://localhost:3000"
echo "   Medical QA:      http://localhost:3001"
echo "   Content Brief:   http://localhost:3002"
echo "   Medical Report:  http://localhost:3003"
echo ""
echo "📋 Useful commands:"
echo "   View logs:       docker-compose logs -f [service-name]"
echo "   Stop services:   docker-compose down"
echo "   Restart:         docker-compose restart [service-name]"
echo "   Shell access:    docker-compose exec [service-name] /bin/bash"
echo ""
echo "✅ All services should now be running and accessible!"
