# 🐳 Docker Deployment Guide

## ✅ DOCKER SETUP COMPLETE!

All Docker files have been created and configured for your 3 AI applications:

### 📦 Created Docker Files

1. **Backend Services:**
   - `backend/medical-qa/Dockerfile` (Port 3001)
   - `backend/content-brief/Dockerfile` (Port 3002)
   - `backend/medical-report/Dockerfile` (Port 3003)

2. **Frontend:**
   - `Dockerfile` (Port 3000)
   - `nginx.conf` (Nginx configuration)

3. **Orchestration:**
   - `docker-compose.yml` (All services)

4. **Environment Files:**
   - `backend/medical-qa/.env` (with your API keys)
   - `backend/content-brief/.env` (with your API keys)
   - `backend/medical-report/.env` (with your API keys)

### 🚀 Port Configuration (3000-30005 range)

- **Frontend**: Port 3000
- **Medical QA**: Port 3001
- **Content Brief**: Port 3002
- **Medical Report**: Port 3003

### 🔧 Prerequisites

1. **Start Docker Desktop:**
   ```bash
   open -a Docker
   ```
   Wait for Docker to fully start (green icon in menu bar)

2. **Verify Docker is running:**
   ```bash
   docker ps
   ```

### 🚀 Quick Start

1. **Build and start all services:**
   ```bash
   ./test_docker_deployment.sh
   ```

2. **Or manually with docker-compose:**
   ```bash
   docker-compose up -d
   ```

### 🧪 Testing

1. **Run comprehensive test:**
   ```bash
   ./test_docker_deployment.sh
   ```

2. **Quick health check:**
   ```bash
   ./quick_test.sh
   ```

3. **Manual testing:**
   ```bash
   # Check service health
   curl http://localhost:3001/health  # Medical QA
   curl http://localhost:3002/health  # Content Brief
   curl http://localhost:3003/health  # Medical Report
   curl http://localhost:3000         # Frontend
   ```

### 🌐 Access URLs

- **Frontend Application**: http://localhost:3000
- **Medical QA API**: http://localhost:3001/docs
- **Content Brief API**: http://localhost:3002/docs
- **Medical Report API**: http://localhost:3003/docs

### 📋 Useful Commands

```bash
# View all containers
docker ps

# View logs
docker-compose logs -f [service-name]

# Restart a service
docker-compose restart [service-name]

# Stop all services
docker-compose down

# Rebuild a service
docker-compose build [service-name]

# Shell access to container
docker-compose exec [service-name] /bin/bash

# View resource usage
docker stats
```

### 🔍 Troubleshooting

1. **Service won't start:**
   ```bash
   docker-compose logs [service-name]
   ```

2. **Port conflicts:**
   ```bash
   lsof -i :3000  # Check what's using port 3000
   ```

3. **Rebuild everything:**
   ```bash
   docker-compose down
   docker system prune -f
   docker-compose up --build -d
   ```

4. **Check API keys:**
   ```bash
   docker-compose exec medical-qa env | grep LITELLM
   ```

### 🔐 Environment Variables

All API keys are configured in the `.env` files:

- **LiteLLM**: `sk-V12plNmxne0F7XIQuyzJDQ`
- **Azure**: Configured with your endpoints and keys
- **Qdrant**: Configured with your cloud instance
- **HuggingFace**: Configured with your token

### 📊 Service Architecture

```
Frontend (3000) → Nginx → API Proxy
    ↓
Medical QA (3001)     ← LiteLLM, Azure, Qdrant
Content Brief (3002)  ← LiteLLM, HuggingFace
Medical Report (3003) ← LiteLLM, Azure, Qdrant
```

### ✅ Next Steps

1. Start Docker Desktop
2. Run `./test_docker_deployment.sh`
3. Access http://localhost:3000
4. Test all 3 applications

### 🎯 Production Ready

- All services containerized
- Health checks configured
- Proper networking setup
- Environment variables secured
- Nginx reverse proxy configured
- Auto-restart policies enabled

**Your Docker deployment is ready to test!** 🚀
