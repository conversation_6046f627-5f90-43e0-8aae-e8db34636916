#!/bin/bash

# Monitoring Script for Deployed Services
# Run this to check the status of all services

echo "📊 AI Apps Monitoring Dashboard"
echo "================================"

# Check server resources
echo ""
echo "🖥️ Server Resources:"
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "Memory Usage: $(free -m | awk 'NR==2{printf "%.1f%%", $3*100/$2 }')"
echo "Disk Usage: $(df -h / | awk 'NR==2 {print $5}')"

# Check PM2 services
echo ""
echo "🔧 PM2 Services Status:"
pm2 list

# Check service health endpoints
echo ""
echo "🏥 Service Health Checks:"

# Function to check service health
check_health() {
    local service_name=$1
    local port=$2
    local url="http://*************:$port/health"
    
    echo -n "  $service_name ($port): "
    
    if curl -s --max-time 5 "$url" > /dev/null 2>&1; then
        echo "✅ Healthy"
    else
        echo "❌ Unhealthy"
    fi
}

check_health "Medical QA" 8001
check_health "Content Brief" 8002
check_health "Medical Report" 8003

# Check frontend
echo -n "  Frontend (3000): "
if curl -s --max-time 5 "http://*************:3000" > /dev/null 2>&1; then
    echo "✅ Healthy"
else
    echo "❌ Unhealthy"
fi

# Show recent logs
echo ""
echo "📝 Recent Logs (last 10 lines):"
echo "================================"

for service in medical-qa content-brief medical-report ai-apps-frontend; do
    echo ""
    echo "--- $service ---"
    pm2 logs $service --lines 5 --nostream 2>/dev/null || echo "No logs available"
done

echo ""
echo "🔗 Access URLs:"
echo "  Frontend: http://*************:3000"
echo "  Medical QA API: http://*************:8001/docs"
echo "  Content Brief API: http://*************:8002/docs"
echo "  Medical Report API: http://*************:8003/docs"

echo ""
echo "⚙️ Management Commands:"
echo "  Restart all: pm2 restart all"
echo "  Stop all: pm2 stop all"
echo "  View logs: pm2 logs"
echo "  Monitor: pm2 monit"
