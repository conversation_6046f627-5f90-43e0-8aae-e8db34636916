#!/bin/bash

# Hetzner Deployment Script for 3 AI Apps
# Server IP: *************
# Apps: Content Brief Generator, Medical QA, Medical Report Analysis

set -e

# Configuration
SERVER_IP="*************"
PEM_FILE="Weaviate instance (1).pem"
SERVER_USER="root"
PROJECT_NAME="ai-planet-apps"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting deployment to Hetzner server...${NC}"

# Step 1: Set PEM file permissions
echo -e "${YELLOW}📋 Setting PEM file permissions...${NC}"
chmod 600 "$PEM_FILE"

# Step 2: Test connection
echo -e "${YELLOW}🔗 Testing server connection...${NC}"
ssh -i "$PEM_FILE" -o ConnectTimeout=10 $SERVER_USER@$SERVER_IP "echo 'Connection successful!'"

# Step 3: Create project directory on server
echo -e "${YELLOW}📁 Creating project directory on server...${NC}"
ssh -i "$PEM_FILE" $SERVER_USER@$SERVER_IP "mkdir -p /root/$PROJECT_NAME"

# Step 4: Copy project files (excluding node_modules and venv)
echo -e "${YELLOW}📤 Uploading project files...${NC}"
rsync -avz -e "ssh -i '$PEM_FILE'" \
  --exclude 'node_modules' \
  --exclude 'backend/*/venv' \
  --exclude 'backend/*/__pycache__' \
  --exclude '.git' \
  --exclude '*.pem' \
  ./ $SERVER_USER@$SERVER_IP:/root/$PROJECT_NAME/

# Step 5: Run server setup script
echo -e "${YELLOW}⚙️ Setting up server environment...${NC}"
ssh -i "$PEM_FILE" $SERVER_USER@$SERVER_IP "cd /root/$PROJECT_NAME && chmod +x server_setup.sh && ./server_setup.sh"

# Step 6: Deploy each backend service
echo -e "${YELLOW}🔧 Deploying backend services...${NC}"
ssh -i "$PEM_FILE" $SERVER_USER@$SERVER_IP "cd /root/$PROJECT_NAME && chmod +x deploy_backends.sh && ./deploy_backends.sh"

# Step 7: Deploy frontend
echo -e "${YELLOW}🎨 Deploying frontend...${NC}"
ssh -i "$PEM_FILE" $SERVER_USER@$SERVER_IP "cd /root/$PROJECT_NAME && chmod +x deploy_frontend.sh && ./deploy_frontend.sh"

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}🌐 Your apps are now accessible at:${NC}"
echo -e "${GREEN}   Frontend: http://$SERVER_IP:3000${NC}"
echo -e "${GREEN}   Medical QA API: http://$SERVER_IP:8001${NC}"
echo -e "${GREEN}   Content Brief API: http://$SERVER_IP:8002${NC}"
echo -e "${GREEN}   Medical Report API: http://$SERVER_IP:8003${NC}"
echo -e "${GREEN}   API Docs: http://$SERVER_IP:800X/docs (replace X with 1,2,3)${NC}"

echo -e "${YELLOW}📝 Next steps:${NC}"
echo -e "1. Set up your environment variables on the server"
echo -e "2. Test each service individually"
echo -e "3. Share the URLs with your team for testing"
