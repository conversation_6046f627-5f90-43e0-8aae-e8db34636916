# 🚀 Hetzner Deployment Guide

This guide will help you deploy your 3 AI applications to Hetzner cloud for testing.

## 📋 Prerequisites

- Hetzner server with IP: `*************`
- PEM file: `Weaviate instance (1).pem` (already in directory)
- Your API keys for various services

## 🎯 Applications to Deploy

1. **Medical QA** - Port 8001
2. **Content Brief Generator** - Port 8002  
3. **Medical Report Analysis** - Port 8003
4. **Frontend** - Port 3000

## 🚀 Quick Deployment

### Step 1: Run Deployment Script
```bash
./deploy.sh
```

This will:
- Set up the server environment
- Deploy all 3 backend services
- Deploy the React frontend
- Set up PM2 process management

### Step 2: Configure Environment Variables

After deployment, SSH into the server:
```bash
ssh -i "Weaviate instance (1).pem" root@*************
```

Then run the environment setup:
```bash
cd /root/ai-planet-apps
./setup_env_vars.sh
```

Edit each environment file with your API keys:
```bash
# Medical QA
nano /root/ai-planet-apps/backend/medical-qa/.env

# Content Brief Generator  
nano /root/ai-planet-apps/backend/content-brief/.env

# Medical Report Analysis
nano /root/ai-planet-apps/backend/medical-report/.env
```

### Step 3: Restart Services
```bash
pm2 restart all
```

## 🌐 Access Your Apps

After successful deployment:

- **Frontend**: http://*************:3000
- **Medical QA API**: http://*************:8001/docs
- **Content Brief API**: http://*************:8002/docs  
- **Medical Report API**: http://*************:8003/docs

## 📊 Monitoring

Check service status:
```bash
./monitor.sh
```

Or use PM2 commands:
```bash
pm2 list          # List all services
pm2 logs          # View logs
pm2 monit         # Real-time monitoring
pm2 restart all   # Restart all services
```

## 🔑 Required Environment Variables

### Content Brief Generator
```
LITELLM_API_KEY=your_key_here
LITELLM_BASE_URL=https://litellm.aiplanet.com
HUGGINGFACE_HUB_TOKEN=your_token_here
```

### Medical QA & Medical Report
```
LITELLM_API_KEY=your_key_here
LITELLM_BASE_URL=https://litellm.aiplanet.com
AZURE_ENDPOINT=your_endpoint_here
AZURE_KEY=your_key_here
OPENAI_API_KEY=your_key_here
AZURE_DEPLOYMENT=your_deployment_here
AZURE_OPENAI_ENDPOINT=your_endpoint_here
QDRANT_URL=your_qdrant_url_here
QDRANT_API_KEY=your_qdrant_key_here
```

## 🔧 Troubleshooting

### Service Not Starting
```bash
pm2 logs service-name
pm2 restart service-name
```

### Check Service Health
```bash
curl http://*************:8001/health
curl http://*************:8002/health
curl http://*************:8003/health
```

### Update Code
```bash
# On your local machine
./deploy.sh

# On server
pm2 restart all
```

## 📤 Sharing for Testing

Share these URLs with your team:

1. **Main Application**: http://*************:3000
2. **API Documentation**: 
   - Medical QA: http://*************:8001/docs
   - Content Brief: http://*************:8002/docs
   - Medical Report: http://*************:8003/docs

## 🛡️ Security Notes

- All API keys are stored in environment files on the server
- No sensitive data is committed to the repository
- Services are managed by PM2 with auto-restart
- Logs are stored in `/var/log/ai-apps/`
