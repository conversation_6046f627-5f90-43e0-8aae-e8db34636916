#!/bin/bash

# Quick validation script for Docker setup
echo "🔍 Validating Docker setup for the 3 services..."
echo "================================================"

# Check if all required files exist
SERVICES=("medical-qa" "content-brief" "medical-report")
PORTS=("3001" "3002" "3003")
ISSUES=0

for i in "${!SERVICES[@]}"; do
    service="${SERVICES[$i]}"
    port="${PORTS[$i]}"
    
    echo "📋 Checking $service service..."
    
    # Check directory
    if [ ! -d "backend/$service" ]; then
        echo "❌ Directory backend/$service not found"
        ((ISSUES++))
        continue
    fi
    
    # Check Dockerfile
    if [ ! -f "backend/$service/Dockerfile" ]; then
        echo "❌ Dockerfile missing for $service"
        ((ISSUES++))
    else
        echo "✅ Dockerfile found"
    fi
    
    # Check requirements.txt
    if [ ! -f "backend/$service/requirements.txt" ]; then
        echo "❌ requirements.txt missing for $service"
        ((ISSUES++))
    else
        echo "✅ requirements.txt found"
    fi
    
    # Check main.py
    if [ ! -f "backend/$service/main.py" ]; then
        echo "❌ main.py missing for $service"
        ((ISSUES++))
    else
        echo "✅ main.py found"
    fi
    
    # Check .env file (create if missing)
    if [ ! -f "backend/$service/.env" ]; then
        echo "⚠️  .env file missing - creating template"
        cat > "backend/$service/.env" << EOF
# Environment variables for $service
API_HOST=0.0.0.0
API_PORT=$port
# Add your API keys here
EOF
        echo "✅ Template .env file created"
    else
        echo "✅ .env file found"
    fi
    
    echo ""
done

# Check docker-compose.yml
echo "📋 Checking docker-compose.yml..."
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found"
    ((ISSUES++))
else
    echo "✅ docker-compose.yml found"
fi

echo "================================================"
if [ $ISSUES -eq 0 ]; then
    echo "🎉 All files are ready for Docker testing!"
    echo ""
    echo "Once Docker is installed, you can:"
    echo "1. Test individual services: ./test_individual_service.sh medical-qa"
    echo "2. Test all services: ./test_docker_services.sh"
    echo "3. Use docker-compose: docker-compose up --build"
else
    echo "⚠️  Found $ISSUES issues that need to be fixed"
fi
echo "================================================"
