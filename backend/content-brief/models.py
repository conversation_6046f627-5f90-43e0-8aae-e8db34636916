"""
Pydantic models for Content Brief Generator API
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from enum import Enum


class ContentBriefRequest(BaseModel):
    """Request model for content brief generation"""
    query: str = Field(..., min_length=1, max_length=5000, description="The content brief query or requirement")
    session_id: str = Field(..., description="Session ID for the uploaded documents")


class DocumentUploadResponse(BaseModel):
    """Response model for document upload"""
    success: bool = Field(..., description="Whether the upload was successful")
    message: str = Field(..., description="Status message")
    session_id: str = Field(..., description="Session ID for the uploaded documents")
    document_count: int = Field(..., description="Number of documents processed")
    processed_files: List[str] = Field(..., description="List of processed file names")


class ContentBriefResponse(BaseModel):
    """Response model for content brief generation"""
    success: bool = Field(..., description="Whether the generation was successful")
    content_brief: str = Field(..., description="The generated content brief")
    context_analysis: Optional[str] = Field(default=None, description="The context intelligence analysis section")
    executive_brief: Optional[str] = Field(default=None, description="The executive summary and strategic brief section")
    session_id: str = Field(..., description="Session ID used for generation")
    timestamp: str = Field(..., description="Generation timestamp")
    query_used: str = Field(..., description="The query that was used for generation")


class HealthCheckResponse(BaseModel):
    """Response model for health check"""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="API version")
    timestamp: str = Field(..., description="Current timestamp")
    services: Dict[str, str] = Field(..., description="Status of dependent services")


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")


class ClearDocumentResponse(BaseModel):
    """Response model for clearing documents"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Status message")
    cleared_session: Optional[str] = Field(default=None, description="ID of cleared session")
