"""
Configuration management for Content Brief Generator API
"""

from typing import List
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings"""

    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 3002
    debug: bool = True

    # LiteLLM Configuration
    litellm_api_key: str
    litellm_base_url: str
    litellm_model: str = "gpt-4o-mini"

    # HuggingFace Configuration
    huggingface_hub_token: str
    
    # CORS Configuration
    allowed_origins: List[str] = [
        "http://localhost:8080",
        "http://localhost:3000", 
        "http://127.0.0.1:8080"
    ]
    
    # LLM Configuration
    llm_temperature: float = 0.1
    llm_max_tokens: int = 4000
    
    # Text Splitting Configuration
    chunk_size: int = 1000
    chunk_overlap: int = 200
    
    # Retrieval Configuration
    retrieval_k: int = 4
    
    # Whisper Configuration
    whisper_model_size: str = "base"
    whisper_device: str = "cpu"
    whisper_compute_type: str = "int8"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
