"""
Content Brief Service - Core business logic for content brief generation
"""

import logging
import tempfile
import os
import uuid
import mimetypes
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from email import policy
from email.parser import BytesParser
import PyPDF2

from fastapi import UploadFile
from langchain.schema import HumanMessage, Document
from langchain_huggingface.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import Annotated, TypedDict
import litellm
from faster_whisper import WhisperModel

from config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Configure LiteLLM
litellm.api_base = settings.litellm_base_url
litellm.api_key = settings.litellm_api_key


class AgentState(TypedDict):
    messages: Annotated[list, add_messages]
    documents: List[Document]
    embeddings_created: bool
    retrieved_docs: List[str]
    content_brief: Dict[str, Any]


class ContentBriefService:
    def __init__(self):
        """Initialize the content brief service"""
        self.embeddings = None
        self.whisper_model = None
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap
        )
        self.sessions: Dict[str, Dict] = {}  # Store session data
        self._initialize_models()

    def _initialize_models(self):
        """Initialize the embedding and whisper models"""
        try:
            # Initialize embeddings
            self.embeddings = HuggingFaceEmbeddings(
                model_name="sentence-transformers/all-MiniLM-L6-v2"
            )
            logger.info("Embeddings model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load embeddings model: {str(e)}")

        try:
            # Initialize Whisper model
            self.whisper_model = WhisperModel(
                settings.whisper_model_size,
                device=settings.whisper_device,
                compute_type=settings.whisper_compute_type
            )
            logger.info("Whisper model loaded successfully")
        except Exception as e:
            logger.warning(f"Could not load Whisper model: {str(e)}")

    def process_email(self, email_content: bytes) -> str:
        """Process email content and extract text"""
        try:
            msg = BytesParser(policy=policy.default).parsebytes(email_content)
            text_content = []
            
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        text_content.append(part.get_content())
            else:
                text_content.append(msg.get_content())
            
            return "\n".join(text_content)
        except Exception as e:
            logger.error(f"Error processing email: {str(e)}")
            return ""

    async def process_uploaded_files(self, uploaded_files: List[UploadFile]) -> Tuple[List[Document], str]:
        """Process uploaded files and convert to documents"""
        documents = []
        session_id = str(uuid.uuid4())
        
        for uploaded_file in uploaded_files:
            # Create a temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.filename.split('.')[-1]}") as tmp_file:
                content = await uploaded_file.read()
                tmp_file.write(content)
                tmp_file_path = tmp_file.name
            
            try:
                file_content = ""
                mime_type, _ = mimetypes.guess_type(uploaded_file.filename)
                
                if mime_type == 'message/rfc822':  # Email
                    file_content = self.process_email(content)
                elif mime_type == 'application/pdf':
                    with open(tmp_file_path, 'rb') as pdf_file:
                        pdf_reader = PyPDF2.PdfReader(pdf_file)
                        page_contents = []
                        for page in pdf_reader.pages:
                            page_content = page.extract_text()
                            if page_content:
                                page_contents.append(page_content)
                        file_content = "\n---\n".join(page_contents)
                elif mime_type and mime_type.startswith('text/'):
                    file_content = content.decode("utf-8", errors="ignore")
                elif uploaded_file.content_type in ['application/json', 'text/csv']:
                    file_content = content.decode("utf-8", errors="ignore")
                elif mime_type and mime_type.startswith('audio/'):
                    if self.whisper_model:
                        try:
                            segments, info = self.whisper_model.transcribe(tmp_file_path, beam_size=5)
                            transcribed_text = ""
                            for segment in segments:
                                transcribed_text += segment.text + " "
                            file_content = transcribed_text.strip()
                            logger.info(f"Transcription complete for {uploaded_file.filename}")
                        except Exception as e:
                            logger.error(f"Error transcribing audio file: {str(e)}")
                            continue
                    else:
                        logger.warning("Whisper model not available for audio transcription")
                        continue
                else:
                    logger.warning(f"Unsupported file type: {uploaded_file.filename}")
                    continue
                
                if file_content:
                    doc = Document(
                        page_content=file_content,
                        metadata={
                            "source": uploaded_file.filename,
                            "upload_time": datetime.now().isoformat(),
                            "type": mime_type
                        }
                    )
                    documents.append(doc)
                
            except Exception as e:
                logger.error(f"Error processing {uploaded_file.filename}: {str(e)}")
            finally:
                if os.path.exists(tmp_file_path):
                    os.unlink(tmp_file_path)
        
        # Store documents in session
        self.sessions[session_id] = {
            "documents": documents,
            "vector_store": None,
            "created_at": datetime.now().isoformat()
        }
        
        return documents, session_id

    def create_embeddings_node(self, state: AgentState) -> AgentState:
        """Node to create embeddings from uploaded documents"""
        documents = state.get("documents", [])

        if not documents:
            return {**state, "embeddings_created": False}

        try:
            # Split documents into chunks
            split_docs = self.text_splitter.split_documents(documents)

            # Create vector store
            vector_store = FAISS.from_documents(split_docs, self.embeddings)

            # Store vector store in session (we'll need session_id from context)
            return {**state, "embeddings_created": True, "vector_store": vector_store}
        except Exception as e:
            logger.error(f"Error creating embeddings: {str(e)}")
            return {**state, "embeddings_created": False}

    def document_retriever_node(self, state: AgentState) -> AgentState:
        """Node to retrieve relevant documents based on query"""
        messages = state.get("messages", [])
        vector_store = state.get("vector_store")

        if not messages or not vector_store:
            return {**state, "retrieved_docs": []}

        try:
            # Get the latest user message
            user_query = messages[-1].content if messages else ""

            # Retrieve similar documents
            docs = vector_store.similarity_search(user_query, k=settings.retrieval_k)
            retrieved_content = [doc.page_content for doc in docs]

            return {**state, "retrieved_docs": retrieved_content}
        except Exception as e:
            logger.error(f"Error retrieving documents: {str(e)}")
            return {**state, "retrieved_docs": []}

    def llm_processing_node(self, state: AgentState) -> AgentState:
        """Node to process retrieved documents and generate content brief"""
        messages = state.get("messages", [])
        retrieved_docs = state.get("retrieved_docs", [])

        if not messages:
            return {**state, "content_brief": {}}

        user_query = messages[-1].content if messages else ""
        context = "\n\n".join(retrieved_docs)

        # First, analyze the context to understand what we're working with
        analysis_prompt = f"""
        Analyze this client data and provide a strategic assessment:

        CONTEXT DATA:
        {context}

        CLIENT REQUEST: {user_query}

        Provide a brief analysis covering:
        1. Industry/Business Type: What business is this?
        2. Key Stakeholders: Who are the main people involved?
        3. Project Phase: What stage are they in?
        4. Critical Challenges: What problems need solving?
        5. Success Metrics: How do they measure success?
        6. Competitive Context: Who are they up against?
        7. Strategic Opportunities: What advantages can they leverage?

        Keep it concise but insightful.
        """

        try:
            # Get context analysis first
            analysis_response = litellm.completion(
                model=settings.litellm_model,
                messages=[{"role": "user", "content": analysis_prompt}],
                api_base=settings.litellm_base_url,
                api_key=settings.litellm_api_key
            )

            context_analysis = analysis_response.choices[0].message.content

        except Exception as e:
            context_analysis = "Context analysis unavailable due to processing error."
            logger.error(f"Context analysis failed: {str(e)}")

        return self._generate_main_brief(state, user_query, context, context_analysis)

    def _generate_main_brief(self, state: AgentState, user_query: str, context: str, context_analysis: str) -> AgentState:
        """Generate the main content brief"""
        # Now create the comprehensive brief
        main_prompt = f"""
        CONTEXT INTELLIGENCE REPORT:
        {context_analysis}

        RAW CLIENT COMMUNICATIONS:
        {context}

        CLIENT OBJECTIVE: {user_query}

        Based on this intelligence, create a strategic content brief that delivers genuine competitive advantage. This isn't generic marketing advice - it's strategic intelligence extracted from real client data.

        IMPORTANT FORMATTING REQUIREMENTS:
        - Use ONLY plain text formatting
        - NO markdown syntax (no #, *, **, -, etc.)
        - Use clear section headers in ALL CAPS
        - Use simple line breaks and spacing for structure
        - Each section should be clearly separated
        - Use colons after labels (e.g., "Business Context:")

        STRATEGIC CONTENT INTELLIGENCE BRIEF

        EXECUTIVE SUMMARY
        Business Context: [One-line description of their business and current situation]
        Primary Objective: [What they're trying to achieve based on communications]
        Strategic Recommendation: [Your number 1 strategic advice based on analysis]
        Expected Impact: [Predicted business outcome if executed properly]

        COMPETITIVE POSITIONING ANALYSIS
        Market Position: [Where they stand vs competitors based on data]
        Unique Advantages: [Specific strengths mentioned in communications]
        Competitive Threats: [What they're up against - extracted from context]
        Market Opportunity: [Gaps they can exploit based on conversations]
        Differentiation Strategy: [How to stand out based on their specific situation]

        TARGET AUDIENCE INTELLIGENCE
        Primary Segment: [Specific demographics/psychographics from data]
        Decision Makers: [Who actually makes the buying decisions]
        Pain Points: [Specific problems they solve - not generic frustrations]
        Buying Triggers: [What makes prospects ready to purchase]
        Objections & Concerns: [Specific hesitations mentioned in communications]

        STRATEGIC MESSAGING FRAMEWORK
        Core Value Proposition: [The compelling reason to choose them over alternatives]
        Message Architecture: [How to layer messages for maximum impact]
        Proof Elements: [Specific credibility indicators from their data]
        Emotional Drivers: [What emotionally motivates their audience]
        Rational Justifiers: [Logical reasons to support the emotional decision]

        CONTENT STRATEGY & EXECUTION
        Content Themes: [3-4 strategic content pillars based on their strengths]
        Format Strategy: [Best content types for their specific audience/industry]
        Content Calendar: [Strategic timing based on their business cycles]
        Distribution Channels: [Where their audience actually consumes content]
        Content Optimization: [How to maximize engagement and conversion]

        CONVERSION & CTA STRATEGY
        Primary Call-to-Action: [Main action you want prospects to take]
        Secondary CTAs: [Supporting actions that nurture prospects]
        CTA Placement Strategy: [Where and when to present CTAs for maximum impact]
        Conversion Funnel: [Step-by-step path from awareness to action]
        Urgency & Scarcity: [How to create appropriate urgency without being pushy]
        Success Metrics: [How to measure CTA effectiveness]

        TACTICAL EXECUTION ROADMAP
        Week 1-2 (Foundation):
        [Specific tasks to establish foundation - based on their current state]

        Week 3-4 (Launch):
        [Specific launch activities - tailored to their resources and timeline]

        Month 2-3 (Optimization):
        [Specific optimization and scaling activities based on early results]

        Ongoing (Systems):
        [Sustainable systems and processes for long-term success]

        RISK MITIGATION & CONTINGENCIES
        Potential Risks: [Specific challenges they might face based on context]
        Mitigation Strategies: [How to prevent or address each risk]
        Plan B Scenarios: [Alternative approaches if primary strategy doesn't work]
        Early Warning Indicators: [Metrics that signal problems before they become serious]

        MEASUREMENT & OPTIMIZATION
        Key Performance Indicators: [Specific metrics that matter for their business]
        Tracking Setup: [How to measure success - tools and processes]
        Optimization Triggers: [When and how to adjust strategy based on data]
        Reporting Framework: [How to present results to stakeholders]
        Success Benchmarks: [Specific targets to aim for based on industry/context]

        COMPETITIVE INTELLIGENCE ACTIONS
        Competitor Monitoring: [Who to watch and what to track]
        Market Research: [Specific research needed to maintain advantage]
        Innovation Opportunities: [Areas where they can lead the market]
        Partnership Potential: [Strategic alliances that could accelerate growth]

        RESOURCE REQUIREMENTS & BUDGET
        Team Requirements: [Specific roles needed based on their scale]
        Technology Stack: [Tools and platforms required for execution]
        Budget Allocation: [How to prioritize spending for maximum ROI]
        Timeline Considerations: [Critical deadlines and milestones]

        NEXT STEPS & IMMEDIATE ACTIONS
        This Week: [3-5 specific actions to take immediately]
        This Month: [Key milestones to achieve in 30 days]
        Next Quarter: [Strategic objectives for next 90 days]
        Decision Points: [Key decisions that need to be made and when]

        CRITICAL REQUIREMENTS:
        - Extract ALL insights from actual client communications only
        - Be hyper-specific to their industry, scale, and situation
        - Provide actionable intelligence with specific steps and timelines
        - Include concrete numbers, metrics, and deliverables where available
        - Focus on competitive advantage and market differentiation
        - Address their specific challenges and opportunities
        - If specific information isn't in communications, state "Not specified in client communications"
        - Avoid generic marketing advice - focus on strategic intelligence
        - Include specific CTAs and conversion strategies based on their business model
        - Provide clean text output without markdown formatting (no hashtags, asterisks, etc.)

        Strategic Brief Generated: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}
        Analysis Based On: Client communications and strategic assessment
        """

        try:
            response = litellm.completion(
                model=settings.litellm_model,
                messages=[{"role": "user", "content": main_prompt}],
                api_base=settings.litellm_base_url,
                api_key=settings.litellm_api_key,
                temperature=settings.llm_temperature,
                max_tokens=settings.llm_max_tokens
            )

            content = response.choices[0].message.content

            # Clean the content to remove markdown formatting
            content = self._clean_markdown_formatting(content)

            # Clean the context analysis as well
            cleaned_context_analysis = self._clean_markdown_formatting(context_analysis)

            # Create two separate sections as requested
            context_section = f"""CONTEXT INTELLIGENCE ANALYSIS

{cleaned_context_analysis}"""

            executive_section = f"""STRATEGIC CONTENT BRIEF

{content}"""

            content_brief = {
                "context_analysis": context_section,
                "executive_brief": executive_section,
                "raw_content": f"{context_section}\n\n{'='*80}\n\n{executive_section}"
            }

        except Exception as e:
            logger.error(f"LLM processing failed: {str(e)}")
            content_brief = {"error": f"Failed to generate strategic brief: {str(e)}"}

        return {**state, "content_brief": content_brief}

    def _clean_markdown_formatting(self, text: str) -> str:
        """Clean markdown formatting from text to provide plain text output"""
        import re

        # Remove markdown headers (# ## ### ####)
        text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)

        # Remove bold formatting (**text**)
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)

        # Remove italic formatting (*text*)
        text = re.sub(r'\*(.*?)\*', r'\1', text)

        # Remove code blocks (```text```)
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)

        # Remove inline code (`text`)
        text = re.sub(r'`(.*?)`', r'\1', text)

        # Remove markdown lists (- item)
        text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)

        # Remove numbered lists (1. item)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)

        # Clean up extra whitespace
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)

        return text.strip()

    def create_workflow(self, session_id: str):
        """Create the LangGraph workflow"""
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("create_embeddings", self.create_embeddings_node)
        workflow.add_node("retrieve_documents", self.document_retriever_node)
        workflow.add_node("process_with_llm", self.llm_processing_node)

        # Set entry point
        workflow.set_entry_point("create_embeddings")

        # Add edges
        workflow.add_edge("create_embeddings", "retrieve_documents")
        workflow.add_edge("retrieve_documents", "process_with_llm")
        workflow.add_edge("process_with_llm", END)

        return workflow.compile()

    async def generate_content_brief(self, query: str, session_id: str) -> Dict[str, Any]:
        """Generate content brief from query and session documents"""
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")

        session_data = self.sessions[session_id]
        documents = session_data["documents"]

        if not documents:
            raise ValueError("No documents found in session")

        try:
            # Create workflow
            workflow = self.create_workflow(session_id)

            # Prepare initial state
            initial_state = {
                "messages": [HumanMessage(content=query)],
                "documents": documents,
                "embeddings_created": False,
                "retrieved_docs": [],
                "content_brief": {}
            }

            # Execute workflow
            result = workflow.invoke(initial_state)
            content_brief = result.get("content_brief", {})

            if "error" in content_brief:
                raise Exception(content_brief["error"])

            return {
                "success": True,
                "content_brief": content_brief.get("raw_content", ""),
                "context_analysis": content_brief.get("context_analysis", ""),
                "executive_brief": content_brief.get("executive_brief", ""),
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "query_used": query
            }

        except Exception as e:
            logger.error(f"Content brief generation failed: {str(e)}")
            return {
                "success": False,
                "content_brief": f"Error generating content brief: {str(e)}",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "query_used": query
            }

    def clear_session(self, session_id: str) -> bool:
        """Clear a document session"""
        try:
            if session_id in self.sessions:
                del self.sessions[session_id]
                logger.info(f"Session {session_id} cleared successfully")
                return True
            else:
                logger.warning(f"Session {session_id} not found")
                return False
        except Exception as e:
            logger.error(f"Error clearing session {session_id}: {str(e)}")
            return False


# Global service instance
content_brief_service = ContentBriefService()
