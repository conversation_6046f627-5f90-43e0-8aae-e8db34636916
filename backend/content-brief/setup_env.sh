#!/bin/bash

# Setup script for Content Brief Generator backend

echo "Setting up Content Brief Generator backend environment..."

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install requirements
pip install -r requirements.txt

echo "Environment setup complete!"
echo "To activate the environment, run: source venv/bin/activate"
echo "To start the server, run: ./start_server.sh"
