"""
FastAPI main application for Content Brief Generator
"""

import logging
from datetime import datetime
from typing import List, Optional
import uuid

from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from config import settings
from models import (
    ContentBriefRequest,
    ContentBriefResponse,
    DocumentUploadResponse,
    HealthCheckResponse,
    ErrorResponse,
    ClearDocumentResponse
)
from content_brief_service import content_brief_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Content Brief Generator API",
    description="AI-powered content brief generation from client communications",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Global exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            message="An unexpected error occurred",
            details={"exception": str(exc)}
        ).dict()
    )


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check service status
        services_status = {
            "embeddings": "healthy" if content_brief_service.embeddings else "unhealthy",
            "whisper": "healthy" if content_brief_service.whisper_model else "unhealthy",
            "litellm": "healthy"
        }
        
        return HealthCheckResponse(
            status="healthy",
            version="1.0.0",
            timestamp=datetime.now().isoformat(),
            services=services_status
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Health check failed")


@app.post("/upload-documents", response_model=DocumentUploadResponse)
async def upload_documents(files: List[UploadFile] = File(...)):
    """Upload and process client communication documents"""
    try:
        if not files:
            raise HTTPException(status_code=400, detail="No files uploaded")
        
        # Process the documents
        documents, session_id = await content_brief_service.process_uploaded_files(files)
        
        if not documents:
            raise HTTPException(status_code=400, detail="No valid documents could be processed")
        
        return DocumentUploadResponse(
            success=True,
            message=f"{len(documents)} documents processed successfully",
            session_id=session_id,
            document_count=len(documents),
            processed_files=[doc.metadata.get("source", "Unknown") for doc in documents]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Document processing failed: {str(e)}")


@app.post("/generate-brief", response_model=ContentBriefResponse)
async def generate_content_brief(request: ContentBriefRequest):
    """Generate content brief from uploaded documents and query"""
    try:
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        # Generate the content brief
        result = await content_brief_service.generate_content_brief(
            query=request.query,
            session_id=request.session_id
        )
        
        return ContentBriefResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Content brief generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate content brief: {str(e)}")


@app.delete("/clear-session", response_model=ClearDocumentResponse)
async def clear_session(session_id: str):
    """Clear a document session"""
    try:
        success = content_brief_service.clear_session(session_id)
        
        if success:
            return ClearDocumentResponse(
                success=True,
                message="Session cleared successfully",
                cleared_session=session_id
            )
        else:
            return ClearDocumentResponse(
                success=False,
                message="Failed to clear session or session not found"
            )
            
    except Exception as e:
        logger.error(f"Session clearing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear session: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
