from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import pandas as pd
import os
from langchain_openai import AzureChatOpenAI
import io
import dotenv

dotenv.load_dotenv()

from constants import SYSTEM_PROMPT, SYSTEM_PROMPT2, CARD_DETAILS

# Models
class CustomerData(BaseModel):
    Name: str
    Age: int
    Credit_Score: int
    Monthly_Income_CHF: float
    Current_Credit_Card: str
    Proposed_Credit_Card: str
    # Add other fields as needed

class RecommendationRequest(BaseModel):
    customers: List[CustomerData]

class EmailSMS(BaseModel):
    email: str
    sms: str

class RecommendationResponse(BaseModel):
    customer_name: str
    recommendation: str
    personalized_messages: EmailSMS

class BatchRecommendationResponse(BaseModel):
    recommendations: List[RecommendationResponse]

# Initialize FastAPI app
app = FastAPI(
    title="Credit Card Recommendation API",
    description="API for credit card recommendations based on customer data",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Helper functions
def get_credit_score_rating(credit_score):
    if credit_score > 600:
        return "Excellent"
    elif credit_score in range(450, 600):
        return "Good"
    elif credit_score in range(350, 450):
        return "Neutral"
    elif credit_score in range(250, 350):
        return "Poor"
    return "Unknown"

def get_llm_explanation(user_data, card_details):
    prompt2 = f"""
    Analyze the provided customer data and generate a tailored credit card recommendation using this format:

    INPUT PARAMETERS:
    CUSTOMER_DATA: {user_data}
    Card Details: {card_details}

    REQUIRED OUTPUT FORMAT:
    
    ### 👤Customer Details:
        1. Name: 
        2. Age:
        3. Current Card:

    ### 📈 Customer Analysis
    - **Recent Activity**: [LEVEL] (Very Low/Low/Medium/High)
    - **Credit Score**: [Value] (Excellent (>550)/ Good (400-550) / Bad (<350))
    - **Customer Profile & Behaviour Analysis**: [~100 words analyzing customer's financial behavior, spending patterns, and lifestyle indicators]. Remember this customer analysis should not be generic, rather it should showcase what customer love spending their money. 
    
    ###💡Tailored Recommendation

    - **Proposed Card**: [CARD NAME], the proposed card is already in the data, use that itself. 
    - **Reasoning**: Start with what card to recommend[~100 words explaining why this card is suitable for the customer's profile, including:
        * How it matches spending patterns
        * Benefits relevant to customer lifestyle
        * Financial impact considerations
        * Growth potential]
    """

    os.environ["OPENAI_API_VERSION"] = os.getenv("OPENAI_API_VERSION")
    os.environ["AZURE_OPENAI_ENDPOINT"] = os.getenv("AZURE_OPENAI_ENDPOINT")
    os.environ["AZURE_OPENAI_API_KEY"]= os.getenv("AZURE_OPENAI_API_KEY")

    llm = AzureChatOpenAI(
        deployment_name="intern-gpt4",
        model="gpt-4o-mini",
        temperature=0,
    )
    
    messages = [
        ("system", SYSTEM_PROMPT2),
        ("human", prompt2)
    ]
    ai_msg = llm.invoke(messages)
    
    mail_sms = [
        (
            "system",
            "You are an expert AI copywriter specializing in financial product communications. Your role is to craft personalized, compelling messages that effectively convey the benefits of credit card offers to potential customers. You have a deep understanding of consumer financial behavior, credit card features, and effective marketing strategies.",
        ),
        (
            "human",
            f"""
            Your task is to create engaging email and SMS communications for a credit card offer. The messaging should be upbeat and focus on the card's key benefits and unique value proposition.

            Example style to follow:
            "Jane, Don't Miss Your 20% OFF on Eco-Friendly Fitness Gear!
            Get ready to crush your fitness goals with the best eco-friendly gear! 🌿💪 This exclusive 20% discount is just for you—don't wait! Shop now and feel great, while doing good for the planet."

            User_Data:
            {user_data}
            Recommendations and PROPOSED CARD details: {ai_msg.content}
            CARD DETAILS:
            {card_details}

            Please provide Email and SMS adhering to the KEY GUIDELINES, if failed to follow, you will be fired. 
            1. Email:
            Craft a concise email (max 200 words) that:
            - Uses an engaging subject line following the example style
            - Highlights 2-3 key benefits of the proposed credit card
            - Focuses on the unique value proposition
            - Uses an upbeat, friendly yet professional tone
            - Emphasizes the upgrade opportunity/exclusive offer
            - Includes emojis sparingly for engagement
            - Ends with a clear call-to-action
            - Signs off as "Team Cembra Bank"

            2. SMS:
            Create a brief SMS (max 160 characters) that:
            - Focuses on the most compelling card benefit
            - Uses an exciting, urgent tone
            - Includes a clear call-to-action
            - Incorporates 1-2 relevant emojis
            
            KEY GUIDELINES:
            - Keep messaging general and benefit-focused
            - Don't reference specific spending patterns or behaviors
            - Focus on the card's premium/upgrade value
            - Use positive, forward-looking language
            - Create a sense of exclusivity and opportunity
            - Maintain a warm, inviting tone throughout
            - You MUST not mention USER DATA values within the messaging. 
            """
        )
    ]
    sms_model = llm.invoke(mail_sms)
    
    return ai_msg.content, sms_model.content

# Endpoints
@app.post("/api/recommend", response_model=RecommendationResponse)
async def recommend_card(customer: CustomerData):
    """
    Generate a credit card recommendation for a single customer
    """
    try:
        user_data = customer.dict()
        recommendation, messages = get_llm_explanation(user_data, CARD_DETAILS)
        
        return {
            "customer_name": customer.Name,
            "recommendation": recommendation,
            "personalized_messages": {
                "email": messages.split("SMS:")[0].replace("Email:", "").strip(),
                "sms": messages.split("SMS:")[1].strip() if "SMS:" in messages else ""
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating recommendation: {str(e)}")

@app.post("/api/recommend-batch", response_model=BatchRecommendationResponse)
async def recommend_cards_batch(request: RecommendationRequest):
    """
    Generate credit card recommendations for multiple customers
    """
    try:
        recommendations = []
        
        for customer in request.customers:
            user_data = customer.dict()
            recommendation, messages = get_llm_explanation(user_data, CARD_DETAILS)
            
            recommendations.append({
                "customer_name": customer.Name,
                "recommendation": recommendation,
                "personalized_messages": {
                    "email": messages.split("SMS:")[0].replace("Email:", "").strip(),
                    "sms": messages.split("SMS:")[1].strip() if "SMS:" in messages else ""
                }
            })
        
        return {"recommendations": recommendations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating batch recommendations: {str(e)}")

@app.post("/api/upload-csv")
async def process_csv(file: UploadFile = File(...)):
    """
    Process a CSV file with customer data and return recommendations
    """
    try:
        # Read CSV file
        contents = await file.read()
        df = pd.read_csv(io.BytesIO(contents))
        
        # Drop unnecessary columns as in the Streamlit app
        df.drop(["Credit_Card_Preference", "Loan_Repayment_History", "Rent_CHF", 
                 "Taxable_Assets", "Residence_Permit_Type"], axis=1, inplace=True, errors='ignore')
        
        recommendations = []
        
        # Process each row
        for index, row in df.iterrows():
            row_dict = row.to_dict()
            customer_name = row_dict.get('Name', f'Customer {index + 1}')
            
            recommendation, messages = get_llm_explanation(row_dict, CARD_DETAILS)
            
            recommendations.append({
                "customer_name": customer_name,
                "recommendation": recommendation,
                "personalized_messages": {
                    "email": messages.split("SMS:")[0].replace("Email:", "").strip(),
                    "sms": messages.split("SMS:")[1].strip() if "SMS:" in messages else ""
                }
            })
        
        return {"recommendations": recommendations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing CSV: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8007)