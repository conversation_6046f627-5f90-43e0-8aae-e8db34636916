import openai
import streamlit as st
import pandas as pd
from constants import SYSTEM_PROMPT, SYSTEM_PROMPT2,CARD_DETAILS
# from langchain_community.chat_models import AzureChatOpenAI
from langchain_openai import AzureChatOpenAI
import os

from prettify import make_better

def get_credit_score_rating(credit_score):
    if credit_score > 600:
        return "Excellent"
    elif credit_score in range(450, 600):
        return "Good"
    elif credit_score in range(350, 450):
        return "Neutral"
    elif credit_score in range(250, 350):
        return "Poor"
    return "Unknown"

def get_llm_explanation(user_data, card_details):
    prompt = f"""
        Based on the CARD Details predict the Proposed Card Name to the customer. 

        - Evaluate Spending and Lifestyle: 
        Assess the customers spending patterns, with particular focus on categories like shopping, dining, and travel, to infer lifestyle preferences (e.g., spontaneous purchases, city trips).
        - Consider Demographics: 
        Include insights on the customer's age, marital status, and dependents to contextualize flexibility and financial obligations, as younger customers often prefer rewards and travel benefits.
        - Assess Financial Stability: 
        Use monthly income and credit score to gauge the customers financial health and evaluate their suitability for an upgraded card.
        - Match Card Benefits to Profile: 
        Based on the customers profile, identify features of the proposed card that cater to their preferences, such as cashback, travel rewards, and purchase protection.
        - Structure and Conciseness: 
        Ensure the response follows this format:
        - **Customer Analysis** (approx. 100 words): Summarize the customer's lifestyle, spending habits, and financial stability.
        - **Tailored Recommendation** (approx. 100 words): Recommend the proposed credit card upgrade by aligning the card's benefits with the customer's preferences and financial behavior.

        CUSTOMER_DATA: {user_data}
        Card Details: {card_details}

        OUTPUT FORMAT:
        - Recent Activity: (High/Low/Excellent/Poor)
        - Customer Analysis
        - Proposed Card Name
        - Tailored Recommendation:
    """
    prompt2 = f"""
    Analyze the provided customer data and generate a tailored credit card recommendation using this format:

    INPUT PARAMETERS:
    CUSTOMER_DATA: {user_data}
    Card Details: {card_details}

    REQUIRED OUTPUT FORMAT:
    
    ### 👤Customer Details:
        1. Name: 
        2. Age:
        3. Current Card:

    ### 📈 Customer Analysis
    - **Recent Activity**: [LEVEL] (Very Low/Low/Medium/High)
    - **Credit Score**: [Value] (Excellent (>550)/ Good (400-550) / Bad (<350))
    - **Customer Profile & Behaviour Analysis**: [~100 words analyzing customer's financial behavior, spending patterns, and lifestyle indicators]. Remember this customer analysis should not be generic, rather it should showcase what customer love spending their money. 
    
    ###💡Tailored Recommendation

    - **Proposed Card**: [CARD NAME], the proposed card is already in the data, use that itself. 
    - **Reasoning**: Start with what card to recommend[~100 words explaining why this card is suitable for the customer's profile, including:
        * How it matches spending patterns
        * Benefits relevant to customer lifestyle
        * Financial impact considerations
        * Growth potential]

    REQUIREMENTS:
    - Match the professional tone and data-driven approach shown in the example images
    - Focus on quantitative data while incorporating qualitative lifestyle factors
    - Emphasize how card benefits align with customer needs
    - Consider future potential usage based on customer profile

    If the user data is from the example, consider the same customer analysis and recommendation. 

    ### EXAMPLE OUTPUT:

    #### 👤 Customer Details:
    
    1. **Name:** Alex
    2. **Age:** 39
    3. **Current Card:** Everyday Rewards Card (GroceryPro)
    
    * *
    
    ### 📈 Customer Analysis:
    
    **Recent Activity:** Moderate
    **Credit Score:** 580 (Excellent)
    **Customer Profile & Behavior Analysis:**  
        Alex is a 39-year-old professional with a stable income and a family of three. His spending patterns highlight consistent use of the Everyday Rewards Card for retail and grocery purchases, with an average monthly transaction value of $500. He has demonstrated responsible financial management and maintains a high credit score. While his current card provides good cashback benefits for everyday purchases, Alex’s evolving lifestyle, including frequent travel and occasional luxury spending, indicates a need for a more versatile card with broader rewards.
    
    * *
    
    ### 💡 Tailored Recommendation:
    
    **Proposed Card:** Impulse Card Platinum (Explorer)
    **Reasoning:**  
        The Impulse Card Platinum (Explorer) is recommended for Alex due to its travel benefits and flexible payment options, which align with his financial behavior and occasional luxury expenses. Its loyalty rewards for all purchases globally and comprehensive insurance coverage complement Alex’s lifestyle needs. This card offers a balance between everyday utility and premium perks, ensuring both savings and exclusive experiences. Additionally, the ability to carry interest-free balances for larger purchases aligns with his financial planning approach, providing added flexibility and peace of mind.
    """
    # os.environ["OPENAI_API_TYPE"] = "azure"
    openai_api_version = os.getenv("OPENAI_API_VERSION")
    azure_openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    azure_openai_api_key = os.getenv("AZURE_OPENAI_API_KEY")

    llm = AzureChatOpenAI(
        deployment_name="intern-gpt4",
        model="gpt-4o-mini",
        temperature=0,
        openai_api_version=openai_api_version,
        azure_openai_endpoint=azure_openai_endpoint,
        azure_openai_api_key=azure_openai_api_key,
    )
    messages = [
        ("system", SYSTEM_PROMPT2),
        ("human", prompt2)
    ]
    ai_msg = llm.invoke(messages)
    
    mail_sms = [
        (
            "system",
            "You are an expert AI copywriter specializing in financial product communications. Your role is to craft personalized, compelling messages that effectively convey the benefits of credit card offers to potential customers. You have a deep understanding of consumer financial behavior, credit card features, and effective marketing strategies.",
        ),
        (
            "human",
            f"""
            Your task is to create engaging email and SMS communications for a credit card offer. The messaging should be upbeat and focus on the card's key benefits and unique value proposition.

            Example style to follow:
            "Jane, Don't Miss Your 20% OFF on Eco-Friendly Fitness Gear!
            Get ready to crush your fitness goals with the best eco-friendly gear! 🌿💪 This exclusive 20% discount is just for you—don't wait! Shop now and feel great, while doing good for the planet."

            User_Data:
            {user_data}
            Recommendations and PROPOSED CARD details: {ai_msg.content}
            CARD DETAILS:
            {card_details}

            Please provide Email and SMS adhering to the KEY GUIDELINES, if failed to follow, you will be fired. 
            1. Email:
            Craft a concise email (max 200 words) that:
            - Uses an engaging subject line following the example style
            - Highlights 2-3 key benefits of the proposed credit card
            - Focuses on the unique value proposition
            - Uses an upbeat, friendly yet professional tone
            - Emphasizes the upgrade opportunity/exclusive offer
            - Includes emojis sparingly for engagement
            - Ends with a clear call-to-action
            - Signs off as "Team Cembra Bank"

            2. SMS:
            Create a brief SMS (max 160 characters) that:
            - Focuses on the most compelling card benefit
            - Uses an exciting, urgent tone
            - Includes a clear call-to-action
            - Incorporates 1-2 relevant emojis
            
            KEY GUIDELINES:
            - Keep messaging general and benefit-focused
            - Don't reference specific spending patterns or behaviors
            - Focus on the card's premium/upgrade value
            - Use positive, forward-looking language
            - Create a sense of exclusivity and opportunity
            - Maintain a warm, inviting tone throughout
            - You MUST not mention USER DATA values within the messaging. 
            """)
        ]
    sms_model = llm.invoke(mail_sms)
    
    return ai_msg.content,sms_model.content

def main():
    st.title("Credit Card Recommendation System")

    uploaded_file = st.file_uploader("Choose a CSV file", type="csv")

    if uploaded_file is not None:
        df = pd.read_csv(uploaded_file)
        df.drop(["Credit_Card_Preference","Loan_Repayment_History","Rent_CHF","Taxable_Assets","Residence_Permit_Type"],axis=1,inplace=True)
        st.markdown("#### User Data")
        st.dataframe(df.drop(['Proposed_Credit_Card'],axis=1))

        st.markdown("#### Prediction")
        st.dataframe(df)

        if st.button("Get Credit Card Recommendations"):
            st.subheader("Analysis Results:")
        
            for index, row in df.iterrows():
                customer_name = row.get('Name', f'Customer {index + 1}')

                with st.expander(f"Analysis for {customer_name}", expanded=True):
                    row_dict = row.to_dict()
                    
                    credit_score = int(row_dict['Credit_Score'])
                    result,mail = get_llm_explanation(row_dict, CARD_DETAILS)
                    
                    st.markdown("### Recommendation Details:")
                    with st.spinner(f"Analyzing {customer_name} data..."):
                        result,mail = get_llm_explanation(row_dict, CARD_DETAILS)
                        st.write(result)
                    
                    st.markdown("---") 
                    st.markdown("### Personalized Message- Previous")
                    st.write(mail)

if __name__ == "__main__":
    main()
