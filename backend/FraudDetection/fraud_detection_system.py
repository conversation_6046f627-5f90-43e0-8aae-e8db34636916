import os
import json
import tempfile
import hashlib
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pydantic import BaseModel
from groq import Groq
from openai import AzureOpenAI
from dotenv import load_dotenv

load_dotenv()

# Environment variables
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
OPENAI_API_VERSION = os.getenv("OPENAI_API_VERSION", "2024-02-15-preview")
AZURE_DEPLOYMENT = os.getenv("AZURE_DEPLOYMENT", "gpt-4")

# Pydantic models for API
class TranscriptionResult(BaseModel):
    transcript: str
    confidence: float = 1.0
    language: str = "en"

class AnalysisResult(BaseModel):
    verification_status: str
    confidence: float
    risk_level: str
    missing_information: List[str]
    suspicious_elements: List[str]
    recommended_action: str
    explanation: str

class TemplateUpdate(BaseModel):
    template: str

class AudioAnalysisRequest(BaseModel):
    audio_file_path: str
    template: Optional[str] = None

class TextAnalysisRequest(BaseModel):
    transcript: str
    template: Optional[str] = None

# Default template
DEFAULT_TEMPLATE = """
Analyze the provided bank callback verification call transcript by following these guidelines:

1. Verify if the bank agent asked for all necessary information:
   - Customer's full name
   - Last 4 digits of the card used for the transaction
   - Date and approximate time of the transaction
   - Amount of the transaction
   - Merchant or location where the transaction occurred

2. Check if the customer provided consistent and accurate information:
   - Does the information match what the agent is verifying?
   - Are there any discrepancies or hesitations in the customer's responses?

3. Assess the overall tone and context of the call:
   - Does the customer seem surprised by the transaction?
   - Is there any indication of coercion or stress in the customer's voice?
   - Does the customer have a clear recollection of the transaction?

4. Evaluate the bank agent's adherence to protocol:
   - Did they properly introduce themselves and state the reason for the call?
   - Did they verify the customer's identity before discussing transaction details?
   - Did they explain why the transaction was flagged for verification?

5. Look for any red flags or unusual behavior:
   - Attempts to rush the verification process
   - Reluctance to provide certain pieces of information
   - Inconsistencies in the story or details provided

6. Consider additional security measures:
   - Did the agent suggest or implement any additional security measures?
   - Was there a discussion about updating security preferences or alerts?
"""

def init_clients():
    """Initialize external service clients"""
    # Initialize Groq client for transcription
    groq_client = None
    if GROQ_API_KEY:
        try:
            groq_client = Groq(api_key=GROQ_API_KEY)
        except Exception as e:
            print(f"Warning: Could not initialize Groq client: {e}")
            print("Running in demo mode - audio transcription will return mock responses")
    else:
        print("Warning: Missing Groq API key - audio transcription disabled")
    
    # Initialize Azure OpenAI client for analysis
    azure_client = None
    if AZURE_OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT:
        try:
            azure_client = AzureOpenAI(
                api_key=AZURE_OPENAI_API_KEY,
                api_version=OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT
            )
        except Exception as e:
            print(f"Warning: Could not initialize Azure OpenAI client: {e}")
            print("Running in demo mode - AI analysis will return mock responses")
    else:
        print("Warning: Missing Azure OpenAI credentials - AI analysis disabled")
    
    return groq_client, azure_client

class FraudDetectionSystem:
    """Core fraud detection system for bank callback verification"""
    
    def __init__(self):
        self.groq_client, self.azure_client = init_clients()
        self.current_template = DEFAULT_TEMPLATE
    
    def transcribe_audio(self, file_content: bytes, filename: str) -> TranscriptionResult:
        """Transcribe audio file using Groq Whisper"""
        if not self.groq_client:
            # Demo mode - return mock transcription
            mock_transcript = f"""
            Demo transcription for {filename}:
            
            Bank Agent: Hello, this is Sarah from First National Bank's fraud prevention department. 
            Am I speaking with John Smith?
            
            Customer: Yes, this is John.
            
            Bank Agent: Mr. Smith, we're calling to verify a recent transaction on your account. 
            Can you please confirm the last 4 digits of the card ending in 1234?
            
            Customer: Yes, that's 1234.
            
            Bank Agent: Thank you. We show a transaction for $150.00 at Best Buy yesterday around 2 PM. 
            Do you recall making this purchase?
            
            Customer: Yes, I bought a new phone charger there yesterday afternoon.
            
            Bank Agent: Perfect. Thank you for confirming. This transaction has been verified as legitimate.
            
            Note: This is a demo transcription. For real audio processing, configure your Groq API key.
            """
            return TranscriptionResult(
                transcript=mock_transcript.strip(),
                confidence=0.95,
                language="en"
            )
        
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as tmp_file:
                tmp_file.write(file_content)
                tmp_file_path = tmp_file.name
            
            # Transcribe using Groq
            with open(tmp_file_path, "rb") as audio_file:
                transcription = self.groq_client.audio.transcriptions.create(
                    file=(filename, audio_file.read()),
                    model="whisper-large-v3-turbo",
                    prompt="This is a bank callback verification call",
                    response_format="json",
                    language="en",
                    temperature=0.0
                )
            
            # Clean up temporary file
            os.unlink(tmp_file_path)
            
            return TranscriptionResult(
                transcript=transcription.text,
                confidence=1.0,
                language="en"
            )
            
        except Exception as e:
            raise Exception(f"Error transcribing audio: {str(e)}")
    
    def clean_json_response(self, content: str) -> str:
        """Helper function to clean and prepare JSON string for parsing"""
        # Remove code block markers
        cleaned = content.replace('```json', '').replace('```', '').strip()
        
        # Remove control characters that cause JSON parsing issues
        import re
        cleaned = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', cleaned)
        
        # Remove any non-JSON content before the first '{'
        start_idx = cleaned.find('{')
        end_idx = cleaned.rfind('}')
        if start_idx != -1 and end_idx != -1:
            cleaned = cleaned[start_idx:end_idx + 1]
        
        return cleaned
    
    def analyze_callback_verification(self, transcript: str, template: Optional[str] = None) -> AnalysisResult:
        """Analyze callback verification transcript"""
        analysis_template = template or self.current_template
        
        if not self.azure_client:
            # Demo mode - return structured mock analysis
            return self._create_demo_analysis(transcript)
        
        try:
            system_message = f"""You are an AI assistant trained to analyze bank callback verification call transcripts. 
            Use the following guidelines to conduct your analysis:
            {analysis_template}
            Based on your analysis, provide a response in the following JSON format:
            {{
            "verification_status": "VERIFIED or NOT VERIFIED",
            "confidence": "number between 0 and 1",
            "risk_level": "HIGH, MEDIUM, or LOW",
            "missing_information": ["list", "of", "missing", "items"],
            "suspicious_elements": ["list", "of", "suspicious", "elements"],
            "recommended_action": "specific action to take (e.g., approve transaction, block card, request additional verification)",
            "explanation": "Provide a very brief yet comprehensive markdown-formatted explanation here. You can use markdown features like:
                          ### Header Level 2
                          - Bullet points
                          **Bold text**
                          *Italic text*
                          > Blockquotes
                          And other markdown formatting as needed to make the explanation clear and well-structured."
            }}
            
            IMPORTANT: Make the explanation field beautifully formatted with proper markdown syntax for clear organization.
            For the explanation, always use Header Level 3 as the header formatting for the explanation to separate different aspects of the analysis,
            use bullet points (-) for listing findings, and use bold/italic text to emphasize important points."""

            response = self.azure_client.chat.completions.create(
                model=AZURE_DEPLOYMENT,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": f"Analyze the following bank callback verification call transcript: {transcript}"}
                ],
                temperature=0.1,
                max_tokens=4000,
                top_p=1
            )
            
            response_content = response.choices[0].message.content
            
            # Clean up the response content using the helper method
            cleaned_content = self.clean_json_response(response_content)
            
            try:
                parsed_response = json.loads(cleaned_content)
                
                # Validate required fields
                required_fields = ["verification_status", "confidence", "risk_level", 
                                 "missing_information", "suspicious_elements", 
                                 "recommended_action", "explanation"]
                
                for field in required_fields:
                    if field not in parsed_response:
                        raise ValueError(f"Missing required field: {field}")
                
                # Ensure lists are actually lists
                if not isinstance(parsed_response["missing_information"], list):
                    parsed_response["missing_information"] = [parsed_response["missing_information"]]
                if not isinstance(parsed_response["suspicious_elements"], list):
                    parsed_response["suspicious_elements"] = [parsed_response["suspicious_elements"]]
                
                # Clean up explanation field
                if isinstance(parsed_response["explanation"], str):
                    parsed_response["explanation"] = parsed_response["explanation"].replace('\\n', '\n')
                
                return AnalysisResult(**parsed_response)
                
            except json.JSONDecodeError as e:
                raise Exception(f"Failed to parse JSON response: {str(e)}")
                
        except Exception as e:
            raise Exception(f"Error analyzing transcript: {str(e)}")
    
    def _create_demo_analysis(self, transcript: str) -> AnalysisResult:
        """Create a demo analysis when AI services are not available"""
        # Simple keyword-based analysis for demo
        transcript_lower = transcript.lower()
        
        # Check for basic verification elements
        has_name = any(word in transcript_lower for word in ["name", "john", "smith", "customer"])
        has_card_digits = any(word in transcript_lower for word in ["last 4", "digits", "1234", "card"])
        has_transaction = any(word in transcript_lower for word in ["transaction", "purchase", "buy", "amount"])
        has_merchant = any(word in transcript_lower for word in ["store", "merchant", "best buy", "amazon"])
        
        verification_elements = sum([has_name, has_card_digits, has_transaction, has_merchant])
        
        if verification_elements >= 3:
            status = "VERIFIED"
            risk = "LOW"
            confidence = 0.85
            missing = []
            suspicious = []
            action = "approve transaction"
        elif verification_elements >= 2:
            status = "VERIFIED"
            risk = "MEDIUM"
            confidence = 0.65
            missing = ["Some verification details incomplete"]
            suspicious = []
            action = "approve transaction with monitoring"
        else:
            status = "NOT VERIFIED"
            risk = "HIGH"
            confidence = 0.45
            missing = ["Insufficient verification information"]
            suspicious = ["Incomplete verification process"]
            action = "request additional verification"
        
        explanation = f"""
### Demo Analysis Results

**Verification Elements Found:** {verification_elements}/4

**Analysis Summary:**
- **Customer Identity:** {'✓' if has_name else '✗'} Name verification
- **Card Verification:** {'✓' if has_card_digits else '✗'} Last 4 digits confirmed
- **Transaction Details:** {'✓' if has_transaction else '✗'} Transaction amount/details
- **Merchant Information:** {'✓' if has_merchant else '✗'} Purchase location

**Risk Assessment:**
- Based on keyword analysis of the transcript
- {verification_elements} out of 4 key verification elements detected

**Note:** This is a demo analysis. For AI-powered fraud detection, configure your Azure OpenAI credentials.
"""
        
        return AnalysisResult(
            verification_status=status,
            confidence=confidence,
            risk_level=risk,
            missing_information=missing,
            suspicious_elements=suspicious,
            recommended_action=action,
            explanation=explanation.strip()
        )
    
    def update_template(self, new_template: str) -> bool:
        """Update the analysis template"""
        try:
            self.current_template = new_template
            return True
        except Exception as e:
            raise Exception(f"Error updating template: {str(e)}")
    
    def get_template(self) -> str:
        """Get current analysis template"""
        return self.current_template
    
    def reset_template(self) -> bool:
        """Reset template to default"""
        try:
            self.current_template = DEFAULT_TEMPLATE
            return True
        except Exception as e:
            raise Exception(f"Error resetting template: {str(e)}")
    
    def get_template_info(self) -> Dict[str, Any]:
        """Get template information"""
        is_default = self.current_template == DEFAULT_TEMPLATE
        return {
            "current_template": self.current_template,
            "is_default": is_default,
            "template_length": len(self.current_template),
            "default_available": True
        } 