from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import os
from datetime import datetime

# Import our fraud detection system
from fraud_detection_system import (
    FraudDetectionSystem,
    TranscriptionResult,
    AnalysisResult,
    TemplateUpdate,
    TextAnalysisRequest,
    DEFAULT_TEMPLATE
)

# FastAPI app
app = FastAPI(title="Bank Callback Verification API", version="1.0.0")

# CORS middleware for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Add your React app URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Additional Pydantic models for API responses
class AudioUploadResponse(BaseModel):
    success: bool
    message: str
    transcription: TranscriptionResult
    analysis: AnalysisResult

class TextAnalysisResponse(BaseModel):
    success: bool
    message: str
    analysis: AnalysisResult

class TemplateResponse(BaseModel):
    success: bool
    message: str
    template_info: Dict[str, Any]

# Initialize the fraud detection system
fraud_system = FraudDetectionSystem()

# API Endpoints
@app.get("/")
async def root():
    return {"message": "Bank Callback Verification API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/transcribe-audio", response_model=TranscriptionResult)
async def transcribe_audio(file: UploadFile = File(...)):
    """Transcribe audio file using Groq Whisper"""
    try:
        # Validate file type
        allowed_types = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in allowed_types:
            raise HTTPException(
                status_code=400, 
                detail="File type not supported. Please upload WAV, MP3, M4A, FLAC, or OGG files."
            )
        
        # Read file content
        file_content = await file.read()
        
        # Transcribe audio
        result = fraud_system.transcribe_audio(file_content, file.filename)
        
        return result
        
    except HTTPException:
        raise  # Re-raise HTTPExceptions as-is
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error transcribing audio: {str(e)}")

@app.post("/analyze-audio", response_model=AudioUploadResponse)
async def analyze_audio_file(
    file: UploadFile = File(...),
    template: Optional[str] = None
):
    """Upload audio file, transcribe, and analyze for fraud detection"""
    try:
        # Validate file type
        allowed_types = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in allowed_types:
            raise HTTPException(
                status_code=400, 
                detail="File type not supported. Please upload WAV, MP3, M4A, FLAC, or OGG files."
            )
        
        # Read file content
        file_content = await file.read()
        
        # Transcribe audio
        transcription = fraud_system.transcribe_audio(file_content, file.filename)
        
        # Analyze transcript
        analysis = fraud_system.analyze_callback_verification(transcription.transcript, template)
        
        return AudioUploadResponse(
            success=True,
            message="Audio transcribed and analyzed successfully",
            transcription=transcription,
            analysis=analysis
        )
        
    except HTTPException:
        raise  # Re-raise HTTPExceptions as-is
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing audio: {str(e)}")

@app.post("/analyze-text", response_model=TextAnalysisResponse)
async def analyze_text(request: TextAnalysisRequest):
    """Analyze text transcript for fraud detection"""
    try:
        if not request.transcript.strip():
            raise HTTPException(status_code=400, detail="Transcript cannot be empty")
        
        # Analyze transcript
        analysis = fraud_system.analyze_callback_verification(request.transcript, request.template)
        
        return TextAnalysisResponse(
            success=True,
            message="Transcript analyzed successfully",
            analysis=analysis
        )
        
    except HTTPException:
        raise  # Re-raise HTTPExceptions as-is
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing transcript: {str(e)}")

@app.get("/template", response_model=TemplateResponse)
async def get_template():
    """Get current analysis template"""
    try:
        template_info = fraud_system.get_template_info()
        
        return TemplateResponse(
            success=True,
            message="Template retrieved successfully",
            template_info=template_info
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting template: {str(e)}")

@app.post("/template", response_model=TemplateResponse)
async def update_template(request: TemplateUpdate):
    """Update analysis template"""
    try:
        if not request.template.strip():
            raise HTTPException(status_code=400, detail="Template cannot be empty")
        
        success = fraud_system.update_template(request.template)
        
        if success:
            template_info = fraud_system.get_template_info()
            return TemplateResponse(
                success=True,
                message="Template updated successfully",
                template_info=template_info
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to update template")
            
    except HTTPException:
        raise  # Re-raise HTTPExceptions as-is
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating template: {str(e)}")

@app.post("/template/reset", response_model=TemplateResponse)
async def reset_template():
    """Reset template to default"""
    try:
        success = fraud_system.reset_template()
        
        if success:
            template_info = fraud_system.get_template_info()
            return TemplateResponse(
                success=True,
                message="Template reset to default successfully",
                template_info=template_info
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to reset template")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resetting template: {str(e)}")

@app.get("/template/default")
async def get_default_template():
    """Get the default analysis template"""
    return {
        "default_template": DEFAULT_TEMPLATE,
        "template_length": len(DEFAULT_TEMPLATE)
    }

# Additional utility endpoints
@app.get("/supported-formats")
async def get_supported_formats():
    """Get list of supported audio formats"""
    return {
        "audio_formats": [".wav", ".mp3", ".m4a", ".flac", ".ogg"],
        "max_file_size": "25MB",
        "recommended_format": "wav"
    }

@app.get("/demo-transcript")
async def get_demo_transcript():
    """Get a sample transcript for testing"""
    demo_transcript = """
Bank Agent: Hello, this is Sarah from First National Bank's fraud prevention department. Am I speaking with John Smith?

Customer: Yes, this is John.

Bank Agent: Mr. Smith, we're calling to verify a recent transaction on your account. Can you please confirm the last 4 digits of the card ending in 1234?

Customer: Yes, that's 1234.

Bank Agent: Thank you. We show a transaction for $150.00 at Best Buy yesterday around 2 PM. Do you recall making this purchase?

Customer: Yes, I bought a new phone charger there yesterday afternoon.

Bank Agent: Perfect. Thank you for confirming. This transaction has been verified as legitimate.
"""
    
    return {
        "demo_transcript": demo_transcript.strip(),
        "description": "Sample bank callback verification transcript for testing"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8009) 