#!/bin/bash

# Medical QA Backend Environment Setup Script

echo "Setting up Medical QA Backend Environment..."

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

echo "Environment setup complete!"
echo "To activate the environment, run: source venv/bin/activate"
echo "To start the server, run: uvicorn main:app --reload --host 0.0.0.0 --port 8001"
