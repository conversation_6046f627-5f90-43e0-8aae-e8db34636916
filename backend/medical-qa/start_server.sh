#!/bin/bash

# Medical QA Backend Server Startup Script

echo "Starting Medical QA Backend Server..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Virtual environment not found. Please run setup_env.sh first."
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Warning: .env file not found. Please copy .env.example to .env and configure your settings."
    echo "Using .env.example as fallback..."
    cp .env.example .env
fi

# Start the FastAPI server
echo "Starting FastAPI server on http://0.0.0.0:8001"
uvicorn main:app --reload --host 0.0.0.0 --port 8001
