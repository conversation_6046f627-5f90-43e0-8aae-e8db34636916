"""
Pydantic models for Medical QA API
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from enum import Enum


class UserType(str, Enum):
    """User type enumeration"""
    PATIENT = "Patient"
    HEALTHCARE_PROFESSIONAL = "Healthcare Professional"
    STUDENT = "Student"


class UrgencyLevel(str, Enum):
    """Urgency level enumeration"""
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"


class ResponseStyle(str, Enum):
    """Response style enumeration"""
    CONCISE = "Concise"
    MODERATE = "Moderate"
    PROLONGED = "Prolonged"


class OutputFormat(str, Enum):
    """Output format enumeration"""
    BULLETS = "bullets"
    PARAGRAPH = "paragraph"


class DetailLevel(str, Enum):
    """Detail level enumeration"""
    BASIC = "basic"
    DETAILED = "detailed"
    COMPREHENSIVE = "comprehensive"


class MedicalQuestionRequest(BaseModel):
    """Request model for medical question submission"""
    question: str = Field(..., min_length=1, max_length=5000, description="The medical question to ask")
    user_type: UserType = Field(default=UserType.PATIENT, description="Type of user asking the question")
    urgency: UrgencyLevel = Field(default=UrgencyLevel.MEDIUM, description="Urgency level of the question")
    style: ResponseStyle = Field(default=ResponseStyle.MODERATE, description="Desired response style")
    output_format: Optional[OutputFormat] = Field(default=None, description="Preferred output format")
    detail_level: Optional[DetailLevel] = Field(default=None, description="Level of detail required")
    collection_name: Optional[str] = Field(default=None, description="Collection name for document-based queries")


class DocumentUploadResponse(BaseModel):
    """Response model for document upload"""
    success: bool = Field(..., description="Whether the upload was successful")
    message: str = Field(..., description="Status message")
    collection_name: Optional[str] = Field(default=None, description="Generated collection name")
    filename: Optional[str] = Field(default=None, description="Original filename")
    document_id: Optional[str] = Field(default=None, description="Unique document identifier")


class MedicalResponse(BaseModel):
    """Response model for medical questions"""
    answer: str = Field(..., description="The medical answer")
    source_type: str = Field(..., description="Type of source used (document or general knowledge)")
    source_info: Optional[str] = Field(default=None, description="Information about the source")
    urgent_notice: Optional[str] = Field(default=None, description="Urgent medical notice if applicable")
    confidence_level: Optional[str] = Field(default=None, description="Confidence level of the response")
    disclaimer: str = Field(..., description="Medical disclaimer")


class SampleQuestionsResponse(BaseModel):
    """Response model for sample questions"""
    questions: List[str] = Field(..., description="List of sample questions")
    document_based: bool = Field(..., description="Whether questions are based on uploaded document")


class FollowUpQuestionRequest(BaseModel):
    """Request model for follow-up questions"""
    question: str = Field(..., min_length=1, max_length=5000, description="The follow-up question")
    conversation_context: Optional[List[Dict[str, str]]] = Field(default=None, description="Previous conversation context")
    collection_name: Optional[str] = Field(default=None, description="Collection name for document-based queries")
    user_type: UserType = Field(default=UserType.PATIENT, description="Type of user asking the question")
    urgency: UrgencyLevel = Field(default=UrgencyLevel.MEDIUM, description="Urgency level of the question")
    style: ResponseStyle = Field(default=ResponseStyle.MODERATE, description="Desired response style")


class HealthCheckResponse(BaseModel):
    """Response model for health check"""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="API version")
    timestamp: str = Field(..., description="Current timestamp")
    services: Dict[str, str] = Field(..., description="Status of dependent services")


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")


class ClearDocumentResponse(BaseModel):
    """Response model for clearing documents"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Status message")
    cleared_collection: Optional[str] = Field(default=None, description="Name of cleared collection")
