"""
Core medical QA service logic ported from Streamlit
"""

import os
import tempfile
import uuid
from typing import Optional, List, Dict, Any, Tuple
import logging

from langchain_community.chat_models import ChatLiteLLM
from langchain_core.prompts import ChatPromptTemplate
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import AzureOpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore
from langchain.chains import RetrievalQA
from qdrant_client import QdrantClient

from config import settings
from models import UserType, UrgencyLevel, ResponseStyle

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MedicalQAService:
    """Medical QA service class"""
    
    def __init__(self):
        self.llm = None
        self.embeddings = None
        self.qdrant_client = None
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialize all required services"""
        try:
            self.llm = self._initialize_llm()
            self.embeddings = self._initialize_embeddings()
            self.qdrant_client = self._initialize_qdrant_client()
            logger.info("Medical QA services initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize services: {str(e)}")
            raise
    
    def _initialize_llm(self) -> Optional[ChatLiteLLM]:
        """Initialize LiteLLM with configuration"""
        try:
            # Set environment variables
            os.environ['LITELLM_API_KEY'] = settings.litellm_api_key
            os.environ['LITELLM_BASE_URL'] = settings.litellm_base_url
            
            llm = ChatLiteLLM(
                model=settings.litellm_model,
                api_base=settings.litellm_base_url,
                api_key=settings.litellm_api_key,
                temperature=settings.llm_temperature,
                max_tokens=settings.llm_max_tokens
            )
            
            logger.info("LLM initialized successfully")
            return llm
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {str(e)}")
            raise
    
    def _initialize_embeddings(self) -> Optional[AzureOpenAIEmbeddings]:
        """Initialize Azure OpenAI embeddings"""
        try:
            embeddings = AzureOpenAIEmbeddings(
                azure_deployment=settings.azure_deployment,
                openai_api_version=settings.openai_api_version,
                azure_endpoint=settings.azure_openai_endpoint,
                api_key=settings.openai_api_key
            )
            logger.info("Embeddings initialized successfully")
            return embeddings
        except Exception as e:
            logger.error(f"Failed to initialize embeddings: {str(e)}")
            raise
    
    def _initialize_qdrant_client(self) -> Optional[QdrantClient]:
        """Initialize Qdrant client"""
        try:
            client = QdrantClient(
                url=settings.qdrant_url,
                api_key=settings.qdrant_api_key,
                prefer_grpc=True
            )
            logger.info("Qdrant client initialized successfully")
            return client
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client: {str(e)}")
            raise
    
    def classify_user_context(self, user_type: UserType, urgency: UrgencyLevel) -> Tuple[str, str]:
        """Classify user context and urgency level"""
        context_map = {
            UserType.HEALTHCARE_PROFESSIONAL: "medical_professional",
            UserType.PATIENT: "patient",
            UserType.STUDENT: "student"
        }
        
        urgency_map = {
            UrgencyLevel.LOW: "routine_inquiry",
            UrgencyLevel.MEDIUM: "standard_consultation",
            UrgencyLevel.HIGH: "urgent_medical_concern"
        }
        
        return (
            context_map.get(user_type, "patient"),
            urgency_map.get(urgency, "routine_inquiry")
        )
    
    def get_style_instruction(self, style: ResponseStyle) -> str:
        """Get style instruction based on user selection"""
        style_map = {
            ResponseStyle.CONCISE: "Provide a very brief, concise answer with only the most essential information in 1-2 sentences. Use plain text format without any markdown, bullet points, or special formatting.",
            ResponseStyle.MODERATE: "Provide a balanced response with moderate detail and clear explanations. Use 3-5 sentences with key information and some context. Use plain text format without any markdown, bullet points, or special formatting.",
            ResponseStyle.PROLONGED: "Provide a comprehensive, detailed response with extensive explanations, background information, multiple examples, step-by-step breakdowns, related considerations, and thorough coverage of all relevant aspects. Use multiple paragraphs and elaborate on each point extensively. Use plain text format without any markdown, bullet points, or special formatting."
        }
        return style_map.get(style, style_map[ResponseStyle.MODERATE])
    
    def create_medical_prompt(
        self, 
        user_context: str, 
        urgency_level: str, 
        style: ResponseStyle, 
        has_knowledge_base: bool = False
    ) -> ChatPromptTemplate:
        """Create context-aware medical prompt"""
        
        if user_context == "medical_professional":
            base_prompt = """You are a medical information assistant designed to support healthcare professionals. 
            Provide evidence-based, detailed medical information with clinical context."""
        else:
            base_prompt = """You are a medical information assistant designed to provide general health information to patients.
            Use clear, understandable language. Always emphasize the importance of consulting healthcare professionals."""
        
        if urgency_level == "urgent_medical_concern":
            urgency_note = "\n\nIMPORTANT: This appears to be an urgent medical concern. Strongly recommend immediate medical consultation."
        else:
            urgency_note = ""
        
        # Add style instruction
        style_instruction = f"\n\nResponse Style: {self.get_style_instruction(style)}"
        
        if has_knowledge_base:
            context_instruction = f"""
            Answer the question based on the provided context from the uploaded document. 
            If the context doesn't contain relevant information, clearly state that the information is not available in the document.
            
            Document Context: {{context}}
            
            Question: {{question}}"""
        else:
            context_instruction = f"""
            Answer the medical question based on your general medical knowledge. 
            Provide accurate, evidence-based information.
            
            Question: {{question}}"""
        
        full_prompt = base_prompt + urgency_note + style_instruction + "\n\n" + context_instruction
        
        return ChatPromptTemplate.from_template(full_prompt)

    async def process_pdf_document(self, file_content: bytes, filename: str) -> Tuple[Optional[QdrantVectorStore], Optional[str]]:
        """Process uploaded PDF and create Qdrant vector store"""
        try:
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
                tmp_file.write(file_content)
                tmp_file_path = tmp_file.name

            # Load PDF
            loader = PyPDFLoader(tmp_file_path)
            documents = loader.load()

            # Split text into chunks
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=settings.chunk_size,
                chunk_overlap=settings.chunk_overlap,
                length_function=len
            )
            chunks = text_splitter.split_documents(documents)

            if not self.embeddings or not self.qdrant_client:
                raise Exception("Embeddings or Qdrant client not initialized")

            # Create unique collection name
            collection_name = f"medical_docs_{uuid.uuid4().hex[:8]}"

            # Create vector store
            vector_store = QdrantVectorStore.from_documents(
                documents=chunks,
                embedding=self.embeddings,
                url=settings.qdrant_url,
                api_key=settings.qdrant_api_key,
                collection_name=collection_name,
                prefer_grpc=True
            )

            # Clean up temporary file
            os.unlink(tmp_file_path)

            logger.info(f"Document processed successfully: {filename}, collection: {collection_name}")
            return vector_store, collection_name

        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            # Clean up temporary file if it exists
            try:
                if 'tmp_file_path' in locals():
                    os.unlink(tmp_file_path)
            except:
                pass
            raise

    def get_vector_store_by_collection(self, collection_name: str) -> Optional[QdrantVectorStore]:
        """Get vector store by collection name"""
        try:
            if not self.embeddings:
                raise Exception("Embeddings not initialized")

            vector_store = QdrantVectorStore(
                client=self.qdrant_client,
                collection_name=collection_name,
                embedding=self.embeddings
            )

            return vector_store
        except Exception as e:
            logger.error(f"Error getting vector store: {str(e)}")
            return None

    async def generate_response(
        self,
        query: str,
        user_type: UserType,
        urgency: UrgencyLevel,
        style: ResponseStyle,
        collection_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate response with or without knowledge base"""

        if not self.llm:
            raise Exception("LLM not initialized")

        user_context, urgency_level = self.classify_user_context(user_type, urgency)

        try:
            vector_store = None
            if collection_name:
                vector_store = self.get_vector_store_by_collection(collection_name)

            if vector_store:
                # Use Qdrant vector store for retrieval
                prompt_template = self.create_medical_prompt(
                    user_context, urgency_level, style, has_knowledge_base=True
                )

                qa_chain = RetrievalQA.from_chain_type(
                    llm=self.llm,
                    chain_type="stuff",
                    retriever=vector_store.as_retriever(search_kwargs={"k": settings.retrieval_k}),
                    chain_type_kwargs={"prompt": prompt_template}
                )

                response = qa_chain.run(query)
                source_type = "document"
                source_info = f"Answer based on uploaded document"

            else:
                # Use LLM directly without knowledge base
                prompt_template = self.create_medical_prompt(
                    user_context, urgency_level, style, has_knowledge_base=False
                )
                formatted_prompt = prompt_template.format_messages(question=query)
                response = self.llm.invoke(formatted_prompt)
                response = response.content
                source_type = "general_knowledge"
                source_info = "Answer based on general medical knowledge"

            # Add urgent notice if needed
            urgent_notice = None
            if urgency_level == "urgent_medical_concern":
                urgent_notice = """🚨 URGENT NOTICE: This appears to be a potentially urgent medical situation.
                Please seek immediate medical attention or contact emergency services."""

            return {
                "answer": response,
                "source_type": source_type,
                "source_info": source_info,
                "urgent_notice": urgent_notice,
                "disclaimer": "This information is for educational purposes only and should not replace professional medical advice. Always consult with qualified healthcare professionals for medical concerns."
            }

        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            raise

    def clear_collection(self, collection_name: str) -> bool:
        """Clear the specified collection from Qdrant"""
        try:
            if self.qdrant_client and collection_name:
                self.qdrant_client.delete_collection(collection_name)
                logger.info(f"Collection cleared: {collection_name}")
                return True
        except Exception as e:
            logger.error(f"Error clearing collection: {str(e)}")
        return False

    def get_document_based_questions(self, collection_name: str) -> List[str]:
        """Generate contextual questions based on uploaded document content"""
        try:
            vector_store = self.get_vector_store_by_collection(collection_name)
            if not vector_store:
                return []

            # Use the retriever to get some sample content
            retriever = vector_store.as_retriever(search_kwargs={"k": 2})
            sample_docs = retriever.get_relevant_documents("main topic summary key information")

            if sample_docs:
                # Generate context-aware questions based on document content
                doc_content = " ".join([doc.page_content[:200] for doc in sample_docs])

                # Simple keyword-based question generation
                questions = []

                # Always include these generic document questions
                questions.extend([
                    "What is the main topic of this document?",
                    "Can you summarize the key points?"
                ])

                # Add specific questions based on content keywords
                content_lower = doc_content.lower()

                if any(word in content_lower for word in ['treatment', 'therapy', 'medication', 'drug']):
                    questions.append("What treatments or medications are mentioned?")

                if any(word in content_lower for word in ['symptom', 'sign', 'condition', 'disease']):
                    questions.append("What symptoms or conditions are discussed?")

                if any(word in content_lower for word in ['procedure', 'surgery', 'operation']):
                    questions.append("What procedures are described?")

                if any(word in content_lower for word in ['risk', 'complication', 'side effect']):
                    questions.append("What risks or complications are mentioned?")

                return questions[:4]  # Return max 4 questions

        except Exception as e:
            logger.error(f"Error generating document questions: {str(e)}")
            # Fallback to generic document questions
            return [
                "What is the main topic of this document?",
                "Can you summarize the key points?",
                "What important information should I know?",
                "Are there any specific recommendations?"
            ]

        return []


# Global service instance
medical_service = MedicalQAService()
