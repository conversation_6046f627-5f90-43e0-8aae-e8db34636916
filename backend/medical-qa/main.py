"""
FastAPI main application for Medical QA
"""

import logging
from datetime import datetime
from typing import List, Optional
import uuid

from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from config import settings
from models import (
    MedicalQuestionRequest,
    MedicalResponse,
    DocumentUploadResponse,
    SampleQuestionsResponse,
    FollowUpQuestionRequest,
    HealthCheckResponse,
    ErrorResponse,
    ClearDocumentResponse
)
from medical_service import medical_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Medical QA API",
    description="AI-powered medical question answering system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Global exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            message="An unexpected error occurred",
            details={"exception": str(exc)}
        ).dict()
    )


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check service status
        services_status = {
            "llm": "healthy" if medical_service.llm else "unhealthy",
            "embeddings": "healthy" if medical_service.embeddings else "unhealthy",
            "qdrant": "healthy" if medical_service.qdrant_client else "unhealthy"
        }
        
        return HealthCheckResponse(
            status="healthy",
            version="1.0.0",
            timestamp=datetime.now().isoformat(),
            services=services_status
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Health check failed")


@app.post("/upload-document", response_model=DocumentUploadResponse)
async def upload_document(file: UploadFile = File(...)):
    """Upload and process a medical document"""
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="Only PDF files are supported")
        
        # Read file content
        file_content = await file.read()
        
        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file uploaded")
        
        # Process the document
        vector_store, collection_name = await medical_service.process_pdf_document(
            file_content, file.filename
        )
        
        if not vector_store or not collection_name:
            raise HTTPException(status_code=500, detail="Failed to process document")
        
        return DocumentUploadResponse(
            success=True,
            message=f"Document '{file.filename}' processed successfully",
            collection_name=collection_name,
            filename=file.filename,
            document_id=str(uuid.uuid4())
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Document processing failed: {str(e)}")


@app.post("/ask-question", response_model=MedicalResponse)
async def ask_question(request: MedicalQuestionRequest):
    """Ask a medical question"""
    try:
        response_data = await medical_service.generate_response(
            query=request.question,
            user_type=request.user_type,
            urgency=request.urgency,
            style=request.style,
            collection_name=request.collection_name
        )
        
        return MedicalResponse(**response_data)
        
    except Exception as e:
        logger.error(f"Question processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process question: {str(e)}")


@app.post("/follow-up-question", response_model=MedicalResponse)
async def ask_follow_up_question(request: FollowUpQuestionRequest):
    """Ask a follow-up question"""
    try:
        response_data = await medical_service.generate_response(
            query=request.question,
            user_type=request.user_type,
            urgency=request.urgency,
            style=request.style,
            collection_name=request.collection_name
        )
        
        return MedicalResponse(**response_data)
        
    except Exception as e:
        logger.error(f"Follow-up question processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process follow-up question: {str(e)}")


@app.get("/sample-questions", response_model=SampleQuestionsResponse)
async def get_sample_questions(collection_name: Optional[str] = None):
    """Get sample questions based on uploaded document or general medical topics"""
    try:
        if collection_name:
            questions = medical_service.get_document_based_questions(collection_name)
            document_based = True
        else:
            # General medical questions
            questions = [
                "What are the common symptoms of diabetes?",
                "How can I improve my cardiovascular health?",
                "What are the side effects of common pain medications?",
                "When should I see a doctor for a persistent cough?"
            ]
            document_based = False
        
        return SampleQuestionsResponse(
            questions=questions,
            document_based=document_based
        )
        
    except Exception as e:
        logger.error(f"Sample questions generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate sample questions: {str(e)}")


@app.delete("/clear-document", response_model=ClearDocumentResponse)
async def clear_document(collection_name: str):
    """Clear a document collection"""
    try:
        success = medical_service.clear_collection(collection_name)
        
        if success:
            return ClearDocumentResponse(
                success=True,
                message="Document collection cleared successfully",
                cleared_collection=collection_name
            )
        else:
            return ClearDocumentResponse(
                success=False,
                message="Failed to clear document collection"
            )
            
    except Exception as e:
        logger.error(f"Document clearing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear document: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
