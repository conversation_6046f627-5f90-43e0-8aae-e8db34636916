from openai import AzureOpenAI
from typing import List, Dict, Any, Optional
import json
import asyncio
from dataclasses import dataclass
from enum import Enum
import time
from document_processing import ResearchPaperProcessor 

class TargetAudience(Enum):
    RESEARCHERS = "researchers"
    CLINICIANS = "clinicians"
    REGULATORY = "regulatory"

class SummaryDepth(Enum):
    BRIEF = "brief"
    DETAILED = "detailed"
    COMPREHENSIVE = "comprehensive"

@dataclass
class SummaryRequest:
    file_path: str
    target_audience: TargetAudience = TargetAudience.RESEARCHERS
    summary_depth: SummaryDepth = SummaryDepth.DETAILED

@dataclass
class ChunkSummary:
    section: str
    executive_summary: str
    chunk_index: str
    content_length: int

@dataclass
class ResearchSummary:
    executive_summaries: List[ChunkSummary]
    key_findings: str
    methodology_highlights: str
    clinical_implications: str
    research_gaps: str
    future_directions: str
    comparative_analysis: str
    processing_metadata: Dict[str, Any]

class ResearchPaperSummarizer:
    def __init__(self, azure_endpoint: str, azure_api_key: str, azure_api_version: str, 
                 deployment_name: str, llama_cloud_api_key: str):
        """
        Initialize the Research Paper Summarizer
        
        Args:
            azure_endpoint: Azure OpenAI endpoint
            azure_api_key: Azure OpenAI API key
            azure_api_version: Azure OpenAI API version
            deployment_name: Azure OpenAI deployment name
            llama_cloud_api_key: LlamaCloud API key for document processing
        """
        self.client = AzureOpenAI(
            azure_endpoint=azure_endpoint,
            api_key=azure_api_key,
            api_version=azure_api_version
        )
        self.deployment_name = deployment_name
        
        # Initialize document processor
        self.document_processor = ResearchPaperProcessor(llama_cloud_api_key)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum seconds between requests

    def _rate_limit(self):
        """Simple rate limiting to avoid API throttling"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time = time.time()

    def _get_chunk_summary_prompt(self, chunk_content: str, section: str, 
                                  target_audience: TargetAudience, 
                                  summary_depth: SummaryDepth) -> str:
        """Generate prompt for chunk-level executive summary"""
        
        audience_context = {
            TargetAudience.RESEARCHERS: "academic researchers and scientists",
            TargetAudience.CLINICIANS: "healthcare practitioners and clinicians",
            TargetAudience.REGULATORY: "regulatory professionals and policy makers"
        }
        
        depth_instruction = {
            SummaryDepth.BRIEF: "Provide a concise 2-3 sentence summary focusing on the most critical points.",
            SummaryDepth.DETAILED: "Provide a comprehensive 4-6 sentence summary covering key points and implications.",
            SummaryDepth.COMPREHENSIVE: "Provide an in-depth 6-8 sentence summary with detailed analysis and context."
        }
        
        return f"""
You are an expert research analyst creating executive summaries for {audience_context[target_audience]}.

TASK: Create an executive summary for this section of a research paper.

SECTION: {section}
SUMMARY DEPTH: {depth_instruction[summary_depth]}

CONTENT TO SUMMARIZE:
{chunk_content}

INSTRUCTIONS:
1. Focus on actionable insights and key takeaways relevant to {audience_context[target_audience]}
2. Highlight methodology, findings, or implications as appropriate for the section
3. Use clear, professional language
4. Maintain scientific accuracy while being accessible
5. If the content contains data/statistics, include the most significant ones
6. If this is a methods section, focus on innovative approaches or key protocols
7. If this is results, emphasize significant findings and their magnitude

EXECUTIVE SUMMARY:
"""

    def _get_comprehensive_analysis_prompt(self, all_summaries: List[ChunkSummary], 
                                           full_document_metadata: Dict[str, Any],
                                           target_audience: TargetAudience) -> str:
        """Generate prompt for comprehensive analysis"""
        
        summaries_text = "\n\n".join([
            f"SECTION: {summary.section}\nSUMMARY: {summary.executive_summary}"
            for summary in all_summaries
        ])
        
        audience_focus = {
            TargetAudience.RESEARCHERS: "Focus on scientific rigor, methodology innovations, and research implications",
            TargetAudience.CLINICIANS: "Emphasize clinical applicability, patient outcomes, and practice implications",
            TargetAudience.REGULATORY: "Highlight regulatory considerations, safety profiles, and compliance aspects"
        }
        
        return f"""
You are an expert research analyst creating a comprehensive analysis for {target_audience.value}.

DOCUMENT METADATA:
- Total sections analyzed: {len(all_summaries)}
- Document has tables: {full_document_metadata.get('has_tables', False)}
- Document has figures: {full_document_metadata.get('has_figures', False)}
- Reference count: {full_document_metadata.get('reference_count', 0)}

SECTION SUMMARIES:
{summaries_text}

TASK: Provide a comprehensive analysis with the following components:

1. KEY FINDINGS AND METHODOLOGY HIGHLIGHTS:
   - Most significant research findings with quantitative results where available
   - Novel methodological approaches or innovations
   - Study design strengths and key protocols
   - Statistical significance and effect sizes

2. CLINICAL IMPLICATIONS:
   - Direct applications to patient care or clinical practice
   - Potential impact on treatment protocols or guidelines
   - Safety considerations and contraindications
   - Population-specific considerations

3. RESEARCH GAPS AND FUTURE DIRECTIONS:
   - Limitations acknowledged by the authors
   - Unexplored areas identified in the research
   - Methodological gaps or improvements needed
   - Sample size or population limitations

4. COMPARATIVE ANALYSIS WITH EXISTING LITERATURE:
   - How findings compare to previous studies
   - Confirmation or contradiction of existing knowledge
   - Novel contributions to the field
   - Position within current research landscape

AUDIENCE FOCUS: {audience_focus[target_audience]}

FORMAT: Provide each section as a well-structured paragraph or set of paragraphs. Use clear headings and maintain scientific accuracy.

ANALYSIS:
"""

    def _call_llm(self, prompt: str, max_tokens: int = 1000) -> str:
        """Make a call to Azure OpenAI with rate limiting"""
        self._rate_limit()
        
        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are an expert research analyst specializing in scientific literature analysis and summarization."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=0.3,
                top_p=0.9
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"Error calling Azure OpenAI: {e}")
            return f"Error generating summary: {str(e)}"

    def summarize_chunks(self, chunks: List, target_audience: TargetAudience, 
                        summary_depth: SummaryDepth) -> List[ChunkSummary]:
        """Generate executive summaries for each chunk"""
        print(f"Generating executive summaries for {len(chunks)} chunks...")
        
        chunk_summaries = []
        
        for i, chunk in enumerate(chunks):
            print(f"Processing chunk {i+1}/{len(chunks)} - Section: {chunk.metadata.get('section', 'unknown')}")
            
            # Skip very small chunks
            if len(chunk.page_content.strip()) < 100:
                continue
            
            prompt = self._get_chunk_summary_prompt(
                chunk.page_content,
                chunk.metadata.get('section', 'unknown'),
                target_audience,
                summary_depth
            )
            
            summary = self._call_llm(prompt, max_tokens=500)
            
            chunk_summary = ChunkSummary(
                section=chunk.metadata.get('section', 'unknown'),
                executive_summary=summary,
                chunk_index=chunk.metadata.get('chunk_index', f'chunk_{i}'),
                content_length=len(chunk.page_content)
            )
            
            chunk_summaries.append(chunk_summary)
        
        print(f"✓ Generated {len(chunk_summaries)} executive summaries")
        return chunk_summaries

    def generate_comprehensive_analysis(self, chunk_summaries: List[ChunkSummary],
                                      document_metadata: Dict[str, Any],
                                      target_audience: TargetAudience) -> Dict[str, str]:
        """Generate comprehensive analysis combining all insights"""
        print("Generating comprehensive analysis...")
        
        prompt = self._get_comprehensive_analysis_prompt(
            chunk_summaries, 
            document_metadata, 
            target_audience
        )
        
        # Use a larger token limit for comprehensive analysis
        analysis_text = self._call_llm(prompt, max_tokens=2000)
        
        # Parse the analysis into components
        # This is a simple parsing approach - you might want to make it more robust
        analysis_parts = {
            'key_findings': '',
            'methodology_highlights': '',
            'clinical_implications': '',
            'research_gaps': '',
            'future_directions': '',
            'comparative_analysis': ''
        }
        
        # Simple parsing based on common patterns
        sections = analysis_text.split('\n\n')
        current_section = None
        
        for section in sections:
            section_lower = section.lower()
            if 'key findings' in section_lower or 'methodology' in section_lower:
                current_section = 'key_findings'
                analysis_parts['key_findings'] += section + '\n\n'
            elif 'clinical implications' in section_lower:
                current_section = 'clinical_implications'
                analysis_parts['clinical_implications'] += section + '\n\n'
            elif 'research gaps' in section_lower or 'future directions' in section_lower:
                current_section = 'research_gaps'
                analysis_parts['research_gaps'] += section + '\n\n'
            elif 'comparative analysis' in section_lower or 'existing literature' in section_lower:
                current_section = 'comparative_analysis'
                analysis_parts['comparative_analysis'] += section + '\n\n'
            else:
                # Add to current section or key findings if no section identified
                if current_section:
                    analysis_parts[current_section] += section + '\n\n'
                else:
                    analysis_parts['key_findings'] += section + '\n\n'
        
        # Clean up the sections
        for key in analysis_parts:
            analysis_parts[key] = analysis_parts[key].strip()
        
        print("✓ Comprehensive analysis generated")
        return analysis_parts

    def process_research_paper(self, request: SummaryRequest) -> ResearchSummary:
        """Complete pipeline to process and summarize a research paper"""
        print(f"Starting research paper summarization for: {request.file_path}")
        print(f"Target audience: {request.target_audience.value}")
        print(f"Summary depth: {request.summary_depth.value}")
        
        try:
            # Step 1: Process document using your existing processor
            processing_result = self.document_processor.process_research_paper(request.file_path)
            
            if processing_result.get("processing_status") != "success":
                raise ValueError(f"Document processing failed: {processing_result.get('error')}")
            
            chunks = processing_result["chunks"]
            document_metadata = processing_result["document_metadata"]
            
            # Step 2: Generate chunk-level executive summaries
            chunk_summaries = self.summarize_chunks(
                chunks, 
                request.target_audience, 
                request.summary_depth
            )
            
            # Step 3: Generate comprehensive analysis
            comprehensive_analysis = self.generate_comprehensive_analysis(
                chunk_summaries,
                document_metadata,
                request.target_audience
            )
            
            # Step 4: Create final summary object
            research_summary = ResearchSummary(
                executive_summaries=chunk_summaries,
                key_findings=comprehensive_analysis.get('key_findings', ''),
                methodology_highlights=comprehensive_analysis.get('key_findings', ''),  # Combined in key_findings
                clinical_implications=comprehensive_analysis.get('clinical_implications', ''),
                research_gaps=comprehensive_analysis.get('research_gaps', ''),
                future_directions=comprehensive_analysis.get('research_gaps', ''),  # Combined in research_gaps
                comparative_analysis=comprehensive_analysis.get('comparative_analysis', ''),
                processing_metadata={
                    **processing_result,
                    'target_audience': request.target_audience.value,
                    'summary_depth': request.summary_depth.value,
                    'total_executive_summaries': len(chunk_summaries)
                }
            )
            
            print("✓ Research paper summarization completed successfully")
            return research_summary
            
        except Exception as e:
            print(f"Error in research paper processing: {e}")
            raise

    def print_summary(self, summary: ResearchSummary):
        """Print a formatted summary to console"""
        print("\n" + "="*80)
        print("RESEARCH PAPER SUMMARY")
        print("="*80)
        
        print(f"\nTARGET AUDIENCE: {summary.processing_metadata.get('target_audience', 'Unknown')}")
        print(f"SUMMARY DEPTH: {summary.processing_metadata.get('summary_depth', 'Unknown')}")
        print(f"TOTAL SECTIONS PROCESSED: {len(summary.executive_summaries)}")
        
        print(f"\nEXECUTIVE SUMMARIES ({len(summary.executive_summaries)} sections):")
        print("-" * 60)
        for i, exec_summary in enumerate(summary.executive_summaries):
            print(f"\n{i+1}. SECTION: {exec_summary.section.upper()}")
            print(f"   Content Length: {exec_summary.content_length} characters")
            print(f"   Summary: {exec_summary.executive_summary}")
        
        print(f"\n\nKEY FINDINGS & METHODOLOGY HIGHLIGHTS:")
        print("-" * 60)
        print(summary.key_findings if summary.key_findings else "No specific findings extracted.")
        
        print(f"\n\nCLINICAL IMPLICATIONS:")
        print("-" * 60)
        print(summary.clinical_implications if summary.clinical_implications else "No clinical implications identified.")
        
        print(f"\n\nRESEARCH GAPS & FUTURE DIRECTIONS:")
        print("-" * 60)
        print(summary.research_gaps if summary.research_gaps else "No research gaps identified.")
        
        print(f"\n\nCOMPARATIVE ANALYSIS WITH EXISTING LITERATURE:")
        print("-" * 60)
        print(summary.comparative_analysis if summary.comparative_analysis else "No comparative analysis available.")
        
        print("\n" + "="*80)

    def save_summary_to_file(self, summary: ResearchSummary, output_file: str):
        """Save the research summary to a JSON file"""
        # Clean processing metadata to remove non-serializable objects
        clean_metadata = {}
        for key, value in summary.processing_metadata.items():
            if key == 'chunks':
                # Convert chunks to serializable format
                clean_metadata[key] = [
                    {
                        'content': chunk.page_content[:200] + '...' if len(chunk.page_content) > 200 else chunk.page_content,
                        'metadata': {k: v for k, v in chunk.metadata.items() if isinstance(v, (str, int, float, bool))}
                    }
                    for chunk in value
                ]
            elif isinstance(value, (str, int, float, bool, list, dict)):
                clean_metadata[key] = value
            else:
                clean_metadata[key] = str(value)
        
        summary_dict = {
            'executive_summaries': [
                {
                    'section': cs.section,
                    'summary': cs.executive_summary,
                    'chunk_index': cs.chunk_index,
                    'content_length': cs.content_length
                }
                for cs in summary.executive_summaries
            ],
            'key_findings_and_methodology': summary.key_findings,
            'clinical_implications': summary.clinical_implications,
            'research_gaps_and_future_directions': summary.research_gaps,
            'comparative_analysis': summary.comparative_analysis,
            'processing_metadata': clean_metadata
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary_dict, f, indent=2, ensure_ascii=False)
        
        print(f"✓ Summary saved to: {output_file}")


# Example usage
if __name__ == "__main__":
    # Azure OpenAI configuration
    
    # # Initialize summarizer
    summarizer = ResearchPaperSummarizer(
        azure_endpoint=AZURE_ENDPOINT,
        azure_api_key=AZURE_API_KEY,
        azure_api_version=AZURE_API_VERSION,
        deployment_name=DEPLOYMENT_NAME,
        llama_cloud_api_key=LLAMA_CLOUD_API_KEY
    )
    
    # Create summary request
    request = SummaryRequest(
        file_path="C:\\Users\\<USER>\\Desktop\\Research-Summarization\\medical_pdf.pdf",
        target_audience=TargetAudience.CLINICIANS,
        summary_depth=SummaryDepth.DETAILED
    )
    
    try:
        # Process the research paper
        summary = summarizer.process_research_paper(request)
        
        # Print formatted results
        summarizer.print_summary(summary)
        
        # Save to file
        summarizer.save_summary_to_file(summary, "research_summary.json")
        
    except Exception as e:
        print(f"Processing failed: {e}")





