# requirements.txt
# langchain==0.1.0
# langchain-community==0.0.10
# llama-parse==0.4.0
# llama-index==0.9.0
# nest-asyncio==1.5.8

from llama_parse import LlamaParse
from langchain.text_splitter import RecursiveCharacterTextSplitter, MarkdownHeaderTextSplitter
from langchain.schema import Document
from typing import List, Dict, Any, Optional
import asyncio
import nest_asyncio
from pathlib import Path
import json

# Enable nested asyncio (needed for Jupyter/some environments)
nest_asyncio.apply()

class ResearchPaperProcessor:
    def __init__(self, llama_cloud_api_key: str):
        """
        Initialize with LlamaParse for intelligent document parsing
        
        Args:
            llama_cloud_api_key: Your LlamaCloud API key from https://cloud.llamaindex.ai
        """
        self.llama_parser = LlamaParse(
            api_key=llama_cloud_api_key,
            result_type="markdown",  # Get structured markdown output
            verbose=True,
            language="en",
            parsing_instruction="""
            This is a scientific research paper. Please:
            1. Preserve the document structure with clear section headers
            2. Maintain all tables, figures, and their captions
            3. Keep mathematical formulas and statistical data intact
            4. Preserve reference formatting
            5. Use proper markdown formatting for sections (# ## ###)
            6. Identify and mark key sections: Abstract, Introduction, Methods, Results, Discussion, Conclusion
            """
        )
        
        # Text splitter optimized for research papers
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1500,
            chunk_overlap=200,
            separators=["\n\n", "\n", ". ", " ", ""],
            length_function=len,
        )
        
        # Markdown header splitter for structured content
        self.markdown_splitter = MarkdownHeaderTextSplitter(
            headers_to_split_on=[
                ("#", "Header 1"),
                ("##", "Header 2"), 
                ("###", "Header 3"),
                ("####", "Header 4"),
            ]
        )
        
        # Section mapping for research papers
        self.section_mappings = {
            'abstract': ['abstract', 'summary'],
            'introduction': ['introduction', 'background'],
            'methods': ['methods', 'methodology', 'materials and methods', 'experimental design'],
            'results': ['results', 'findings'],
            'discussion': ['discussion', 'analysis'],
            'conclusion': ['conclusion', 'conclusions', 'summary and conclusions'],
            'references': ['references', 'bibliography', 'citations']
        }

    async def parse_document_async(self, file_path: str) -> str:
        """Parse PDF using LlamaParse asynchronously"""
        try:
            print(f"Parsing document with LlamaParse: {file_path}")
            documents = await self.llama_parser.aload_data(file_path)
            
            if not documents:
                raise ValueError("No content extracted from document")
            
            # Combine all pages into structured markdown
            full_content = ""
            for doc in documents:
                full_content += doc.text + "\n\n"
            
            print(f"✓ Document parsed successfully. Content length: {len(full_content)} characters")
            return full_content
            
        except Exception as e:
            print(f"Error parsing document with LlamaParse: {e}")
            raise

    def parse_document(self, file_path: str) -> str:
        """Synchronous wrapper for document parsing"""
        return asyncio.run(self.parse_document_async(file_path))

    def extract_sections_from_markdown(self, markdown_content: str) -> Dict[str, str]:
        """Extract sections from LlamaParse markdown output"""
        sections = {}
        
        try:
            # Split by markdown headers using the splitter
            header_chunks = self.markdown_splitter.split_text(markdown_content)
            
            current_section = None
            section_content = []
            
            for chunk in header_chunks:
                chunk_text = chunk.page_content if hasattr(chunk, 'page_content') else str(chunk)
                chunk_metadata = chunk.metadata if hasattr(chunk, 'metadata') else {}
                
                # Check if this chunk starts a new section
                header_info = self._identify_section_from_metadata(chunk_metadata)
                
                if header_info:
                    # Save previous section
                    if current_section and section_content:
                        sections[current_section] = "\n\n".join(section_content)
                    
                    # Start new section
                    current_section = header_info
                    section_content = [chunk_text]
                else:
                    # Add to current section
                    if current_section:
                        section_content.append(chunk_text)
                    else:
                        # Handle content before first header
                        if not sections.get('introduction'):
                            sections['introduction'] = chunk_text
            
            # Don't forget the last section
            if current_section and section_content:
                sections[current_section] = "\n\n".join(section_content)
            
            print(f"✓ Sections extracted: {list(sections.keys())}")
            return sections
            
        except Exception as e:
            print(f"Error extracting sections: {e}")
            # Fallback: try to parse manually
            return self._fallback_section_extraction(markdown_content)

    def _identify_section_from_metadata(self, metadata: Dict) -> Optional[str]:
        """Identify section type from chunk metadata"""
        for header_key in ["Header 1", "Header 2", "Header 3"]:
            if header_key in metadata:
                header_text = metadata[header_key].lower().strip()
                
                # Map header text to standard section
                for section, variations in self.section_mappings.items():
                    if any(var in header_text for var in variations):
                        return section
        
        return None

    def _fallback_section_extraction(self, content: str) -> Dict[str, str]:
        """Fallback method to extract sections using simple parsing"""
        sections = {}
        lines = content.split('\n')
        
        current_section = None
        section_lines = []
        
        for line in lines:
            line_lower = line.lower().strip()
            
            # Check if line is a header
            if line.startswith('#') and any(
                any(var in line_lower for var in variations) 
                for variations in self.section_mappings.values()
            ):
                # Save previous section
                if current_section and section_lines:
                    sections[current_section] = '\n'.join(section_lines)
                
                # Identify new section
                for section, variations in self.section_mappings.items():
                    if any(var in line_lower for var in variations):
                        current_section = section
                        section_lines = []
                        break
            else:
                if current_section:
                    section_lines.append(line)
        
        # Don't forget the last section
        if current_section and section_lines:
            sections[current_section] = '\n'.join(section_lines)
        
        return sections

    def create_enhanced_chunks(self, markdown_content: str, file_path: str) -> List[Document]:
        """Create enhanced chunks with rich metadata from parsed content"""
        all_chunks = []
        
        # Extract sections
        sections = self.extract_sections_from_markdown(markdown_content)
        
        if not sections:
            print("Warning: No sections found, using full content")
            sections = {'full_document': markdown_content}
        
        # Process each section
        for section_name, section_content in sections.items():
            if not section_content.strip():
                continue
                
            # Use markdown splitter first for structured splitting
            try:
                structured_chunks = self.markdown_splitter.split_text(section_content)
            except:
                # Fallback to text-based chunks
                structured_chunks = [Document(page_content=section_content)]
            
            # Process each structured chunk
            for chunk_idx, chunk in enumerate(structured_chunks):
                chunk_text = chunk.page_content if hasattr(chunk, 'page_content') else str(chunk)
                chunk_metadata = chunk.metadata if hasattr(chunk, 'metadata') else {}
                
                # Further split if too large
                if len(chunk_text) > self.text_splitter._chunk_size:
                    sub_chunks = self.text_splitter.split_text(chunk_text)
                    
                    for sub_idx, sub_chunk_text in enumerate(sub_chunks):
                        enhanced_chunk = Document(
                            page_content=sub_chunk_text,
                            metadata={
                                **chunk_metadata,
                                'source': file_path,
                                'section': section_name,
                                'chunk_index': f"{section_name}_{chunk_idx}_{sub_idx}",
                                'chunk_type': 'enhanced_chunk',
                                'section_hierarchy': self._get_section_hierarchy(section_name),
                                'parsing_method': 'llama_parse',
                                'has_structured_content': bool(chunk_metadata),
                                'content_length': len(sub_chunk_text)
                            }
                        )
                        all_chunks.append(enhanced_chunk)
                else:
                    enhanced_chunk = Document(
                        page_content=chunk_text,
                        metadata={
                            **chunk_metadata,
                            'source': file_path,
                            'section': section_name,
                            'chunk_index': f"{section_name}_{chunk_idx}",
                            'chunk_type': 'enhanced_chunk',
                            'section_hierarchy': self._get_section_hierarchy(section_name),
                            'parsing_method': 'llama_parse',
                            'has_structured_content': bool(chunk_metadata),
                            'content_length': len(chunk_text)
                        }
                    )
                    all_chunks.append(enhanced_chunk)
        
        print(f"✓ Created {len(all_chunks)} enhanced chunks")
        return all_chunks

    def _get_section_hierarchy(self, section_name: str) -> int:
        """Get hierarchical importance of section"""
        hierarchy = {
            'abstract': 1,
            'introduction': 2,
            'methods': 3,
            'results': 4,
            'discussion': 5,
            'conclusion': 6,
            'references': 7,
            'full_document': 99
        }
        return hierarchy.get(section_name, 50)

    def process_research_paper(self, file_path: str) -> Dict[str, Any]:
        """Complete processing pipeline using LlamaParse"""
        print(f"Processing research paper with LlamaParse: {file_path}")
        
        try:
            # Step 1: Parse with LlamaParse
            markdown_content = self.parse_document(file_path)
            
            if not markdown_content:
                return {"error": "Failed to parse document content"}
            
            # Step 2: Create enhanced chunks
            chunks = self.create_enhanced_chunks(markdown_content, file_path)
            
            if not chunks:
                return {"error": "Failed to create chunks from parsed content"}
            
            # Step 3: Analyze document structure
            sections_found = set()
            section_distribution = {}
            
            for chunk in chunks:
                section = chunk.metadata.get('section', 'unknown')
                sections_found.add(section)
                section_distribution[section] = section_distribution.get(section, 0) + 1
            
            # Step 4: Extract document metadata
            doc_metadata = self._extract_document_metadata(markdown_content)
            
            result = {
                "file_path": file_path,
                "parsing_method": "llama_parse",
                "total_chunks": len(chunks),
                "sections_found": list(sections_found),
                "section_distribution": section_distribution,
                "document_metadata": doc_metadata,
                "chunks": chunks,
                "processing_status": "success",
                "content_preview": markdown_content[:500] + "..." if len(markdown_content) > 500 else markdown_content
            }
            
            print(f"✓ Processing complete: {len(chunks)} chunks created")
            print(f"✓ Sections found: {sections_found}")
            
            return result
            
        except Exception as e:
            print(f"Error in processing pipeline: {e}")
            return {"error": f"Processing failed: {str(e)}"}

    def _extract_document_metadata(self, content: str) -> Dict[str, Any]:
        """Extract document metadata from parsed content"""
        metadata = {
            "total_length": len(content),
            "estimated_pages": len(content) // 3000,  # Rough estimate
            "has_tables": "table" in content.lower() or "|" in content,
            "has_figures": "figure" in content.lower() or "fig." in content.lower(),
            "has_equations": "$" in content or "equation" in content.lower(),
            "reference_count": content.lower().count("reference") + content.lower().count("citation")
        }
        
        return metadata


# Example usage
if __name__ == "__main__":
    # Initialize processor with LlamaCloud API key
    processor = ResearchPaperProcessor(llama_cloud_api_key="llx-9OK9vKfZb71jAdqasUK0GtIVBZ2OJHDPnKBJc0gOBmMivT7i")
    
    # Test with a research paper PDF
    file_path = "C:\\Users\\<USER>\\Desktop\\Research-Summarization\\medical_pdf.pdf"
    result = processor.process_research_paper(file_path)
    
    if result.get("processing_status") == "success":
        print(f"Successfully processed: {result['total_chunks']} chunks")
        print(f"Sections: {result['sections_found']}")
        print(f"Document metadata: {result['document_metadata']}")
        
        # Show sample chunks
        for i, chunk in enumerate(result['chunks'][:3]):
            print(f"\nChunk {i+1} (Section: {chunk.metadata['section']}):")
            print(f"Content preview: {chunk.page_content[:200]}...")
    else:
        print(f"Processing failed: {result.get('error')}")