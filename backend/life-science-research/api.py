from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import tempfile
import os
import json
from typing import Optional, Dict, Any, List
import uuid
from datetime import datetime
import shutil

# Import your backend classes
from main import (
    ResearchPaperSummarizer, 
    SummaryRequest, 
    TargetAudience, 
    SummaryDepth,
    ResearchSummary,
    ChunkSummary
)

# Create FastAPI app
app = FastAPI(
    title="Research Paper Summarizer API",
    description="API for summarizing research papers with audience-specific insights",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store in-memory results (replace with database in production)
summary_results = {}

# Configuration
AZURE_ENDPOINT = os.getenv("AZURE_ENDPOINT") 
AZURE_API_KEY = os.getenv("AZURE_API_KEY") 
AZURE_API_VERSION = os.getenv("AZURE_API_VERSION")
DEPLOYMENT_NAME = os.getenv("DEPLOYMENT_NAME") 
LLAMA_CLOUD_API_KEY = os.getenv("LLAMA_CLOUD_API_KEY")

# Pydantic models for API requests and responses
class SummaryRequestModel(BaseModel):
    target_audience: str = Field(
        default="researchers", 
        description="Target audience for the summary (researchers, clinicians, regulatory)"
    )
    summary_depth: str = Field(
        default="detailed", 
        description="Depth of the summary (brief, detailed, comprehensive)"
    )

class SummaryStatusResponse(BaseModel):
    task_id: str
    status: str
    message: str

class ChunkSummaryModel(BaseModel):
    section: str
    executive_summary: str
    chunk_index: str
    content_length: int

class SummaryResultResponse(BaseModel):
    task_id: str
    status: str
    executive_summaries: List[ChunkSummaryModel]
    key_findings: str
    methodology_highlights: str
    clinical_implications: str
    research_gaps: str
    future_directions: str
    comparative_analysis: str
    processing_metadata: Dict[str, Any]
    timestamp: str

def get_summarizer():
    """Initialize the research paper summarizer"""
    if not all([AZURE_ENDPOINT, AZURE_API_KEY, AZURE_API_VERSION, DEPLOYMENT_NAME, LLAMA_CLOUD_API_KEY]):
        raise HTTPException(
            status_code=500, 
            detail="Missing API configuration. Please set all required environment variables."
        )
    
    try:
        summarizer = ResearchPaperSummarizer(
            azure_endpoint=AZURE_ENDPOINT,
            azure_api_key=AZURE_API_KEY,
            azure_api_version=AZURE_API_VERSION,
            deployment_name=DEPLOYMENT_NAME,
            llama_cloud_api_key=LLAMA_CLOUD_API_KEY
        )
        return summarizer
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initialize summarizer: {str(e)}")

def process_paper_task(task_id: str, file_path: str, target_audience: str, summary_depth: str):
    """Background task to process the research paper"""
    try:
        # Map string values to enum values
        audience_map = {
            "researchers": TargetAudience.RESEARCHERS,
            "clinicians": TargetAudience.CLINICIANS,
            "regulatory": TargetAudience.REGULATORY
        }
        
        depth_map = {
            "brief": SummaryDepth.BRIEF,
            "detailed": SummaryDepth.DETAILED,
            "comprehensive": SummaryDepth.COMPREHENSIVE
        }
        
        # Create summary request
        request = SummaryRequest(
            file_path=file_path,
            target_audience=audience_map.get(target_audience, TargetAudience.RESEARCHERS),
            summary_depth=depth_map.get(summary_depth, SummaryDepth.DETAILED)
        )
        
        # Initialize summarizer
        summarizer = get_summarizer()
        
        # Process the paper
        summary = summarizer.process_research_paper(request)
        
        # Store result
        summary_results[task_id] = {
            "task_id": task_id,
            "status": "completed",
            "executive_summaries": [
                {
                    "section": cs.section,
                    "executive_summary": cs.executive_summary,
                    "chunk_index": cs.chunk_index,
                    "content_length": cs.content_length
                }
                for cs in summary.executive_summaries
            ],
            "key_findings": summary.key_findings,
            "methodology_highlights": summary.methodology_highlights,
            "clinical_implications": summary.clinical_implications,
            "research_gaps": summary.research_gaps,
            "future_directions": summary.future_directions,
            "comparative_analysis": summary.comparative_analysis,
            "processing_metadata": summary.processing_metadata,
            "timestamp": datetime.now().isoformat()
        }
        
        # Clean up temporary file
        os.unlink(file_path)
        
    except Exception as e:
        # Store error
        summary_results[task_id] = {
            "task_id": task_id,
            "status": "failed",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
        
        # Clean up temporary file
        if os.path.exists(file_path):
            os.unlink(file_path)

@app.get("/")
async def root():
    return {"message": "Research Paper Summarizer API"}

@app.post("/api/summarize", response_model=SummaryStatusResponse)
async def summarize_paper(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    target_audience: str = Form("researchers"),
    summary_depth: str = Form("detailed")
):
    """
    Submit a research paper for summarization
    
    - **file**: PDF file of the research paper
    - **target_audience**: Target audience (researchers, clinicians, regulatory)
    - **summary_depth**: Depth of summary (brief, detailed, comprehensive)
    """
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are supported")
    
    # Save uploaded file to temporary directory
    try:
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, file.filename)
        
        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving file: {str(e)}")
    
    # Generate task ID
    task_id = str(uuid.uuid4())
    
    # Store initial status
    summary_results[task_id] = {
        "task_id": task_id,
        "status": "processing",
        "message": "Research paper processing started",
        "timestamp": datetime.now().isoformat()
    }
    
    # Start background processing
    background_tasks.add_task(
        process_paper_task,
        task_id=task_id,
        file_path=file_path,
        target_audience=target_audience,
        summary_depth=summary_depth
    )
    
    return SummaryStatusResponse(
        task_id=task_id,
        status="processing",
        message="Research paper processing started"
    )

@app.get("/api/status/{task_id}", response_model=SummaryStatusResponse)
async def get_status(task_id: str):
    """
    Get the status of a summarization task
    
    - **task_id**: ID of the summarization task
    """
    if task_id not in summary_results:
        raise HTTPException(status_code=404, detail="Task not found")
    
    result = summary_results[task_id]
    
    if result["status"] == "processing":
        return SummaryStatusResponse(
            task_id=task_id,
            status="processing",
            message="Research paper processing in progress"
        )
    elif result["status"] == "completed":
        return SummaryStatusResponse(
            task_id=task_id,
            status="completed",
            message="Research paper processing completed"
        )
    else:
        return SummaryStatusResponse(
            task_id=task_id,
            status="failed",
            message=f"Processing failed: {result.get('error', 'Unknown error')}"
        )

@app.get("/api/result/{task_id}", response_model=SummaryResultResponse)
async def get_result(task_id: str):
    """
    Get the result of a completed summarization task
    
    - **task_id**: ID of the summarization task
    """
    if task_id not in summary_results:
        raise HTTPException(status_code=404, detail="Task not found")
    
    result = summary_results[task_id]
    
    if result["status"] == "processing":
        raise HTTPException(status_code=202, detail="Processing in progress")
    elif result["status"] == "failed":
        raise HTTPException(status_code=500, detail=f"Processing failed: {result.get('error', 'Unknown error')}")
    
    return result

@app.delete("/api/result/{task_id}")
async def delete_result(task_id: str):
    """
    Delete a summarization result
    
    - **task_id**: ID of the summarization task
    """
    if task_id not in summary_results:
        raise HTTPException(status_code=404, detail="Task not found")
    
    del summary_results[task_id]
    
    return {"message": "Result deleted successfully"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)

