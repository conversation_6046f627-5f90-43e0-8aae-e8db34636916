"""
FastAPI main application for Medical Report Analysis
"""

import logging
from datetime import datetime
from typing import List, Optional
import uuid

from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from config import settings
from models import (
    ReportAnalysisRequest,
    ReportAnalysisResponse,
    DocumentUploadResponse,
    FollowUpQuestionRequest,
    FollowUpQuestionResponse,
    HealthCheckResponse,
    ErrorResponse
)
from medical_report_service import medical_report_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Medical Report Analysis API",
    description="AI-powered medical report analysis system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Global exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            message="An unexpected error occurred",
            details={"exception": str(exc)}
        ).dict()
    )


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check service status
        services_status = {
            "llm": "healthy" if medical_report_service.llm else "unhealthy",
            "embeddings": "healthy" if medical_report_service.embeddings else "unhealthy",
            "qdrant": "healthy" if medical_report_service.qdrant_client else "unhealthy",
            "azure_doc_intelligence": "healthy" if medical_report_service.azure_endpoint else "unhealthy"
        }
        
        return HealthCheckResponse(
            status="healthy",
            version="1.0.0",
            timestamp=datetime.now().isoformat(),
            services=services_status
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Health check failed")


@app.post("/upload-report", response_model=DocumentUploadResponse)
async def upload_report(file: UploadFile = File(...)):
    """Upload and process a medical report"""
    try:
        # Validate file type
        allowed_types = ['image/png', 'image/jpg', 'image/jpeg', 'image/tiff', 'image/bmp', 'application/pdf']
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400, 
                detail="Only PNG, JPG, JPEG, TIFF, BMP, and PDF files are supported"
            )
        
        # Read file content
        file_content = await file.read()
        
        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file uploaded")
        
        # Process the document
        extracted_text, vector_store = await medical_report_service.process_medical_report(
            file_content, file.filename, file.content_type
        )
        
        if not extracted_text or not vector_store:
            raise HTTPException(status_code=500, detail="Failed to process medical report")
        
        # Generate automatic analyses
        analyses = await medical_report_service.generate_automatic_analyses(vector_store)
        
        return DocumentUploadResponse(
            success=True,
            message=f"Medical report '{file.filename}' processed successfully",
            filename=file.filename,
            document_id=str(uuid.uuid4()),
            extracted_text=extracted_text,
            comprehensive_analysis=analyses.get('comprehensive'),
            key_findings=analyses.get('key_findings'),
            abnormal_findings=analyses.get('abnormal')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Report upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Report processing failed: {str(e)}")


@app.post("/ask-question", response_model=FollowUpQuestionResponse)
async def ask_question(request: FollowUpQuestionRequest):
    """Ask a specific question about the medical report"""
    try:
        if not request.extracted_text:
            raise HTTPException(status_code=400, detail="No medical report text provided")
        
        # Create vector store from extracted text
        vector_store = await medical_report_service.create_vector_store_from_text(request.extracted_text)
        
        if not vector_store:
            raise HTTPException(status_code=500, detail="Failed to create analysis system")
        
        # Get answer to the question
        answer = await medical_report_service.analyze_medical_report(vector_store, request.question)
        
        if not answer:
            raise HTTPException(status_code=500, detail="Failed to analyze question")
        
        return FollowUpQuestionResponse(
            answer=answer,
            question=request.question,
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Question processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process question: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
