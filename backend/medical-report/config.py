"""
Configuration management for Medical Report Analysis API
"""

from typing import List
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings"""

    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8003
    debug: bool = True

    # LiteLLM Configuration
    litellm_api_key: str
    litellm_base_url: str
    litellm_model: str

    # Azure AI Document Intelligence Configuration
    azure_endpoint: str
    azure_key: str

    # Azure OpenAI Embeddings Configuration
    openai_api_key: str
    azure_deployment: str
    azure_openai_endpoint: str
    openai_api_version: str = "2023-05-15"

    # Qdrant Configuration
    qdrant_url: str
    qdrant_api_key: str
    
    # CORS Configuration
    allowed_origins: List[str] = [
        "http://localhost:8080",
        "http://localhost:8081",
        "http://localhost:3000",
        "http://127.0.0.1:8080",
        "http://127.0.0.1:8081"
    ]
    
    # LLM Configuration
    llm_temperature: float = 0.1
    llm_max_tokens: int = 1000
    
    # Text Splitting Configuration
    chunk_size: int = 800
    chunk_overlap: int = 100
    
    # Retrieval Configuration
    retrieval_k: int = 2
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
