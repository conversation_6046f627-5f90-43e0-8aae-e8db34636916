"""
Medical Report Analysis Service
"""

import logging
import tempfile
import os
from typing import Op<PERSON>, Dict, Tuple
import asyncio

from qdrant_client import QdrantClient
from langchain_community.vectorstores import Qdrant
from langchain_openai import AzureOpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from langchain_community.chat_models import ChatLiteLLM
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain_community.document_loaders import AzureAIDocumentIntelligenceLoader

from config import settings

logger = logging.getLogger(__name__)


class MedicalReportService:
    """Service for medical report analysis"""
    
    def __init__(self):
        """Initialize the medical report service"""
        self.llm = None
        self.embeddings = None
        self.qdrant_client = None
        self.azure_endpoint = None
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialize all required services"""
        try:
            # Initialize LiteLLM
            self.llm = ChatLiteLLM(
                model=settings.litellm_model,
                api_key=settings.litellm_api_key,
                api_base=settings.litellm_base_url,
                temperature=settings.llm_temperature,
                max_tokens=settings.llm_max_tokens
            )
            
            # Initialize Azure OpenAI embeddings
            self.embeddings = AzureOpenAIEmbeddings(
                openai_api_key=settings.openai_api_key,
                azure_deployment=settings.azure_deployment,
                azure_endpoint=settings.azure_openai_endpoint,
                openai_api_version=settings.openai_api_version,
                chunk_size=2048,
            )
            
            # Initialize Qdrant client
            self.qdrant_client = QdrantClient(
                url=settings.qdrant_url,
                api_key=settings.qdrant_api_key
            )
            
            # Store Azure endpoint for health checks
            self.azure_endpoint = settings.azure_endpoint
            
            logger.info("Medical report service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize medical report service: {str(e)}")
            raise
    
    async def extract_text_from_file(self, file_content: bytes, filename: str, content_type: str) -> Optional[str]:
        """Extract text from uploaded file using Azure AI Document Intelligence"""
        try:
            # Determine file extension
            if content_type.startswith('image/'):
                suffix = '.png'
            elif content_type == 'application/pdf':
                suffix = '.pdf'
            else:
                suffix = '.png'  # Default fallback
            
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
                tmp_file.write(file_content)
                tmp_file_path = tmp_file.name
            
            # Initialize Azure AI Document Intelligence loader
            loader = AzureAIDocumentIntelligenceLoader(
                api_endpoint=settings.azure_endpoint,
                api_key=settings.azure_key,
                file_path=tmp_file_path,
                api_model="prebuilt-layout",
                mode="markdown"
            )
            
            # Load and extract text
            documents = loader.load()
            
            # Clean up temporary file
            os.unlink(tmp_file_path)
            
            # Combine all document content
            extracted_text = "\n".join([doc.page_content for doc in documents])
            return extracted_text
            
        except Exception as e:
            logger.error(f"Error in extracting text from file: {str(e)}")
            return None
    
    async def create_vector_store_from_text(self, text: str) -> Optional[Qdrant]:
        """Create vector store from extracted text"""
        try:
            # Split text into smaller chunks
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=settings.chunk_size,
                chunk_overlap=settings.chunk_overlap
            )
            chunks = text_splitter.split_text(text)
            
            # Create documents
            documents = [Document(page_content=chunk) for chunk in chunks]
            
            # Create vector store with Qdrant Cloud
            vector_store = Qdrant.from_documents(
                documents,
                self.embeddings,
                url=settings.qdrant_url,
                api_key=settings.qdrant_api_key,
                collection_name="medical_reports_analysis",
                force_recreate=True
            )
            
            return vector_store
            
        except Exception as e:
            logger.error(f"Error creating vector store: {str(e)}")
            return None
    
    async def analyze_medical_report(self, vector_store: Qdrant, query: str) -> Optional[str]:
        """Analyze medical report using LiteLLM"""
        try:
            # Create prompt template
            prompt_template = """
            You are a medical AI assistant. Based on the medical report context provided, answer the question appropriately.

            IMPORTANT: Provide your response in plain text format only. Do not use markdown formatting, bullet points, or special characters.

            - For simple factual questions (like patient name, date, specific values): Give a direct, concise answer
            - For analysis questions (like "comprehensive analysis", "key findings", "abnormal findings", "recommendations"): Provide detailed analysis with:
              1. Key medical findings
              2. Potential concerns or abnormalities
              3. Recommendations for follow-up
              4. Overall assessment

            Medical Report Context: {context}

            Question: {question}

            Answer (in plain text format):
            """
            
            prompt = PromptTemplate(
                template=prompt_template,
                input_variables=["context", "question"]
            )
            
            # Create retrieval QA chain
            qa_chain = RetrievalQA.from_chain_type(
                llm=self.llm,
                chain_type="stuff",
                retriever=vector_store.as_retriever(search_kwargs={"k": settings.retrieval_k}),
                chain_type_kwargs={"prompt": prompt}
            )
            
            # Get response
            response = qa_chain.run(query)
            return response
            
        except Exception as e:
            logger.error(f"Error in medical report analysis: {str(e)}")
            return None

    async def generate_automatic_analyses(self, vector_store: Qdrant) -> Dict[str, Optional[str]]:
        """Generate the three automatic analyses"""
        analyses = {}

        try:
            # Comprehensive Analysis
            logger.info("Generating comprehensive analysis...")
            analyses['comprehensive'] = await self.analyze_medical_report(
                vector_store,
                "Provide a comprehensive analysis of this medical report"
            )

            # Key Findings
            logger.info("Extracting key findings...")
            analyses['key_findings'] = await self.analyze_medical_report(
                vector_store,
                "What are the key findings in this report?"
            )

            # Abnormal Findings
            logger.info("Checking for abnormal findings...")
            analyses['abnormal'] = await self.analyze_medical_report(
                vector_store,
                "Are there any abnormal values or concerning findings?"
            )

            return analyses

        except Exception as e:
            logger.error(f"Error generating automatic analyses: {str(e)}")
            return {
                'comprehensive': None,
                'key_findings': None,
                'abnormal': None
            }

    async def process_medical_report(self, file_content: bytes, filename: str, content_type: str) -> Tuple[Optional[str], Optional[Qdrant]]:
        """Process medical report: extract text and create vector store"""
        try:
            # Extract text from file
            extracted_text = await self.extract_text_from_file(file_content, filename, content_type)

            if not extracted_text:
                logger.error("Failed to extract text from file")
                return None, None

            # Create vector store
            vector_store = await self.create_vector_store_from_text(extracted_text)

            if not vector_store:
                logger.error("Failed to create vector store")
                return extracted_text, None

            return extracted_text, vector_store

        except Exception as e:
            logger.error(f"Error processing medical report: {str(e)}")
            return None, None


# Global service instance
medical_report_service = MedicalReportService()
