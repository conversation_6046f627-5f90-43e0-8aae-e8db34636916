"""
Pydantic models for Medical Report Analysis API
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from enum import Enum


class ReportAnalysisRequest(BaseModel):
    """Request model for medical report analysis"""
    extracted_text: str = Field(..., min_length=1, description="Extracted text from medical report")
    analysis_type: str = Field(default="comprehensive", description="Type of analysis to perform")


class DocumentUploadResponse(BaseModel):
    """Response model for document upload"""
    success: bool = Field(..., description="Whether the upload was successful")
    message: str = Field(..., description="Status message")
    filename: Optional[str] = Field(default=None, description="Original filename")
    document_id: Optional[str] = Field(default=None, description="Unique document identifier")
    extracted_text: Optional[str] = Field(default=None, description="Extracted text from the document")
    comprehensive_analysis: Optional[str] = Field(default=None, description="Comprehensive analysis of the report")
    key_findings: Optional[str] = Field(default=None, description="Key findings from the report")
    abnormal_findings: Optional[str] = Field(default=None, description="Abnormal findings from the report")


class ReportAnalysisResponse(BaseModel):
    """Response model for medical report analysis"""
    comprehensive_analysis: str = Field(..., description="Comprehensive analysis of the medical report")
    key_findings: str = Field(..., description="Key findings from the medical report")
    abnormal_findings: str = Field(..., description="Abnormal findings and concerns")
    extracted_text: str = Field(..., description="Extracted text from the report")
    timestamp: str = Field(..., description="Analysis timestamp")
    disclaimer: str = Field(
        default="⚠️ Disclaimer: This tool is for educational purposes only. Always consult healthcare professionals for medical advice.",
        description="Medical disclaimer"
    )


class FollowUpQuestionRequest(BaseModel):
    """Request model for follow-up questions"""
    question: str = Field(..., min_length=1, max_length=5000, description="The follow-up question about the medical report")
    extracted_text: str = Field(..., min_length=1, description="Extracted text from the medical report")


class FollowUpQuestionResponse(BaseModel):
    """Response model for follow-up questions"""
    answer: str = Field(..., description="Answer to the follow-up question")
    question: str = Field(..., description="The original question")
    timestamp: str = Field(..., description="Response timestamp")
    disclaimer: str = Field(
        default="⚠️ Disclaimer: This tool is for educational purposes only. Always consult healthcare professionals for medical advice.",
        description="Medical disclaimer"
    )


class HealthCheckResponse(BaseModel):
    """Response model for health check"""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="API version")
    timestamp: str = Field(..., description="Current timestamp")
    services: Dict[str, str] = Field(..., description="Status of dependent services")


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
