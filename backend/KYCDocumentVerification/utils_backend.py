import cv2
import os
import json
import random
import datetime
import re
import io
from PIL import Image
import numpy as np
from pathlib import Path
import requests
import base64
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# AI Planet GenAI Stack Configuration
AIPLANET_APP_ID = os.getenv('AIPLANET_APP_ID')
AIPLANET_TOKEN = os.getenv('AIPLANET_TOKEN')
AIPLANET_BASE_URL = os.getenv('AIPLANET_BASE_URL', 'https://app.aiplanet.com/api/v1')

if AIPLANET_APP_ID and AIPLANET_TOKEN:
    print(f"✅ AI Planet GenAI Stack configured: App ID {AIPLANET_APP_ID[:8]}...")
else:
    print("❌ AI Planet configuration missing!")

def convert_pdf_to_image(pdf_bytes):
    """Convert PDF bytes to PIL Image (first page only) - Currently not supported"""
    raise Exception("PDF processing not currently supported. Please upload an image file (.jpg, .png, etc.)")

def call_aiplanet_api_with_files(id_document_path, kyc_form_path):
    """Call AI Planet GenAI Stack API with document files (using working curl authentication)"""
    try:
        url = f"{AIPLANET_BASE_URL}/{AIPLANET_APP_ID}/generations"
        
        # Headers from working curl command
        headers = {
            "Authorization": AIPLANET_TOKEN,  # Token from environment variable
            "Accept": "application/json",
            "Accept-Language": "en-GB,en-US;q=0.9,en;q=0.8",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Referer": f"https://app.aiplanet.com/apps/{AIPLANET_APP_ID}/api-reference",
            "Sec-CH-UA": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            "Sec-CH-UA-Mobile": "?0",
            "Sec-CH-UA-Platform": '"macOS"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        }
        
        # Cookies from working curl command (critical for authentication)
        cookies = {
            "_ga": "GA1.1.679517583.1748181569",
            "_ga_522VNBMBVW": "GS2.1.s1750400506$o5$g1$t1750400570$j60$l0$h0",
            "auth": "0",
            "access_tkn_lflw": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlNDQxMDRjNi03MzZjLTQwMjgtODIyZi03OGVhNzI0ZTg3YTkiLCJleHAiOjE3NTEwOTU5NDh9.7NksoHXieAtvB9SwL6LuZv1d_fzjsy7a8uvkh_JdIhU",
            "refresh_tkn_lflw": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlNDQxMDRjNi03MzZjLTQwMjgtODIyZi03OGVhNzI0ZTg3YTkiLCJ0eXBlIjoicmYiLCJleHAiOjE3NTEwOTU5NDh9.1gd9eV2-rwlkh5FbnvBIXjxtIxbh_lvbkvA2jHz-rc4",
            "cf_clearance": "ikAAbSCrw5fYZRolZfMU97c5uS3R3NQ7EmGD4vGWLXw-1750855115-1.2.1.1-3F0wgrM9_djZlQVt0RTdQgvL5tZTEg6W9wlX.vqfsLfKodKCyCjPn9Jn4RJ5ysCb6HRAudd.xliRzsgP2IZd2mY_q2zkNvDhVewFbrSrxWUTWAtUB17rcf21k4buCMJ_gsjbK_NDPWydDaslF2sP5JMQedm3npu.xxnBjCL9795PS0J8Lxxi.HGe7LnEVACRoWMweZ3GKOqRrMXI.d9j0_1whO7sUk341MIy2nb9lpf3kyS5mqLzLZwNuw6iCzTIbtNVyqQ7dyMEo24YqjBvQCrKvuzgTh03CAA1j9L0QseTdZM89yj9ahyTCUx5IOobEfn_avwbvuNNI_B9jdFmdbsrr3cicE5o2FXPbi2T17I",
            "_ga_WZH24HHFLE": "GS2.1.s1750857657$o12$g0$t1750857657$j60$l0$h0"
        }
        
        # Prepare files exactly as in your curl command
        files = {
            "Applicant Identity Documents": ("passport.pdf", open(id_document_path, "rb"), "application/pdf"),
            "Applicant Form": ("form.pdf", open(kyc_form_path, "rb"), "application/pdf")
        }
        
        print(f"🔍 Calling AI Planet API with proper authentication...")
        response = requests.post(url, headers=headers, cookies=cookies, files=files)
        
        # Close file handles
        for file_tuple in files.values():
            file_tuple[1].close()
        
        print(f"📡 API Response: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI Planet API Success!")
            print(f"Generation ID: {result.get('id')}")
            
            # Return success message since API is async
            return {
                "status": "success",
                "message": "KYC documents successfully submitted to AI Planet for processing",
                "generation_id": result.get('id'),
                "ai_planet_status": result.get('status'),
                "validation_report": "Documents processed via AI Planet GenAI Stack API"
            }
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            # Fallback to mock data for testing
            return get_mock_response("validation", None)
            
    except Exception as e:
        logging.error(f"Error calling AI Planet API: {str(e)}")
        print(f"❌ Using fallback mock data due to API error: {str(e)}")
        return get_mock_response("validation", None)

def call_aiplanet_api(prompt, image_path=None):
    """Call AI Planet GenAI Stack API for text generation (legacy method)"""
    try:
        url = f"{AIPLANET_BASE_URL}/{AIPLANET_APP_ID}/generations"
        
        headers = {
            "Authorization": AIPLANET_TOKEN,  # Direct token, not Bearer
            "Content-Type": "application/json"
        }
        
        payload = {
            "prompt": prompt,
            "max_tokens": 2000,
            "temperature": 0.1
        }
        
        # If image is provided, encode it as base64
        if image_path:
            with open(image_path, "rb") as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')
                payload["image"] = image_data
        
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            result = response.json()
            return result.get('text', result.get('generated_text', str(result)))
        else:
            print(f"API Error: {response.status_code} - {response.text}")
            # Fallback to mock data for testing
            return get_mock_response(prompt, image_path)
            
    except Exception as e:
        logging.error(f"Error calling AI Planet API: {str(e)}")
        print(f"Using fallback mock data due to API error: {str(e)}")
        return get_mock_response(prompt, image_path)

def get_mock_response(prompt, image_path=None):
    """Generate mock response for testing when API fails"""
    if "ID document" in prompt:
        return '''
        {
            "document_type": "Passport",
            "name": "VAZEEN BASHA",
            "date_of_birth": "25/12/1975",
            "address": "BAPUJINAGAR, KARNATAKA, BANGALORE",
            "document_number": "A1234567",
            "expiry_date": "25/12/2021",
            "issue_date": "25/12/2011",
            "other_details": "Indian Passport"
        }
        '''
    elif "KYC onboarding form" in prompt:
        return '''
        {
            "name": "TARUN R JAIN",
            "date_of_birth": "",
            "address": "323/A-1, 4th main road, Manjunthanagar, Bangalore, India - 560010",
            "contact_number": "+91-9876543210",
            "email": "<EMAIL>",
            "pan_number": "Required but not provided",
            "required_documents": "PAN card, Aadhaar card",
            "form_fields": "Incomplete date of birth field"
        }
        '''
    else:
        # Validation report
        return '''
Validation Report:
Overall Pass/Fail Status: Fail

List of Errors, Discrepancies, or Missing Information:

Name Discrepancy: The name on the passport is "VAZEEN BASHA," while the onboarding form lists the name as "TARUN R JAIN." This is a significant discrepancy that requires clarification.
Date of Birth Discrepancy: The date of birth on the passport is "25/12/1975," whereas the onboarding form does not provide a date of birth entry. This is a missing field that needs to be addressed.
Address Discrepancy: The address on the passport is "BAPUJINAGAR, KARNATAKA, BANGALORE," while the address on the onboarding form is "323/A-1, 4th main road, Manjunthanagar, Bangalore, India - 560010." These addresses do not match.
Document Expiration Date: The passport has an expiration date of "25/12/2021," which is already expired. Regulatory requirements state that identification documents must be valid.
Missing PAN Information: The onboarding form indicates that a PAN card is required, but it is not provided in the scanned documents.
Incomplete Fields: The onboarding form lacks a filled date of birth section, which is mandatory.

Confidence Score for Document Authenticity: 70% (The passport appears authentic, but discrepancies with the onboarding form raise concerns about the identity of the applicant.)

Compliance Status:
Confirmation of Regulatory Compliance: Non-compliant

List of Outstanding Compliance Issues:

Significant discrepancies in name and date of birth.
Expired identification document.
Missing PAN card and proof of identity documentation.
Incomplete onboarding form with missing mandatory fields.

Action Items:
Flag Applications Requiring Manual Review: Yes - This application should be flagged for manual review due to multiple discrepancies and missing information.

Generate Appropriate Customer Communication for Missing or Incorrect Information:

Subject: Action Required: Incomplete Onboarding Application
Message:
Dear Customer,

We are currently reviewing your KYC application for account opening. However, we have encountered some discrepancies and missing information that need your immediate attention:

1. The name provided in your application does not match your identification documents.
2. Your date of birth is missing from the application form.
3. The address provided in your application does not match the address on your identification document.
4. Your identification document has expired.
5. The PAN card is required but was not submitted.

Please provide the necessary corrections and documentation at your earliest convenience to proceed with your application.

Thank you for your cooperation.

Best regards,
KYC Team

Trigger Next Steps in the Onboarding Process for Successful Applications: No next steps can be triggered at this time due to the failure to meet compliance requirements. The application will remain on hold until the discrepancies are resolved.
        '''

def match_faces(id_photo_bytes, live_photo_bytes):
    """
    Match faces between ID photo and live capture using improved face recognition
    Returns only match status and confidence score
    """
    try:
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        def detect_face(image_bytes):
            nparr = np.frombuffer(image_bytes, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            faces = face_cascade.detectMultiScale(gray, 1.1, 5, minSize=(30, 30))
            
            if len(faces) == 0:
                return None, "No face detected"
            
            largest_face = max(faces, key=lambda rect: rect[2] * rect[3])
            x, y, w, h = largest_face
            
            padding = int(w * 0.2)  # 20% padding
            x_padded = max(0, x - padding)
            y_padded = max(0, y - padding)
            w_padded = min(img.shape[1] - x_padded, w + 2*padding)
            h_padded = min(img.shape[0] - y_padded, h + 2*padding)
            
            face_roi = img[y_padded:y_padded+h_padded, x_padded:x_padded+w_padded]            
            face_roi = cv2.resize(face_roi, (100, 100))
            face_roi_gray = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)
            face_roi_normalized = cv2.equalizeHist(face_roi_gray)
            
            return face_roi_normalized, None
        
        id_face, id_error = detect_face(id_photo_bytes)
        live_face, live_error = detect_face(live_photo_bytes)
        
        if id_error:
            return {"match": False, "confidence": 0}
        if live_error:
            return {"match": False, "confidence": 0}
        
        hist1 = cv2.calcHist([id_face], [0], None, [256], [0, 256])
        hist2 = cv2.calcHist([live_face], [0], None, [256], [0, 256])
        
        cv2.normalize(hist1, hist1, 0, 1, cv2.NORM_MINMAX)
        cv2.normalize(hist2, hist2, 0, 1, cv2.NORM_MINMAX)
        
        correl_score = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        template_score = cv2.matchTemplate(id_face, live_face, cv2.TM_CCORR_NORMED)[0][0]
        combined_score = 0.6 * max(0, correl_score) + 0.4 * template_score
        
        confidence = round(combined_score * 100, 2)
        match_threshold = 30
        
        return {
            "match": confidence > match_threshold,
            "confidence": confidence
        }
    
    except Exception as e:
        logging.error(f"Error in face matching: {str(e)}")
        return {
            "match": False,
            "confidence": 0
        }

def extract_document_data(document_path, document_type="ID"):
    """Extract data from uploaded document using AI Planet GenAI Stack"""
    try:
        if document_type == "ID":
            prompt = """
            Extract all information from this ID document (Passport, Aadhaar, PAN, etc.) and return in JSON format:
            {
                "document_type": "type of document",
                "name": "full name",
                "date_of_birth": "DOB in DD/MM/YYYY format",
                "address": "complete address",
                "document_number": "document number",
                "expiry_date": "expiry date if available",
                "issue_date": "issue date if available",
                "other_details": "any other relevant information"
            }
            """
        else:  # KYC Form
            prompt = """
            Extract all information from this KYC onboarding form and return in JSON format:
            {
                "name": "applicant name",
                "date_of_birth": "DOB in DD/MM/YYYY format",
                "address": "address provided",
                "contact_number": "phone number",
                "email": "email address",
                "pan_number": "PAN if mentioned",
                "required_documents": "list of required documents mentioned",
                "form_fields": "all other form fields and values"
            }
            """
        
        result = call_aiplanet_api(prompt, document_path)
        return result
        
    except Exception as e:
        logging.error(f"Error extracting document data: {str(e)}")
        return {"error": str(e)}

def validate_kyc_documents(id_data, form_data):
    """Compare ID document with KYC form and generate validation report"""
    
    validation_prompt = f"""
    You are a KYC compliance officer. Compare the ID document data with the KYC form data and generate a detailed validation report.

    ID Document Data:
    {id_data}

    KYC Form Data:
    {form_data}

    Generate a comprehensive validation report in this exact format:

    Validation Report:
    Overall Pass/Fail Status: [Pass/Fail]

    List of Errors, Discrepancies, or Missing Information:
    [List each discrepancy with detailed explanation]

    Confidence Score for Document Authenticity: [X]% ([Explanation])

    Compliance Status:
    Confirmation of Regulatory Compliance: [Compliant/Non-compliant]

    List of Outstanding Compliance Issues:
    [List all compliance issues]

    Action Items:
    Flag Applications Requiring Manual Review: [Yes/No with explanation]

    Generate Appropriate Customer Communication for Missing or Incorrect Information:
    Subject: [Subject line]
    Message: [Professional message to customer explaining issues and required actions]

    Trigger Next Steps in the Onboarding Process for Successful Applications: [Next steps or hold status]

    Focus on:
    1. Name matching
    2. Date of birth verification
    3. Address comparison
    4. Document validity/expiration
    5. Missing required documents
    6. Regulatory compliance
    """

    try:
        result = call_aiplanet_api(validation_prompt)
        return result
    except Exception as e:
        logging.error(f"Error in KYC validation: {str(e)}")
        return f"Error generating validation report: {str(e)}"

def get_mock_validation_data():
    """Generate mock validation data for testing"""
    return [
        {
            "application_id": "KYC001",
            "customer_name": "John Doe",
            "status": "Pending Review",
            "submission_date": "2024-01-15",
            "documents_uploaded": ["Passport", "KYC Form"],
            "validation_score": 85,
            "issues_found": 2
        },
        {
            "application_id": "KYC002", 
            "customer_name": "Jane Smith",
            "status": "Approved",
            "submission_date": "2024-01-14",
            "documents_uploaded": ["Aadhaar", "KYC Form"],
            "validation_score": 95,
            "issues_found": 0
        }
    ]

def get_mock_data():
    """Generate mock data for dashboard display"""
    regions = ["North", "South", "East", "West", "Central"]
    states = ["Delhi", "Maharashtra", "Tamil Nadu", "West Bengal", "Karnataka", "Uttar Pradesh"]
    verification_types = ["Digital KYC", "Physical KYC"]
    verification_statuses = ["Approved", "Rejected", "Pending"]
    
    def random_date():
        days = random.randint(1, 90)
        return (datetime.datetime.now() - datetime.timedelta(days=days)).strftime("%Y-%m-%d %H:%M:%S")
    
    mock_data = []
    for i in range(100):
        verification_type = random.choices(
            verification_types, 
            weights=[0.9, 0.1], 
            k=1
        )[0]
        
        if verification_type == "Digital KYC":
            status_weights = [0.95, 0.03, 0.02]  
        else:
            status_weights = [0.75, 0.15, 0.1]  
            
        status = random.choices(
            verification_statuses,
            weights=status_weights,
            k=1
        )[0]
        
        mock_entry = {
            "name": f"Customer {i+1}",
            "dob": f"{random.randint(1950, 2000)}-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}",
            "father_name": f"Father {i+1}",
            "verification_type": verification_type,
            "verification_status": status,
            "confidence_score": round(random.uniform(30, 95), 2) if status == "Approved" else round(random.uniform(0, 40), 2),
            "region": random.choice(regions),
            "state": random.choice(states),
            "csm": random.choice(["Tarun Jain", "Priya Sharma", "Rajesh Kumar", "Anita Singh"]),
            "asm": random.choice(["Gurram Prudhvi", "Vikram Reddy", "Sanjay Gupta", "Meera Patel"]),
            "submission_time": random_date(),
            "ovd_type": random.choice(["Aadhaar Card", "Voter ID", "PAN Card", "Driving License"]),
            "address": f"Address {i+1}, City, State, India",
            "contact": f"+91-{random.randint(**********, **********)}"
        }
        mock_data.append(mock_entry)
    
    return mock_data

def save_uploaded_file(uploaded_file):
    """
    Save uploaded file to local storage
    For Streamlit compatibility - not used in backend
    """
    upload_dir = Path("uploads")
    upload_dir.mkdir(exist_ok=True)
    
    filename = uploaded_file.name
    file_path = upload_dir / filename
    
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getvalue())
    
    return str(file_path) 