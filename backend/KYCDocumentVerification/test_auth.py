#!/usr/bin/env python3

import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_aiplanet_auth():
    """Test AI Planet API authentication"""
    
    # Get credentials from .env
    app_id = os.getenv('AIPLANET_APP_ID')
    token = os.getenv('AIPLANET_TOKEN')
    base_url = os.getenv('AIPLANET_BASE_URL', 'https://app.aiplanet.com/api/v1')
    
    print("🔍 Testing AI Planet API Authentication")
    print("=" * 50)
    print(f"App ID: {app_id}")
    print(f"Token: {token[:20]}..." if token else "Token: None")
    print(f"Base URL: {base_url}")
    print()
    
    if not app_id or not token:
        print("❌ Missing credentials in .env file")
        return False
    
    # Test 1: Simple GET request to check app
    print("📋 Test 1: GET /api/v1/apps (check app exists)")
    try:
        url = f"{base_url}/apps"
        headers = {
            "Authorization": token,
            "Accept": "application/json"
        }
        
        response = requests.get(url, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ App authentication successful")
        else:
            print(f"❌ App authentication failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "-" * 50)
    
    # Test 2: POST to generations endpoint with files
    print("📋 Test 2: POST /generations (with files)")
    try:
        url = f"{base_url}/{app_id}/generations"
        headers = {
            "Authorization": token,
            "Accept": "application/json"
        }
        
        # Test with your actual files
        files = {
            "Applicant Identity Documents": ("passport.pdf", open("../../passport.pdf", "rb"), "application/pdf"),
            "Applicant Form": ("form.pdf", open("../../form.pdf", "rb"), "application/pdf")
        }
        
        response = requests.post(url, headers=headers, files=files)
        
        # Close files
        for file_tuple in files.values():
            file_tuple[1].close()
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        
        if response.status_code == 200:
            print("✅ File upload authentication successful")
            return True
        else:
            print(f"❌ File upload authentication failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return False

if __name__ == "__main__":
    success = test_aiplanet_auth()
    if success:
        print("\n🎉 Authentication working!")
    else:
        print("\n❌ Authentication needs fixing") 