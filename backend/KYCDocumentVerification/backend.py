from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import json
import os
import datetime
import uuid
import base64
import asyncio
from pathlib import Path
import shutil
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import utilities
from utils_backend import (
    extract_document_data,
    validate_kyc_documents,
    get_mock_validation_data
)
# Removed annotation functionality for simplicity
from PIL import Image
import io

app = FastAPI(
    title="KYC Verification API",
    description="Backend API for KYC verification system with face matching, document OCR, and verification logic",
    version="1.0.0"
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Data models
class UserData(BaseModel):
    name: str
    dob: str  # Format: MM.DD.YYYY
    father_name: str
    ovd_type: str
    address: str
    contact: str
    region: str
    state: str
    csm: str
    asm: str

class VerificationResult(BaseModel):
    status: str
    reasoning: str
    face_match_score: float
    timestamp: str
    discrepancies: List[str] = []

class KYCSession(BaseModel):
    session_id: str
    user_data: Optional[UserData] = None
    document_data: Optional[dict] = None
    verification_result: Optional[VerificationResult] = None
    created_at: str
    updated_at: str

# In-memory storage for sessions (use database in production)
sessions = {}
verification_data = get_mock_validation_data()

# Store validation sessions
validation_sessions = {}

# Utility functions
def generate_session_id():
    return str(uuid.uuid4())

def get_session(session_id: str):
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    return sessions[session_id]

def save_uploaded_file_backend(file: UploadFile, session_id: str):
    """Save uploaded file with session-specific naming"""
    upload_dir = Path("uploads")
    upload_dir.mkdir(exist_ok=True)
    
    file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'jpg'
    filename = f"{session_id}_{file.filename}"
    file_path = upload_dir / filename
    
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    return str(file_path)

# API Endpoints

@app.get("/")
async def root():
    return {"message": "KYC Verification API", "version": "1.0.0"}

@app.post("/sessions", response_model=dict)
async def create_session():
    """Create a new KYC verification session"""
    session_id = generate_session_id()
    session = KYCSession(
        session_id=session_id,
        created_at=datetime.datetime.now().isoformat(),
        updated_at=datetime.datetime.now().isoformat()
    )
    sessions[session_id] = session
    return {"session_id": session_id, "status": "created"}

@app.get("/sessions/{session_id}")
async def get_session_data(session_id: str):
    """Get session data"""
    session = get_session(session_id)
    return session.dict()

@app.post("/sessions/{session_id}/user-data")
async def submit_user_data(session_id: str, user_data: UserData):
    """Submit user form data"""
    session = get_session(session_id)
    session.user_data = user_data
    session.updated_at = datetime.datetime.now().isoformat()
    return {"status": "success", "message": "User data saved"}

@app.post("/sessions/{session_id}/document")
async def upload_document(
    session_id: str,
    document: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Upload and process identity document"""
    session = get_session(session_id)
    
    # Save uploaded file
    try:
        file_path = save_uploaded_file_backend(document, session_id)
        
        # Background task to extract document data
        background_tasks.add_task(extract_document_data_async, session_id, file_path)
        
        return {
            "status": "success", 
            "message": "Document uploaded and processing started",
            "file_path": file_path
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading document: {str(e)}")

async def extract_document_data_async(session_id: str, file_path: str):
    """Background task to extract data from document"""
    try:
        from utils_backend import extract_document_data
        session = sessions[session_id]
        extracted_data = extract_document_data(file_path, "ID")
        session.document_data = extracted_data
        session.updated_at = datetime.datetime.now().isoformat()
    except Exception as e:
        print(f"Error extracting document data: {e}")

@app.get("/sessions/{session_id}/document-data")
async def get_document_data(session_id: str):
    """Get extracted document data"""
    session = get_session(session_id)
    if not session.document_data:
        return {"status": "processing", "message": "Document data extraction in progress"}
    return {"status": "ready", "data": session.document_data}

# Annotation endpoint removed for simplicity

@app.post("/sessions/{session_id}/face-match")
async def verify_face_match(
    session_id: str,
    id_photo: UploadFile = File(...),
    live_photo: UploadFile = File(...)
):
    """Perform face matching between ID photo and live photo"""
    try:
        id_photo_bytes = await id_photo.read()
        live_photo_bytes = await live_photo.read()
        
        face_match_result = match_faces(id_photo_bytes, live_photo_bytes)
        
        return {
            "status": "success",
            "face_match_result": face_match_result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in face matching: {str(e)}")

@app.post("/sessions/{session_id}/verify")
async def complete_verification(
    session_id: str,
    id_photo: UploadFile = File(...),
    live_photo: UploadFile = File(...)
):
    """Complete KYC verification process"""
    session = get_session(session_id)
    
    if not session.user_data:
        raise HTTPException(status_code=400, detail="User data not found")
    
    if not session.document_data:
        raise HTTPException(status_code=400, detail="Document data not found")
    
    try:
        # Perform face matching
        id_photo_bytes = await id_photo.read()
        live_photo_bytes = await live_photo.read()
        face_match_result = match_faces(id_photo_bytes, live_photo_bytes)
        
        # Perform KYC verification
        verification_result = verify_kyc(
            session.user_data.dict(),
            session.document_data,
            face_match_result
        )
        
        # Save verification result
        session.verification_result = VerificationResult(**verification_result)
        session.updated_at = datetime.datetime.now().isoformat()
        
        # Add to verification data
        new_entry = {
            **session.user_data.dict(),
            "verification_type": "Physical KYC",
            "verification_status": verification_result["status"],
            "confidence_score": verification_result["face_match_score"],
            "submission_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        verification_data.insert(0, new_entry)
        
        return {
            "status": "success",
            "verification_result": verification_result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in verification: {str(e)}")

@app.get("/sessions/{session_id}/verification-result")
async def get_verification_result(session_id: str):
    """Get verification result"""
    session = get_session(session_id)
    if not session.verification_result:
        raise HTTPException(status_code=404, detail="Verification result not found")
    return session.verification_result.dict()

@app.get("/analytics")
async def get_analytics(
    region: Optional[str] = None,
    state: Optional[str] = None,
    csm: Optional[str] = None,
    asm: Optional[str] = None
):
    """Get KYC verification analytics"""
    filtered_data = verification_data
    
    # Apply filters
    if region and region != "All":
        filtered_data = [d for d in filtered_data if d.get("region") == region]
    if state and state != "All":
        filtered_data = [d for d in filtered_data if d.get("state") == state]
    if csm and csm != "All":
        filtered_data = [d for d in filtered_data if d.get("csm") == csm]
    if asm and asm != "All":
        filtered_data = [d for d in filtered_data if d.get("asm") == asm]
    
    # Calculate metrics
    digital_kyc = len([d for d in filtered_data if d.get("verification_type") == "Digital KYC"])
    physical_kyc = len([d for d in filtered_data if d.get("verification_type") == "Physical KYC"])
    total_kyc = digital_kyc + physical_kyc
    approved = len([d for d in filtered_data if d.get("verification_status") == "Approved"])
    rejected = len([d for d in filtered_data if d.get("verification_status") == "Rejected"])
    pending = len([d for d in filtered_data if d.get("verification_status") == "Pending"])
    
    return {
        "metrics": {
            "total_kyc": total_kyc,
            "digital_kyc": digital_kyc,
            "physical_kyc": physical_kyc,
            "approved": approved,
            "rejected": rejected,
            "pending": pending,
            "approval_rate": (approved / total_kyc * 100) if total_kyc > 0 else 0
        },
        "data": filtered_data[:50],  # Return top 50 records
        "filters": {
            "regions": list(set([d.get("region", "Unknown") for d in verification_data])),
            "states": list(set([d.get("state", "Unknown") for d in verification_data])),
            "csms": list(set([d.get("csm", "Unknown") for d in verification_data])),
            "asms": list(set([d.get("asm", "Unknown") for d in verification_data]))
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.datetime.now().isoformat()}

# Cleanup endpoint (for development)
@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete session and associated files"""
    if session_id in sessions:
        # Delete uploaded files
        upload_dir = Path("uploads")
        for file in upload_dir.glob(f"{session_id}_*"):
            file.unlink()
        
        # Delete session
        del sessions[session_id]
        return {"status": "deleted"}
    else:
        raise HTTPException(status_code=404, detail="Session not found")

@app.post("/validate-kyc")
async def validate_kyc_documents_endpoint(
    id_document: UploadFile = File(...),
    kyc_form: UploadFile = File(...)
):
    """Upload ID document and KYC form for validation"""
    try:
        # Generate session ID
        session_id = str(uuid.uuid4())
        
        # Create upload directory
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)
        
        # Save uploaded files
        id_doc_path = upload_dir / f"{session_id}_id_document.{id_document.filename.split('.')[-1]}"
        kyc_form_path = upload_dir / f"{session_id}_kyc_form.{kyc_form.filename.split('.')[-1]}"
        
        with open(id_doc_path, "wb") as f:
            f.write(await id_document.read())
        
        with open(kyc_form_path, "wb") as f:
            f.write(await kyc_form.read())
        
        # Call AI Planet API directly with files (using working authentication)
        print(f"🔍 Processing files for session {session_id}")
        from utils_backend import call_aiplanet_api_with_files
        
        # Try the real AI Planet API first
        api_result = call_aiplanet_api_with_files(str(id_doc_path), str(kyc_form_path))
        
        if isinstance(api_result, dict) and api_result.get('status') == 'success':
            # AI Planet API worked!
            validation_report = api_result.get('validation_report', 'Processed via AI Planet')
            id_data = f"AI Planet Generation ID: {api_result.get('generation_id')}"
            form_data = f"Status: {api_result.get('ai_planet_status')}"
        else:
            # Fallback to mock processing
            print("Using fallback mock data")
            from utils_backend import extract_document_data as extract_doc_data, validate_kyc_documents
            id_data = extract_doc_data(str(id_doc_path), "ID")
            form_data = extract_doc_data(str(kyc_form_path), "FORM")
            validation_report = validate_kyc_documents(id_data, form_data)
        
        # Store session data
        validation_sessions[session_id] = {
            "id_document": id_document.filename,
            "kyc_form": kyc_form.filename,
            "id_data": id_data,
            "form_data": form_data,
            "validation_report": validation_report,
            "timestamp": str(Path().cwd())
        }
        
        return {
            "session_id": session_id,
            "status": "success",
            "validation_report": validation_report,
            "extracted_data": {
                "id_document": id_data,
                "kyc_form": form_data
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")

@app.get("/validation/{session_id}")
async def get_validation_result(session_id: str):
    """Get validation result by session ID"""
    if session_id not in validation_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return validation_sessions[session_id]

@app.get("/validations")
async def get_all_validations():
    """Get all validation sessions"""
    return {
        "total": len(validation_sessions),
        "sessions": validation_sessions,
        "mock_data": get_mock_validation_data()
    }

@app.delete("/validation/{session_id}")
async def delete_validation_session(session_id: str):
    """Delete validation session and files"""
    if session_id in validation_sessions:
        # Delete files
        upload_dir = Path("uploads")
        for file_pattern in [f"{session_id}_id_document.*", f"{session_id}_kyc_form.*"]:
            for file_path in upload_dir.glob(file_pattern):
                file_path.unlink()
        
        # Delete session
        del validation_sessions[session_id]
        return {"status": "deleted"}
    else:
        raise HTTPException(status_code=404, detail="Session not found")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting KYC Document Validation API...")
    print("�� Upload ID document + KYC form → Get validation report")
    uvicorn.run(app, host="0.0.0.0", port=8002) 