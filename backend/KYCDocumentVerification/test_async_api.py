#!/usr/bin/env python3

import requests
import os
import time
import json
from dotenv import load_dotenv

load_dotenv()

def test_async_aiplanet():
    """Test AI Planet API with async polling"""
    
    app_id = os.getenv('AIPLANET_APP_ID')
    token = os.getenv('AIPLANET_TOKEN')
    base_url = os.getenv('AIPLANET_BASE_URL', 'https://app.aiplanet.com/api/v1')
    
    print("🔍 Testing AI Planet Async API")
    print("=" * 50)
    
    # Step 1: Submit generation request
    url = f"{base_url}/{app_id}/generations"
    headers = {
        "Authorization": token,
        "Accept": "application/json"
    }
    
    files = {
        "Applicant Identity Documents": ("passport.pdf", open("../../passport.pdf", "rb"), "application/pdf"),
        "Applicant Form": ("form.pdf", open("../../form.pdf", "rb"), "application/pdf")
    }
    
    print("📤 Submitting generation request...")
    response = requests.post(url, headers=headers, files=files)
    
    # Close files
    for file_tuple in files.values():
        file_tuple[1].close()
    
    if response.status_code != 200:
        print(f"❌ Failed to submit: {response.status_code} - {response.text}")
        return
    
    result = response.json()
    generation_id = result.get('id')
    print(f"✅ Generation submitted: {generation_id}")
    print(f"Status: {result.get('status')}")
    
    # Step 2: Poll for results
    print("\n⏳ Polling for results...")
    max_attempts = 30  # 30 attempts = 5 minutes max
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"Attempt {attempt}: Checking status...")
        
        # Check via the generations list endpoint
        list_url = f"{base_url}/generations"
        list_response = requests.get(list_url, headers=headers)
        
        if list_response.status_code == 200:
            generations = list_response.json()
            
            # Find our generation
            our_generation = None
            for gen in generations:
                if gen.get('id') == generation_id:
                    our_generation = gen
                    break
            
            if our_generation:
                status = our_generation.get('status')
                print(f"Status: {status}")
                
                if status == 'completed':
                    print("✅ Generation completed!")
                    print("\n📋 Results:")
                    print(f"Outputs: {json.dumps(our_generation.get('outputs', {}), indent=2)}")
                    print(f"Logs: {our_generation.get('logs', 'No logs')}")
                    return our_generation
                elif status == 'failed':
                    print("❌ Generation failed!")
                    print(f"Logs: {our_generation.get('logs', 'No logs')}")
                    return None
                else:
                    print(f"Still processing... (status: {status})")
            else:
                print("❌ Generation not found in list")
        else:
            print(f"❌ Failed to get generations list: {list_response.status_code}")
        
        if attempt < max_attempts:
            time.sleep(10)  # Wait 10 seconds before next check
    
    print("⏰ Timeout waiting for results")
    return None

if __name__ == "__main__":
    result = test_async_aiplanet()
    if result:
        print("\n🎉 Success! Got results from AI Planet API")
    else:
        print("\n❌ Failed to get results") 