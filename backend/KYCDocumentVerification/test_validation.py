#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from utils_backend import extract_document_data, validate_kyc_documents

def test_validation():
    """Test KYC validation with your actual files"""
    
    # File paths
    passport_path = "../../passport.pdf"
    form_path = "../../form.pdf"
    
    print("🔍 Testing KYC Document Validation")
    print("=" * 50)
    
    try:
        # Test if files exist
        if not os.path.exists(passport_path):
            print(f"❌ Passport file not found: {passport_path}")
            return
        if not os.path.exists(form_path):
            print(f"❌ Form file not found: {form_path}")
            return
            
        print("✅ Files found")
        
        # Extract data from passport
        print("\n🔍 Extracting passport data...")
        id_data = extract_document_data(passport_path, "ID")
        print("✅ Passport data extracted:")
        print(f"   {id_data[:200]}..." if len(str(id_data)) > 200 else f"   {id_data}")
        
        # Extract data from form
        print("\n🔍 Extracting form data...")
        form_data = extract_document_data(form_path, "FORM")
        print("✅ Form data extracted:")
        print(f"   {form_data[:200]}..." if len(str(form_data)) > 200 else f"   {form_data}")
        
        # Generate validation report
        print("\n📊 Generating validation report...")
        validation_report = validate_kyc_documents(id_data, form_data)
        
        print("\n" + "=" * 50)
        print("📋 VALIDATION REPORT")
        print("=" * 50)
        print(validation_report)
        
        return {
            "status": "success",
            "id_data": id_data,
            "form_data": form_data,
            "validation_report": validation_report
        }
        
    except Exception as e:
        print(f"❌ Error during validation: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}

if __name__ == "__main__":
    result = test_validation() 