#!/usr/bin/env python3

from fastapi import Fast<PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path
import uuid
import json
from utils_backend import extract_document_data, validate_kyc_documents

app = FastAPI(title="Simple KYC Validation API")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Simple KYC Validation API", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.post("/validate-kyc")
async def validate_kyc(
    id_document: UploadFile = File(...),
    kyc_form: UploadFile = File(...)
):
    """Simple KYC validation endpoint"""
    try:
        # Generate session ID
        session_id = str(uuid.uuid4())
        
        # Create upload directory
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)
        
        # Save files
        id_doc_path = upload_dir / f"{session_id}_id_document.pdf"
        kyc_form_path = upload_dir / f"{session_id}_kyc_form.pdf"
        
        with open(id_doc_path, "wb") as f:
            f.write(await id_document.read())
        
        with open(kyc_form_path, "wb") as f:
            f.write(await kyc_form.read())
        
        # Call AI Planet API directly with files
        print(f"🔍 Processing files for session {session_id}")
        from utils_backend import call_aiplanet_api_with_files
        
        # Try the real AI Planet API first
        api_result = call_aiplanet_api_with_files(str(id_doc_path), str(kyc_form_path))
        
        if isinstance(api_result, dict) and api_result.get('status') == 'success':
            # AI Planet API worked!
            validation_report = api_result.get('validation_report', 'Processed via AI Planet')
            id_data = f"AI Planet Generation ID: {api_result.get('generation_id')}"
            form_data = f"Status: {api_result.get('ai_planet_status')}"
        else:
            # Fallback to mock processing
            print("Using fallback mock data")
            from utils_backend import extract_document_data, validate_kyc_documents
            id_data = extract_document_data(str(id_doc_path), "ID")
            form_data = extract_document_data(str(kyc_form_path), "FORM")
            validation_report = validate_kyc_documents(id_data, form_data)
        
        # Return simple response
        return {
            "session_id": session_id,
            "status": "success",
            "message": "KYC validation completed",
            "validation_report": str(validation_report),
            "extracted_data": {
                "id_document": str(id_data),
                "kyc_form": str(form_data)
            }
        }
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Simple KYC Validation API on port 8003...")
    uvicorn.run(app, host="0.0.0.0", port=8003) 