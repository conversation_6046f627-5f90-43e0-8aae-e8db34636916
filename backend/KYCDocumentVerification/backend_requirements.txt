# FastAPI and server dependencies
fastapi==0.109.1
uvicorn[standard]==0.27.0
python-multipart==0.0.7
python-dotenv==1.0.0

# PDF processing
PyMuPDF==1.23.5
pdf2image==1.17.0

# Existing dependencies from original requirements.txt
aioice==0.9.0
aiortc==1.10.1
altair==5.5.0
annotated-types==0.7.0
anyio==4.8.0
attrs==25.2.0
av==13.1.0
blinker==1.9.0
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cryptography==44.0.2
dnspython==2.7.0
exceptiongroup==1.2.2
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.164.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-crc32c==1.6.0
google-generativeai==0.8.4
googleapis-common-protos==1.69.1
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
idna==3.10
ifaddr==0.2.0
Jinja2==3.1.6
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
MarkupSafe==3.0.2
narwhals==1.30.0
numpy==2.2.3
opencv-python==*********
packaging==24.2
pandas==2.2.3
pillow==11.1.0
plotly==6.0.0
proto-plus==1.26.1
protobuf==5.29.3
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pydeck==0.9.1
pyee==12.1.1
pylibsrtp==0.11.0
pyOpenSSL==25.0.0
pyparsing==3.2.1
python-dateutil==2.9.0.post0
pytz==2025.1
referencing==0.36.2
requests==2.32.3
rpds-py==0.23.1
rsa==4.9
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
tenacity==9.0.0
toml==0.10.2
tornado==6.4.2
tqdm==4.67.1
typing_extensions==4.12.2
tzdata==2025.1
uritemplate==4.1.1
urllib3==2.3.0
websockets==14.2 