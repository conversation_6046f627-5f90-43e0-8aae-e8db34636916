import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from PIL import Image
import io
import datetime
from utils import (
    capture_photo,
    match_faces,
    extract_details_from_document,
    verify_kyc,
    get_mock_data,
    save_uploaded_file,
    load_css
)
from annotate import get_bounding_boxes, draw_bounding_boxes

st.set_page_config(
    page_title="KYC Verification",
    page_icon="🔐",
    layout="wide",
    initial_sidebar_state="expanded"
)

load_css()

if 'page' not in st.session_state:
    st.session_state.page = 'form'
if 'user_data' not in st.session_state:
    st.session_state.user_data = {}
if 'uploaded_photo' not in st.session_state:
    st.session_state.uploaded_photo = None
if 'live_photo' not in st.session_state:
    st.session_state.live_photo = None
if 'document_data' not in st.session_state:
    st.session_state.document_data = {}
if 'verification_result' not in st.session_state:
    st.session_state.verification_result = {}
if 'mock_data' not in st.session_state:
    st.session_state.mock_data = get_mock_data()
if 'processing' not in st.session_state:
    st.session_state.processing = False
if 'document_path' not in st.session_state:
    st.session_state.document_path = None  

form_fields = ['name', 'dob', 'father_name', 'ovd_type', 'address', 'contact', 'region', 'state', 'csm', 'asm']
for field in form_fields:
    if field not in st.session_state:
        if field == 'ovd_type':
            st.session_state[field] = "Aadhaar Card"
        elif field == 'region':
            st.session_state[field] = "North"
        elif field == 'state':
            st.session_state[field] = "Delhi"
        elif field == 'csm':
            st.session_state[field] = "Tarun Jain"
        elif field == 'asm':
            st.session_state[field] = "Gurram Prudhvi"
        else:
            st.session_state[field] = ""

def go_to_form():
    st.session_state.page = 'form'
    st.session_state.processing = False

def go_to_photo_capture():
    st.session_state.page = 'photo_capture'
    st.session_state.processing = False

def go_to_dashboard():
    st.session_state.page = 'dashboard'
    st.session_state.processing = True

def start_new_verification():
    for field in form_fields:
        if field == 'ovd_type':
            st.session_state[field] = "Aadhaar Card"
        elif field == 'region':
            st.session_state[field] = "North"
        elif field == 'state':
            st.session_state[field] = "Delhi"
        elif field == 'csm':
            st.session_state[field] = "Tarun Jain"
        elif field == 'asm':
            st.session_state[field] = "Gurram Prudhvi"
        else:
            st.session_state[field] = ""
    st.session_state.uploaded_photo = None
    st.session_state.live_photo = None
    st.session_state.document_file = None
    st.session_state.id_photo_file = None
    st.session_state.document_data = {}
    st.session_state.verification_result = {}
    st.session_state.document_path = None  # Reset document path
    go_to_form()

def process_form_submission():
    st.session_state.user_data = {
        'name': st.session_state.name,
        'dob': st.session_state.dob,
        'father_name': st.session_state.father_name,
        'ovd_type': st.session_state.ovd_type,
        'address': st.session_state.address,
        'contact': st.session_state.contact,
        'region': st.session_state.region,
        'state': st.session_state.state,
        'csm': st.session_state.csm,
        'asm': st.session_state.asm,
        'submission_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    if st.session_state.document_file is not None:
        document_path = save_uploaded_file(st.session_state.document_file)
        st.session_state.document_path = document_path  # Store the path
        extracted_data = extract_details_from_document(document_path)
        st.session_state.document_data = extracted_data

    go_to_photo_capture()

def process_verification():
    face_match_result = match_faces(
        st.session_state.uploaded_photo, 
        st.session_state.live_photo
    )
    verification_result = verify_kyc(
        st.session_state.user_data,
        st.session_state.document_data,
        face_match_result
    )
    st.session_state.verification_result = verification_result
    new_entry = {
        **st.session_state.user_data,
        "verification_type": "Physical KYC",
        "verification_status": verification_result["status"],
        "confidence_score": verification_result["face_match_score"]
    }
    st.session_state.mock_data = [new_entry] + st.session_state.mock_data
    st.session_state.processing = False

if st.session_state.page == 'form':
    st.title("KYC Verification Form")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Personal Information")
        st.text_input("Full Name", key="name")
        
        today = datetime.datetime.now()
        min_date = datetime.date(1950, 1, 1)
        max_date = today.date()
        default_value = datetime.date(1990, 1, 1)

        st.date_input(
            "Select your date of birth",
            value=default_value,
            min_value=min_date,
            max_value=max_date,
            format="MM.DD.YYYY",
            key="dob"
        )
        st.text_input("Father's Name", key="father_name")
        st.selectbox("Type of OVD", ["Aadhaar Card", "Voter ID", "PAN Card", "Driving License", "Passport"], key="ovd_type")
        st.text_area("Permanent Address", key="address")
        st.text_input("Contact Number", key="contact")
    with col2:
        st.subheader("Document Upload")
        document_file = st.file_uploader("Upload ID Proof", type=["jpg", "jpeg", "png"], key="document_file")
        id_photo_file = st.file_uploader("Upload Photo from ID", type=["jpg", "jpeg", "png"], key="id_photo_file")
        
        if id_photo_file is not None:
            st.image(id_photo_file, width=200, caption="ID Photo")
            st.session_state.uploaded_photo = id_photo_file.getvalue()
        elif st.session_state.uploaded_photo is not None:
            doc_image = Image.open(io.BytesIO(st.session_state.uploaded_photo))
            st.image(doc_image, width=200, caption="ID Photo (Previously Uploaded)")
        
        st.markdown("---")
        st.subheader("Administrative Information")
        st.selectbox("Region", ["North", "South", "East", "West", "Central"])
        st.selectbox("State", ["Delhi", "Maharashtra", "Tamil Nadu", "West Bengal", "Karnataka", "Uttar Pradesh", "Other"])
        st.text_input("CSM Name", key="csm")
        st.text_input("ASM Name", key="asm")
    
    st.markdown("---")
    _, col_submit, _ = st.columns([1, 2, 1])
    with col_submit:
        if st.button("Continue to Photo Capture", use_container_width=True, key='submit_form_btn'):
            if not st.session_state.name:
                st.error("Please enter your full name before continuing.")
            elif st.session_state.uploaded_photo is None:
                st.error("Please upload an ID photo before continuing.")
            elif document_file is None and 'document_file' not in st.session_state:
                st.error("Please upload an ID proof document before continuing.")
            else:
                with st.spinner("Processing form data..."):
                    process_form_submission()
                    st.rerun()

elif st.session_state.page == 'photo_capture':
    st.title("Live Photo Capture")
    col1, col2 = st.columns(2)
    with col1:
        st.subheader("Document Information")
        if st.session_state.document_data:
            st.markdown(st.session_state.document_data)
        else:
            st.info("No document data extracted. Please return to the form and upload a valid document.")

        if st.button("Annotate Document (beta)"):
            if 'document_path' in st.session_state and st.session_state.document_path:
                with st.spinner("Annotating document..."):
                    try:
                        image = Image.open(st.session_state.document_path)
                        bounding_boxes = get_bounding_boxes(image)
                        annotated_image = draw_bounding_boxes(image, bounding_boxes)
                        st.image(annotated_image, caption="Annotated Document", use_container_width=True)
                    except Exception as e:
                        st.error(f"Error annotating document: {e}")
            else:
                st.error("No document uploaded. Please return to the form and upload a document.")


    with col2:
        st.subheader("Capture Live Photo")
        st.write("Please ensure your face is clearly visible.")        
        live_photo = capture_photo()
        if live_photo is not None:
            st.session_state.live_photo = live_photo
            st.image(live_photo, width=300, caption="Captured Photo")
    
    st.markdown("---")
    col_back, col_empty, col_next = st.columns([1, 1, 1])
    
    with col_back:
        if st.button("⬅️ Back to Form", use_container_width=True):
            go_to_form()
            st.rerun()
    with col_next:
        if st.button("Complete Verification ➡️", use_container_width=True, key='complete_verification_btn'):
            if st.session_state.uploaded_photo and st.session_state.live_photo:
                go_to_dashboard()
                st.rerun()
            else:
                st.error("Both document and live photo are required to complete verification.")

elif st.session_state.page == 'dashboard':
    if st.session_state.processing:
        with st.spinner("Verifying your identity..."):
            process_verification()
            st.rerun()

    st.title("KYC Verification Dashboard")

    col1, col2 = st.columns([2, 1])
    with col1:
        st.subheader("Verification Result")
        status = st.session_state.verification_result.get("status", "Unknown")
        status_color = "green" if status == "Approved" else "red" if status == "Rejected" else "orange"
        st.markdown(f"""
        <div style="padding: 20px; border-radius: 10px; background-color: {status_color}; color: white;">
            <h2 style="margin: 0; color: white;">Status: {status}</h2>
            <p style="margin: 5px 0 0 0;">Face Match Confidence Score : {st.session_state.verification_result.get('face_match_score', 'N/A'):.2f}%</p>
        </div>
        """, unsafe_allow_html=True)
        st.subheader("Rationale")
        st.write(st.session_state.verification_result.get("reasoning", "No reasoning provided."))

    with col2:
        st.subheader("Face Match")
        if st.session_state.uploaded_photo and st.session_state.live_photo:
            doc_image = Image.open(io.BytesIO(st.session_state.uploaded_photo))
            st.image(doc_image, width=150, caption="ID Photo")
            st.image(st.session_state.live_photo, width=150, caption="Live Photo")
            
            fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=st.session_state.verification_result.get("face_match_score", 0),
            title={"text": "Face Match Confidence"},
            gauge={"axis": {"range": [0, 100]},
                "bar": {"color": "darkblue"},
                "steps": [
                    {"range": [0, 30], "color": "red"},       
                    {"range": [30, 60], "color": "orange"},  
                    {"range": [60, 100], "color": "green"}   
                ]}
            ))

            fig.update_layout(height=300, margin=dict(l=10, r=10, t=50, b=10))
            st.plotly_chart(fig, use_container_width=True)

    st.markdown("---")
    
    st.subheader("KYC Verification Analytics")

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        region_filter = st.selectbox("Filter by Region", ["All"] + list(set([d.get("region", "Unknown") for d in st.session_state.mock_data])))
    with col2:
        state_filter = st.selectbox("Filter by State", ["All"] + list(set([d.get("state", "Unknown") for d in st.session_state.mock_data])))
    with col3:
        csm_filter = st.selectbox("Filter by CSM", ["All"] + list(set([d.get("csm", "Unknown") for d in st.session_state.mock_data])))
    with col4:
        asm_filter = st.selectbox("Filter by ASM", ["All"] + list(set([d.get("asm", "Unknown") for d in st.session_state.mock_data])))
    filtered_data = st.session_state.mock_data
    if region_filter != "All":
        filtered_data = [d for d in filtered_data if d.get("region") == region_filter]
    if state_filter != "All":
        filtered_data = [d for d in filtered_data if d.get("state") == state_filter]
    if csm_filter != "All":
        filtered_data = [d for d in filtered_data if d.get("csm") == csm_filter]
    if asm_filter != "All":
        filtered_data = [d for d in filtered_data if d.get("asm") == asm_filter]

    col1, col2, col3, col4 = st.columns(4)

    digital_kyc = len([d for d in filtered_data if d.get("verification_type") == "Digital KYC"])
    physical_kyc = len([d for d in filtered_data if d.get("verification_type") == "Physical KYC"])
    total_kyc = digital_kyc + physical_kyc
    approved = len([d for d in filtered_data if d.get("verification_status") == "Approved"])
    rejected = len([d for d in filtered_data if d.get("verification_status") == "Rejected"])
    pending = len([d for d in filtered_data if d.get("verification_status") == "Pending"])

    with col1:
        st.metric("Total KYC Verifications", total_kyc)
    with col2:
        digital_percent = f"{(digital_kyc / total_kyc * 100):.1f}%" if total_kyc > 0 else "0%"
        st.metric("Digital KYC", digital_kyc, digital_percent)
    with col3:
        physical_percent = f"{(physical_kyc / total_kyc * 100):.1f}%" if total_kyc > 0 else "0%"
        st.metric("Physical KYC", physical_kyc, physical_percent)
    with col4:
        approval_rate = f"{(approved / total_kyc * 100):.1f}%" if total_kyc > 0 else "0%"
        st.metric("Approval Rate", approval_rate)
    
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("KYC Type Distribution")
        fig = go.Figure(data=[
            go.Pie(labels=["Digital KYC", "Physical KYC"],
                   values=[digital_kyc, physical_kyc],
                   marker_colors=["#3498db", "#2ecc71"])
        ])
        fig.update_layout(height=400, margin=dict(l=10, r=10, t=30, b=10))
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        st.subheader("Verification Status")
        fig = go.Figure(data=[
            go.Bar(x=["Approved", "Rejected", "Pending"],
                   y=[approved, rejected, pending],
                   marker_color=["green", "red", "orange"])
        ])
        fig.update_layout(height=400, margin=dict(l=10, r=10, t=30, b=10), yaxis_title="Number of Applications")
        st.plotly_chart(fig, use_container_width=True)

    st.subheader("Recent Verifications")
    df = pd.DataFrame(filtered_data[:10])
    if not df.empty:
        columns_to_display = [
            "name", "verification_type", "verification_status", "confidence_score", 
            "region", "state", "csm", "asm", "submission_time"
        ]
        display_columns = [col for col in columns_to_display if col in df.columns]
        st.dataframe(df[display_columns], use_container_width=True)
    else:
        st.info("No verification data available.")

    st.subheader("3-Month Verification Trends")

    months = ["Jan", "Feb", "Mar"]
    digital_trend = [80, 85, 90]
    physical_trend = [20, 15, 10]

    fig = go.Figure()
    fig.add_trace(go.Scatter(x=months, y=digital_trend, mode='lines+markers', name='Digital KYC', line=dict(width=2)))
    fig.add_trace(go.Scatter(x=months, y=physical_trend, mode='lines+markers', name='Physical KYC', line=dict(width=2)))
    fig.update_layout(
        title="KYC Verification Trends (Last 3 Months)",
        xaxis_title="Month",
        yaxis_title="Number of Verifications",
        legend=dict(y=0.99, x=0.01),
        height=400,
        margin=dict(l=20, r=20, t=40, b=20)
    )
    st.plotly_chart(fig, use_container_width=True)

    st.markdown("---")

    if st.button("⬅️ Back to Form", use_container_width=True):
        go_to_form()
        st.rerun()
