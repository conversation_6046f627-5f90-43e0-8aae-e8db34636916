import os
from PIL import Image, ImageDraw
from pydantic import BaseModel
from google import genai

class BoundingBox(BaseModel):
    """
    Represents a bounding box with its 2D coordinates and associated label.

    Attributes:
        box_2d (list[int]): Coordinates in [y1, x1, y2, x2] format, normalized to [0, 1000].
        label (str): Label of the detected element (e.g., "name", "DOB", "address").
    """
    box_2d: list[int]
    label: str

def get_bounding_boxes(image: Image.Image) -> list[BoundingBox]:
    client = genai.Client()
    response = client.models.generate_content(
        model="gemini-2.0-flash",  
        contents=[
            "Detect the name, date of birth (DOB), and address in this Aadhaar card image. Return precise bounding box coordinates",
            image
        ],
        config={
            "temperature": 0.1,
            "response_mime_type": "application/json",
            "response_schema": list[BoundingBox],
        },
    )
    return response.parsed

def draw_bounding_boxes(image: Image.Image, bounding_boxes: list[BoundingBox]) -> Image.Image:
    draw = ImageDraw.Draw(image)
    width, height = image.size
    line_color = "red"
    line_width = 3

    for bbox in bounding_boxes:
        y1, x1, y2, x2 = bbox.box_2d
        abs_y1 = int(y1 / 1000 * height)
        abs_x1 = int(x1 / 1000 * width)
        abs_y2 = int(y2 / 1000 * height)
        abs_x2 = int(x2 / 1000 * width)
        draw.rectangle(
            ((abs_x1, abs_y1), (abs_x2, abs_y2)), outline=line_color, width=line_width
        )
    return image

def plot_bounding_boxes(image_path: str, bounding_boxes: list[BoundingBox]) -> None:
    with Image.open(image_path) as im:
        annotated_im = draw_bounding_boxes(im, bounding_boxes)
        annotated_im.show()