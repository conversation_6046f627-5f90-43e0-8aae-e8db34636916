import cv2
import os
import json
import random
import datetime
import re
import io
from PIL import Image
import numpy as np
from pathlib import Path
from google import genai
import streamlit as st
import dotenv

dotenv.load_dotenv()

os.environ['GOOGLE_API_KEY'] = os.getenv("GOOGLE_API_KEY")

def call_gemini_api(prompt, image=None):
    try:
        client = genai.Client()        
        if image:
            pil_image = Image.open(io.BytesIO(image))            
            contents = [prompt, pil_image]
            response = client.models.generate_content(model="gemini-2.0-flash",contents=contents)
        else:
            response = client.models.generate_content(model="gemini-2.0-flash",contents=prompt)
        
        response_text = response.text
        return response_text
            
    except Exception as e:
        st.error(f"Error calling Gemini API: {str(e)}")
        return {"error": str(e)}

def capture_photo():
    """
    Capture photo from webcam using Stream<PERSON>'s camera_input
    """
    st.write("Please ensure your face is clearly visible in the camera.")
    
    camera_photo = st.camera_input("Take a photo", key="camera_input")
    
    if camera_photo is not None:
        bytes_data = camera_photo.getvalue()
        return bytes_data
    
    return None

def match_faces(id_photo_bytes, live_photo_bytes):
    """
    Match faces between ID photo and live capture using improved face recognition
    Returns only match status and confidence score
    """
    try:
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        def detect_face(image_bytes):
            nparr = np.frombuffer(image_bytes, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            faces = face_cascade.detectMultiScale(gray, 1.1, 5, minSize=(30, 30))
            
            if len(faces) == 0:
                return None, "No face detected"
            
            largest_face = max(faces, key=lambda rect: rect[2] * rect[3])
            x, y, w, h = largest_face
            
            padding = int(w * 0.2)  # 20% padding
            x_padded = max(0, x - padding)
            y_padded = max(0, y - padding)
            w_padded = min(img.shape[1] - x_padded, w + 2*padding)
            h_padded = min(img.shape[0] - y_padded, h + 2*padding)
            
            face_roi = img[y_padded:y_padded+h_padded, x_padded:x_padded+w_padded]            
            face_roi = cv2.resize(face_roi, (100, 100))
            face_roi_gray = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)
            face_roi_normalized = cv2.equalizeHist(face_roi_gray)
            
            return face_roi_normalized, None
        
        id_face, id_error = detect_face(id_photo_bytes)
        live_face, live_error = detect_face(live_photo_bytes)
        
        if id_error:
            return {"match": False, "confidence": 0}
        if live_error:
            return {"match": False, "confidence": 0}
        
        hist1 = cv2.calcHist([id_face], [0], None, [256], [0, 256])
        hist2 = cv2.calcHist([live_face], [0], None, [256], [0, 256])
        
        cv2.normalize(hist1, hist1, 0, 1, cv2.NORM_MINMAX)
        cv2.normalize(hist2, hist2, 0, 1, cv2.NORM_MINMAX)
        
        correl_score = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        template_score = cv2.matchTemplate(id_face, live_face, cv2.TM_CCORR_NORMED)[0][0]
        combined_score = 0.6 * max(0, correl_score) + 0.4 * template_score
        
        confidence = round(combined_score * 100, 2)
        match_threshold = 30
        
        return {
            "match": confidence > match_threshold,
            "confidence": confidence
        }
    
    except Exception as e:
        return {
            "match": False,
            "confidence": 0
        }

def extract_details_from_document(document_path):
    """
    Extract details from uploaded document using Gemini API
    """
    try:
        # Read the image file
        with open(document_path, "rb") as f:
            image_bytes = f.read()
        
        prompt = """
        Analyze this ID document (Aadhaar/Voter ID/PAN card) image and extract the following fields:
        - Full name
        - Father's name
        - Date of birth
        - Address
        - Document number (if visible)
        
        Return the extracted information in valid JSON format with the following structure:
        {
            "name": "extracted name",
            "father_name": "extracted father name",
            "dob": "extracted date of birth",
            "address": "extracted address",
            "document_number": "extracted document number",
        }
        """
        
        result = call_gemini_api(prompt, image_bytes)
        
        return result
        
    except Exception as e:
        st.error(f"Error extracting details: {str(e)}")
        return {
            "error": str(e),
            "name": None,
            "father_name": None,
            "dob": None,
            "address": None,
            "document_number": None,
        }

def verify_kyc(user_data, document_data, face_match_result):
    """
    Verify KYC using Gemini for reasoning with actual API call
    """
    verification_prompt = f"""
    You are an AI KYC verification assistant. Analyze the following information and provide verification reasoning:
    
    User Input:
    {user_data}
    
    Document Extracted:
    {document_data}
    
    Face Match:
    {face_match_result}
    
    Note don't mention the rules in the response. Your only goal is to provide reasoning that compares the user input with document extracted. 

    Verification Rules:
    1. Name on document should match user input (considering minor spelling variations)
    2. Date of birth should match exactly. Note the date format is: MM.DD.YYYY
    3. Father's name should match (considering minor spelling variations)
    4. Address should generally match (components should be similar)
    5. Face match should have >30% confidence
    6. Ignore Region and State user input
    7. The reasoning key needs to be in MARKDOWN. 
    
    Please analyze the data and:
    1. Provide a detailed reasoning for the verification decision
    2. List any discrepancies found
    3. Provide a final decision: "Approved" or "Rejected"
    4. Return your response in this JSON format:
    {{
        "reasoning": "Your detailed reasoning in Markdown syntax only",
        "discrepancies": ["list", "of", "issues"],
        "status": "Approved or Rejected"
    }}
    """

    gemini_response = call_gemini_api(verification_prompt)
    gemini_response_cleaned = re.sub(r'^```json|```$', '', gemini_response.strip(), flags=re.MULTILINE).strip()

    gemini_response = json.loads(gemini_response_cleaned)

    reasoning = gemini_response.get("reasoning", "")
    status = gemini_response.get("status", "Pending")

    if "error" in gemini_response:
        reasoning = "Error in verification process. Please try again."
        status = "Pending"
    
    verification_result = {
        "status": status,
        "reasoning": reasoning,
        "face_match_score": round(face_match_result.get("confidence", 0), 2),
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return verification_result

def get_mock_data():
    """Generate mock data for dashboard display"""
    regions = ["North", "South", "East", "West", "Central"]
    states = ["Delhi", "Maharashtra", "Tamil Nadu", "West Bengal", "Karnataka", "Uttar Pradesh"]
    verification_types = ["Digital KYC", "Physical KYC"]
    verification_statuses = ["Approved", "Rejected", "Pending"]
    
    def random_date():
        days = random.randint(1, 90)
        return (datetime.datetime.now() - datetime.timedelta(days=days)).strftime("%Y-%m-%d %H:%M:%S")
    
    mock_data = []
    for i in range(100):
        verification_type = random.choices(
            verification_types, 
            weights=[0.9, 0.1], 
            k=1
        )[0]
        
        if verification_type == "Digital KYC":
            status_weights = [0.95, 0.03, 0.02]  
        else:
            status_weights = [0.75, 0.15, 0.1]  
            
        status = random.choices(
            verification_statuses,
            weights=status_weights,
            k=1
        )[0]
        
        mock_entry = {
            "name": f"Customer {i+1}",
            "dob": f"{random.randint(1950, 2000)}-{random.randint(1, 12)}-{random.randint(1, 28)}",
            "father_name": f"Father {i+1}",
            "verification_type": verification_type,
            "verification_status": status,
            "region": random.choice(regions),
            "state": random.choice(states),
            "csm": f"CSM-{random.randint(1, 10)}",
            "asm": f"ASM-{random.randint(1, 5)}",
            "submission_time": random_date(),
            "address": f"Address {i+1}, City {random.randint(1, 20)}"
        }
        mock_data.append(mock_entry)
    
    # Sort by submission time
    mock_data.sort(key=lambda x: x["submission_time"], reverse=True)
    
    return mock_data

# def save_uploaded_file(uploaded_file):
#     """Save uploaded file to temp directory and return path"""
#     # Create temp directory if it doesn't exist
#     temp_dir = Path("temp")
#     if not temp_dir.exists():
#         temp_dir.mkdir(parents=True)
    
#     # Save the file - use filename attribute instead of name
#     file_path = temp_dir / uploaded_file.filename
#     with open(file_path, "wb") as f:
#         f.write(uploaded_file.file.read())
    
#     return str(file_path)

def save_uploaded_file(uploaded_file):
    """Save uploaded file to temp directory and return path"""
    # Create temp directory if it doesn't exist
    temp_dir = Path("temp")
    if not temp_dir.exists():
        temp_dir.mkdir(parents=True)
    
    # For FastAPI UploadFile objects
    if hasattr(uploaded_file, 'filename') and hasattr(uploaded_file, 'file'):
        # FastAPI UploadFile
        file_path = temp_dir / uploaded_file.filename
        with open(file_path, "wb") as f:
            f.write(uploaded_file.file.read())
    # For Streamlit UploadedFile objects
    elif hasattr(uploaded_file, 'name'):
        # Streamlit UploadedFile
        file_path = temp_dir / uploaded_file.name
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getvalue())
    else:
        raise ValueError("Unsupported file type")
    
    return str(file_path)

def load_css():
    """Load custom CSS for better UI"""
    st.markdown("""
    <style>
        /* Main container */
        .stApp {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        /* Headers */
        h1, h2, h3 {
            color: #1E3A8A;
            font-weight: 600;
        }
        
        /* Cards */
        div.stForm {
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        /* Buttons */
        .stButton button {
            border-radius: 5px;
            font-weight: 500;
            border: none;
            padding: 10px 20px;
            background-color: #2563EB;
            color: white;
            transition: all 0.3s;
        }
        
        .stButton button:hover {
            background-color: #1D4ED8;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        /* Status colors */
        .status-approved {
            color: #10B981;
            font-weight: bold;
        }
        
        .status-rejected {
            color: #EF4444;
            font-weight: bold;
        }
        
        .status-pending {
            color: #F59E0B;
            font-weight: bold;
        }
        
        /* Dashboard metrics */
        div[data-testid="stMetric"] {
            background-color: #F3F4F6;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        div[data-testid="stMetric"] > div {
            display: flex;
            justify-content: center;
        }
        
        /* DataFrames */
        .stDataFrame {
            border-radius: 8px;
            overflow: hidden;
        }
        
        /* Sidebar */
        .css-1d391kg {
            background-color: #F8FAFC;
        }
    </style>
    """, unsafe_allow_html=True)

