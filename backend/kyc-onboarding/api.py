# from fastapi import Fast<PERSON>I, File, UploadFile, HTTPException, Form, Body
# from fastapi.middleware.cors import CORSMiddleware
# from fastapi.responses import JSONResponse
# import uvicorn
# from typing import Optional, List, Dict, Any
# from pydantic import BaseModel
# import datetime
# import base64
# from io import BytesIO
# from PIL import Image
# import json

# # Import your existing utils
# from utils import (
#     capture_photo,
#     match_faces,
#     extract_details_from_document,
#     verify_kyc,
#     get_mock_data,
#     save_uploaded_file,
#     load_css
# )
# from annotate import get_bounding_boxes, draw_bounding_boxes

# # Pydantic models for request bodies
# class LivePhotoRequest(BaseModel):
#     session_id: str
#     photo_data: str

# class VerificationRequest(BaseModel):
#     session_id: str

# # Initialize FastAPI app
# app = FastAPI(
#     title="KYC Verification API",
#     description="Backend API for KYC verification system",
#     version="1.0.0"
# )

# # Configure CORS
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=["*"],  # In production, replace with specific origins
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
# )

# # In-memory storage for session data (in production, use Redis or similar)
# sessions = {}

# # Health check endpoint
# @app.get("/api/health")
# async def health_check():
#     return {"status": "healthy", "timestamp": datetime.datetime.now().isoformat()}

# # Form submission endpoint
# @app.post("/api/v1/kyc/form")
# async def submit_form(
#     name: str = Form(...),
#     dob: str = Form(...),
#     father_name: str = Form(...),
#     ovd_type: str = Form(...),
#     address: str = Form(...),
#     contact: str = Form(...),
#     region: str = Form(...),
#     state: str = Form(...),
#     csm: str = Form(...),
#     asm: str = Form(...)
# ):
#     """Submit KYC form data"""
#     try:
#         user_data = {
#             "name": name,
#             "dob": dob,
#             "father_name": father_name,
#             "ovd_type": ovd_type,
#             "address": address,
#             "contact": contact,
#             "region": region,
#             "state": state,
#             "csm": csm,
#             "asm": asm,
#             "submission_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
#         }
        
#         # Generate session ID
#         session_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
#         sessions[session_id] = {"user_data": user_data}
        
#         return {
#             "success": True,
#             "session_id": session_id,
#             "data": user_data
#         }
#     except Exception as e:
#         raise HTTPException(status_code=400, detail=str(e))

# # Document upload endpoint
# @app.post("/api/v1/kyc/document/upload")
# async def upload_document(
#     session_id: str = Form(...),
#     document: UploadFile = File(...)
# ):
#     """Upload and process KYC document"""
#     try:
#         if session_id not in sessions:
#             raise HTTPException(status_code=404, detail="Session not found")
        
#         # Save document
#         content = await document.read()
#         document_path = save_uploaded_file(document)
        
#         # Need to reset file position after reading
#         await document.seek(0)
        
#         # Extract details
#         extracted_data = extract_details_from_document(document_path)
        
#         # Store in session
#         sessions[session_id]["document_path"] = document_path
#         sessions[session_id]["document_data"] = extracted_data
        
#         return {
#             "success": True,
#             "document_path": document_path,
#             "extracted_data": extracted_data
#         }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

# # ID photo upload endpoint
# @app.post("/api/v1/kyc/photo/id")
# async def upload_id_photo(
#     session_id: str = Form(...),
#     photo: UploadFile = File(...)
# ):
#     """Upload photo from ID document"""
#     try:
#         if session_id not in sessions:
#             raise HTTPException(status_code=404, detail="Session not found")
        
#         content = await photo.read()
#         sessions[session_id]["id_photo"] = content
        
#         # Convert to base64 for response
#         photo_base64 = base64.b64encode(content).decode('utf-8')
        
#         return {
#             "success": True,
#             "photo_data": photo_base64
#         }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

# # Debug endpoint to check session data
# @app.get("/api/v1/debug/session/{session_id}")
# async def debug_session(session_id: str):
#     """Debug endpoint to check session data"""
#     if session_id not in sessions:
#         raise HTTPException(status_code=404, detail="Session not found")
    
#     # Return a safe copy of the session data (without binary data)
#     safe_session = {}
#     for key, value in sessions[session_id].items():
#         if key in ["id_photo", "live_photo"]:
#             safe_session[key] = f"Binary data ({len(value)} bytes)" if value else None
#         else:
#             safe_session[key] = value
    
#     return safe_session

# # Live photo capture endpoint - FIXED with Pydantic model
# @app.post("/api/v1/kyc/photo/live")
# async def submit_live_photo(request: LivePhotoRequest):
#     """Submit live captured photo"""
#     try:
#         session_id = request.session_id
#         photo_data = request.photo_data
        
#         if session_id not in sessions:
#             raise HTTPException(status_code=404, detail="Session not found")
        
#         # Decode base64
#         try:
#             photo_bytes = base64.b64decode(photo_data)
#             sessions[session_id]["live_photo"] = photo_bytes
#         except Exception as e:
#             raise HTTPException(status_code=400, detail=f"Invalid base64 data: {str(e)}")
        
#         return {
#             "success": True,
#             "message": "Live photo captured successfully"
#         }
#     except HTTPException:
#         raise
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

# # Complete verification endpoint - FIXED to accept session_id as string
# @app.post("/api/v1/kyc/verify")
# async def complete_verification(session_id: str = Body(..., embed=False)):
#     """Complete KYC verification process"""
#     try:
#         if session_id not in sessions:
#             raise HTTPException(status_code=404, detail=f"Session not found: {session_id}")
        
#         session_data = sessions[session_id]
        
#         # Check if all required data is present
#         if "id_photo" not in session_data:
#             raise HTTPException(status_code=400, detail="Missing ID photo data")
        
#         if "live_photo" not in session_data:
#             raise HTTPException(status_code=400, detail="Missing live photo data")
        
#         # Perform face matching
#         try:
#             face_match_result = match_faces(
#                 session_data["id_photo"],
#                 session_data["live_photo"]
#             )
#         except Exception as e:
#             raise HTTPException(status_code=500, detail=f"Face matching failed: {str(e)}")
        
#         # Verify KYC
#         try:
#             verification_result = verify_kyc(
#                 session_data.get("user_data", {}),
#                 session_data.get("document_data", {}),
#                 face_match_result
#             )
#         except Exception as e:
#             raise HTTPException(status_code=500, detail=f"KYC verification failed: {str(e)}")
        
#         # Store result
#         session_data["verification_result"] = verification_result
        
#         return {
#             "success": True,
#             "verification_result": verification_result
#         }
#     except HTTPException:
#         raise
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")

# # Document annotation endpoint
# @app.post("/api/v1/kyc/document/annotate")
# async def annotate_document(session_id: str = Body(...)):
#     """Annotate document with bounding boxes"""
#     try:
#         if session_id not in sessions:
#             raise HTTPException(status_code=404, detail="Session not found")
        
#         document_path = sessions[session_id].get("document_path")
#         if not document_path:
#             raise HTTPException(status_code=400, detail="No document found")
        
#         # Open image and get bounding boxes
#         image = Image.open(document_path)
#         bounding_boxes = get_bounding_boxes(image)
#         annotated_image = draw_bounding_boxes(image, bounding_boxes)
        
#         # Convert to base64
#         buffered = BytesIO()
#         annotated_image.save(buffered, format="PNG")
#         img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
        
#         return {
#             "success": True,
#             "annotated_image": img_base64,
#             "bounding_boxes": bounding_boxes
#         }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

# # Analytics endpoints
# @app.get("/api/v1/analytics/dashboard")
# async def get_dashboard_data(
#     region: Optional[str] = None,
#     state: Optional[str] = None,
#     csm: Optional[str] = None,
#     asm: Optional[str] = None
# ):
#     """Get dashboard analytics data"""
#     try:
#         # Get mock data
#         mock_data = get_mock_data()
        
#         # Apply filters
#         filtered_data = mock_data
#         if region and region != "All":
#             filtered_data = [d for d in filtered_data if d.get("region") == region]
#         if state and state != "All":
#             filtered_data = [d for d in filtered_data if d.get("state") == state]
#         if csm and csm != "All":
#             filtered_data = [d for d in filtered_data if d.get("csm") == csm]
#         if asm and asm != "All":
#             filtered_data = [d for d in filtered_data if d.get("asm") == asm]
        
#         # Calculate metrics
#         digital_kyc = len([d for d in filtered_data if d.get("verification_type") == "Digital KYC"])
#         physical_kyc = len([d for d in filtered_data if d.get("verification_type") == "Physical KYC"])
#         total_kyc = digital_kyc + physical_kyc
#         approved = len([d for d in filtered_data if d.get("verification_status") == "Approved"])
#         rejected = len([d for d in filtered_data if d.get("verification_status") == "Rejected"])
#         pending = len([d for d in filtered_data if d.get("verification_status") == "Pending"])
        
#         approval_rate = (approved / total_kyc * 100) if total_kyc > 0 else 0
        
#         # Trends data
#         trends = [
#             {"month": "Jan", "digital": 80, "physical": 20},
#             {"month": "Feb", "digital": 85, "physical": 15},
#             {"month": "Mar", "digital": 90, "physical": 10}
#         ]
        
#         return {
#             "total_verifications": total_kyc,
#             "digital_kyc_count": digital_kyc,
#             "physical_kyc_count": physical_kyc,
#             "approved_count": approved,
#             "rejected_count": rejected,
#             "pending_count": pending,
#             "approval_rate": approval_rate,
#             "recent_verifications": filtered_data[:10],
#             "trends": trends
#         }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

# @app.get("/api/v1/analytics/filters")
# async def get_filter_options():
#     """Get available filter options"""
#     try:
#         mock_data = get_mock_data()
        
#         return {
#             "regions": list(set([d.get("region", "Unknown") for d in mock_data if d.get("region")])),
#             "states": list(set([d.get("state", "Unknown") for d in mock_data if d.get("state")])),
#             "csm_names": list(set([d.get("csm", "Unknown") for d in mock_data if d.get("csm")])),
#             "asm_names": list(set([d.get("asm", "Unknown") for d in mock_data if d.get("asm")]))
#         }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

# # Session cleanup endpoint
# @app.delete("/api/v1/session/{session_id}")
# async def cleanup_session(session_id: str):
#     """Clean up session data"""
#     if session_id in sessions:
#         del sessions[session_id]
#         return {"success": True, "message": "Session cleaned up"}
#     else:
#         raise HTTPException(status_code=404, detail="Session not found")

# if __name__ == "__main__":
#     uvicorn.run(app, host="0.0.0.0", port=8007)

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from typing import Optional
import datetime
import base64
from io import BytesIO
from PIL import Image
import json
import tempfile
import os
from pathlib import Path
from pydantic import BaseModel

# Import your existing utils
from utils import (
    capture_photo,
    match_faces,
    extract_details_from_document,
    verify_kyc,
    get_mock_data,
    load_css
)
from annotate import get_bounding_boxes, draw_bounding_boxes

# Pydantic models for request bodies
class LivePhotoRequest(BaseModel):
    session_id: str
    photo_data: str

class VerificationRequest(BaseModel):
    session_id: str

class AnnotationRequest(BaseModel):
    session_id: str

# Fixed save_uploaded_file function
def save_uploaded_file(uploaded_file):
    """Save uploaded file to temp directory and return path"""
    # Create temp directory if it doesn't exist
    temp_dir = Path("temp")
    if not temp_dir.exists():
        temp_dir.mkdir(parents=True)
    
    # Save the file using the uploaded file's filename
    file_path = temp_dir / uploaded_file.filename
    with open(file_path, "wb") as f:
        f.write(uploaded_file.file.read())
    
    return str(file_path)

# Initialize FastAPI app
app = FastAPI(
    title="KYC Verification API",
    description="Backend API for KYC verification system",
    version="1.0.0"
)

# Configure CORS - FIXED
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Add your frontend URLs
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# In-memory storage for session data (in production, use Redis or similar)
sessions = {}

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.datetime.now().isoformat()}

# Form submission endpoint
@app.post("/api/v1/kyc/form")
async def submit_form(
    name: str = Form(...),
    dob: str = Form(...),
    father_name: str = Form(...),
    ovd_type: str = Form(...),
    address: str = Form(...),
    contact: str = Form(...),
    region: str = Form(...),
    state: str = Form(...),
    csm: str = Form(...),
    asm: str = Form(...)
):
    """Submit KYC form data"""
    try:
        user_data = {
            "name": name,
            "dob": dob,
            "father_name": father_name,
            "ovd_type": ovd_type,
            "address": address,
            "contact": contact,
            "region": region,
            "state": state,
            "csm": csm,
            "asm": asm,
            "submission_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Generate session ID
        session_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        sessions[session_id] = {"user_data": user_data}
        
        return {
            "success": True,
            "session_id": session_id,
            "data": user_data
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# Document upload endpoint
@app.post("/api/v1/kyc/document/upload")
async def upload_document(
    session_id: str = Form(...),
    document: UploadFile = File(...)
):
    """Upload and process KYC document"""
    try:
        if session_id not in sessions:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Save document
        document_path = save_uploaded_file(document)
        
        # Extract details
        extracted_data = extract_details_from_document(document_path)
        
        # Store in session
        sessions[session_id]["document_path"] = document_path
        sessions[session_id]["document_data"] = extracted_data
        
        return {
            "success": True,
            "document_path": document_path,
            "extracted_data": extracted_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ID photo upload endpoint
@app.post("/api/v1/kyc/photo/id")
async def upload_id_photo(
    session_id: str = Form(...),
    photo: UploadFile = File(...)
):
    """Upload photo from ID document"""
    try:
        if session_id not in sessions:
            raise HTTPException(status_code=404, detail="Session not found")
        
        content = await photo.read()
        sessions[session_id]["id_photo"] = content
        
        # Convert to base64 for response
        photo_base64 = base64.b64encode(content).decode('utf-8')
        
        return {
            "success": True,
            "photo_data": photo_base64
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Debug endpoint to check session data
@app.get("/api/v1/debug/session/{session_id}")
async def debug_session(session_id: str):
    """Debug endpoint to check session data"""
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Return a safe copy of the session data (without binary data)
    safe_session = {}
    for key, value in sessions[session_id].items():
        if key in ["id_photo", "live_photo"]:
            safe_session[key] = f"Binary data ({len(value)} bytes)" if value else None
        else:
            safe_session[key] = value
    
    return safe_session

# Live photo capture endpoint
@app.post("/api/v1/kyc/photo/live")
async def submit_live_photo(request: LivePhotoRequest):
    """Submit live captured photo"""
    try:
        if request.session_id not in sessions:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Decode base64
        try:
            photo_bytes = base64.b64decode(request.photo_data)
            sessions[request.session_id]["live_photo"] = photo_bytes
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid base64 data: {str(e)}")
        
        return {
            "success": True,
            "message": "Live photo captured successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Complete verification endpoint
@app.post("/api/v1/kyc/verify")
async def complete_verification(request: VerificationRequest):
    """Complete KYC verification process"""
    try:
        if request.session_id not in sessions:
            raise HTTPException(status_code=404, detail=f"Session not found: {request.session_id}")
        
        session_data = sessions[request.session_id]
        
        # Check if all required data is present
        if "id_photo" not in session_data:
            raise HTTPException(status_code=400, detail="Missing ID photo data")
        
        if "live_photo" not in session_data:
            raise HTTPException(status_code=400, detail="Missing live photo data")
        
        # Perform face matching
        try:
            face_match_result = match_faces(
                session_data["id_photo"],
                session_data["live_photo"]
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Face matching failed: {str(e)}")
        
        # Verify KYC
        try:
            verification_result = verify_kyc(
                session_data.get("user_data", {}),
                session_data.get("document_data", {}),
                face_match_result
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"KYC verification failed: {str(e)}")
        
        # Store result
        session_data["verification_result"] = verification_result
        
        return {
            "success": True,
            "verification_result": verification_result
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")

# Document annotation endpoint
@app.post("/api/v1/kyc/document/annotate")
async def annotate_document(request: AnnotationRequest):
    """Annotate document with bounding boxes"""
    try:
        if request.session_id not in sessions:
            raise HTTPException(status_code=404, detail="Session not found")
        
        document_path = sessions[request.session_id].get("document_path")
        if not document_path:
            raise HTTPException(status_code=400, detail="No document found")
        
        # Open image and get bounding boxes
        image = Image.open(document_path)
        bounding_boxes = get_bounding_boxes(image)
        annotated_image = draw_bounding_boxes(image, bounding_boxes)
        
        # Convert to base64
        buffered = BytesIO()
        annotated_image.save(buffered, format="PNG")
        img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
        
        return {
            "success": True,
            "annotated_image": img_base64,
            "bounding_boxes": bounding_boxes
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Analytics endpoints
@app.get("/api/v1/analytics/dashboard")
async def get_dashboard_data(
    region: Optional[str] = None,
    state: Optional[str] = None,
    csm: Optional[str] = None,
    asm: Optional[str] = None
):
    """Get dashboard analytics data"""
    try:
        # Get mock data
        mock_data = get_mock_data()
        
        # Apply filters
        filtered_data = mock_data
        if region and region != "All":
            filtered_data = [d for d in filtered_data if d.get("region") == region]
        if state and state != "All":
            filtered_data = [d for d in filtered_data if d.get("state") == state]
        if csm and csm != "All":
            filtered_data = [d for d in filtered_data if d.get("csm") == csm]
        if asm and asm != "All":
            filtered_data = [d for d in filtered_data if d.get("asm") == asm]
        
        # Calculate metrics
        digital_kyc = len([d for d in filtered_data if d.get("verification_type") == "Digital KYC"])
        physical_kyc = len([d for d in filtered_data if d.get("verification_type") == "Physical KYC"])
        total_kyc = digital_kyc + physical_kyc
        approved = len([d for d in filtered_data if d.get("verification_status") == "Approved"])
        rejected = len([d for d in filtered_data if d.get("verification_status") == "Rejected"])
        pending = len([d for d in filtered_data if d.get("verification_status") == "Pending"])
        
        approval_rate = (approved / total_kyc * 100) if total_kyc > 0 else 0
        
        # Trends data
        trends = [
            {"month": "Jan", "digital": 80, "physical": 20},
            {"month": "Feb", "digital": 85, "physical": 15},
            {"month": "Mar", "digital": 90, "physical": 10}
        ]
        
        return {
            "total_verifications": total_kyc,
            "digital_kyc_count": digital_kyc,
            "physical_kyc_count": physical_kyc,
            "approved_count": approved,
            "rejected_count": rejected,
            "pending_count": pending,
            "approval_rate": approval_rate,
            "recent_verifications": filtered_data[:10],
            "trends": trends
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/analytics/filters")
async def get_filter_options():
    """Get available filter options"""
    try:
        mock_data = get_mock_data()
        
        return {
            "regions": list(set([d.get("region", "Unknown") for d in mock_data if d.get("region")])),
            "states": list(set([d.get("state", "Unknown") for d in mock_data if d.get("state")])),
            "csm_names": list(set([d.get("csm", "Unknown") for d in mock_data if d.get("csm")])),
            "asm_names": list(set([d.get("asm", "Unknown") for d in mock_data if d.get("asm")]))
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Session cleanup endpoint
@app.delete("/api/v1/session/{session_id}")
async def cleanup_session(session_id: str):
    """Clean up session data"""
    if session_id in sessions:
        del sessions[session_id]
        return {"success": True, "message": "Session cleaned up"}
    else:
        raise HTTPException(status_code=404, detail="Session not found")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8007)