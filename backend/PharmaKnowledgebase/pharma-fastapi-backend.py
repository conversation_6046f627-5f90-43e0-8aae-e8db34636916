from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import os
from datetime import datetime

# Import our separated RAG system
from pharma_rag_system import (
    PharmaRAGSystem, 
    create_workflow,
    DocumentMetadata,
    SearchResult,
    WebResult,
    AgentState
)

# FastAPI app
app = FastAPI(title="Pharmaceutical Knowledgebase API", version="1.0.0")

# CORS middleware for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Add your React app URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API requests/responses
class SearchQuery(BaseModel):
    query: str
    include_web: bool = False
    limit: int = 5

class KBSearchQuery(BaseModel):
    query: str
    limit: int = 5
    score_threshold: float = 0.1

class WebSearchQuery(BaseModel):
    query: str
    limit: int = 3

class EmbeddingQuery(BaseModel):
    text: str

class TextProcessingQuery(BaseModel):
    text: str
    chunk_size: int = 800

class AgentTestQuery(BaseModel):
    agent_name: str
    state: Dict[str, Any]

class AgentQuery(BaseModel):
    query: str
    include_web: bool = False
    max_kb_results: int = 5
    max_web_results: int = 3
    temperature: float = 0.1

class AgentStepResult(BaseModel):
    step_name: str
    input_state: Dict[str, Any]
    output_state: Dict[str, Any]
    execution_time: float

class AgentResponse(BaseModel):
    final_answer: str
    sources: List[str]
    query_type: str
    kb_sources_count: int
    web_sources_count: int
    execution_steps: List[AgentStepResult]
    total_execution_time: float

class AnalysisResponse(BaseModel):
    final_answer: str
    sources: List[str]
    query_type: str
    kb_sources_count: int
    web_sources_count: int

# Initialize the RAG system and workflow
rag_system = PharmaRAGSystem()
workflow = create_workflow(rag_system)

# API Endpoints
@app.get("/")
async def root():
    return {"message": "Pharmaceutical Knowledgebase API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    doc_type: str = Form("general")
):
    """Upload and process a document"""
    try:
        # Validate file type
        allowed_types = ['.pdf', '.docx', '.txt']
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in allowed_types:
            raise HTTPException(status_code=400, detail="File type not supported. Please upload PDF, DOCX, or TXT files.")
        
        # Read file content
        file_content = await file.read()
        
        # Process document
        chunks_added = await rag_system.add_document(file_content, file.filename, doc_type)
        
        return {
            "success": True,
            "message": f"Added {chunks_added} searchable sections to your knowledge base!",
            "chunks_added": chunks_added,
            "filename": file.filename,
            "doc_type": doc_type
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

@app.get("/documents", response_model=List[DocumentMetadata])
async def get_documents():
    """Get list of all documents in the knowledge base"""
    try:
        return rag_system.get_kb_documents()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting documents: {str(e)}")

@app.delete("/documents/{filename}")
async def delete_document(filename: str):
    """Delete a specific document from the knowledge base"""
    try:
        success = rag_system.delete_document(filename)
        if success:
            return {"success": True, "message": "Document deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Document not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")

@app.delete("/documents")
async def clear_all_documents():
    """Clear all documents from the knowledge base"""
    try:
        success = rag_system.clear_knowledge_base()
        if success:
            return {"success": True, "message": "Knowledge base cleared successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to clear knowledge base")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error clearing knowledge base: {str(e)}")

@app.get("/sample-questions")
async def get_sample_questions():
    """Generate sample questions based on KB content"""
    try:
        questions = rag_system.generate_sample_questions()
        return {"questions": questions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating sample questions: {str(e)}")

@app.post("/search", response_model=AnalysisResponse)
async def search_knowledge_base(search_query: SearchQuery):
    """Search the knowledge base and generate AI response"""
    try:
        # Create initial state for workflow
        initial_state = {
            "query": search_query.query,
            "query_type": "hybrid" if search_query.include_web else "kb_only",
            "kb_results": [],
            "web_results": [],
            "final_answer": "",
            "sources": [],
            "kb_available": False,
            "include_web": search_query.include_web
        }
        
        # Run workflow
        result = workflow.invoke(initial_state)
        
        return AnalysisResponse(
            final_answer=result["final_answer"],
            sources=result["sources"],
            query_type=result.get("query_type", "unknown"),
            kb_sources_count=result.get("kb_sources_count", 0),
            web_sources_count=result.get("web_sources_count", 0)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing search: {str(e)}")

@app.post("/search-kb", response_model=List[SearchResult])
async def search_knowledge_base_direct(kb_query: KBSearchQuery):
    """Direct knowledge base search without AI synthesis"""
    try:
        results = rag_system.search_kb(kb_query.query, limit=kb_query.limit)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching knowledge base: {str(e)}")

@app.post("/search-web", response_model=List[WebResult])
async def search_web_direct(web_query: WebSearchQuery):
    """Direct web search without AI synthesis"""
    try:
        results = rag_system.search_web(web_query.query, limit=web_query.limit)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching web: {str(e)}")

@app.post("/get-embedding")
async def get_text_embedding(embedding_query: EmbeddingQuery):
    """Generate vector embedding for given text"""
    try:
        embedding = rag_system.get_embedding(embedding_query.text)
        return {
            "embedding": embedding,
            "dimensions": len(embedding),
            "text_length": len(embedding_query.text)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating embedding: {str(e)}")

@app.post("/process-text")
async def process_text(text_query: TextProcessingQuery):
    """Extract and chunk text for processing"""
    try:
        chunks = rag_system.chunk_text(text_query.text, chunk_size=text_query.chunk_size)
        return {
            "original_length": len(text_query.text),
            "chunks_count": len(chunks),
            "chunks": chunks,
            "chunk_size": text_query.chunk_size
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing text: {str(e)}")

@app.post("/test-agent")
async def test_individual_agent(agent_test: AgentTestQuery):
    """Test individual LangGraph agents"""
    try:
        from pharma_rag_system import PharmaAgents
        agents = PharmaAgents(rag_system)
        
        # Map agent names to methods
        agent_methods = {
            "router": agents.router_agent,
            "knowledge": agents.knowledge_agent,
            "synthesis": agents.synthesis_agent
        }
        
        if agent_test.agent_name not in agent_methods:
            raise HTTPException(status_code=400, detail=f"Unknown agent: {agent_test.agent_name}")
        
        # Execute the specific agent
        agent_method = agent_methods[agent_test.agent_name]
        result = agent_method(agent_test.state)
        
        return {
            "agent_name": agent_test.agent_name,
            "input_state": agent_test.state,
            "output_state": result,
            "success": True
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error testing agent: {str(e)}")

@app.post("/run-agent", response_model=AgentResponse)
async def run_agent_workflow(agent_query: AgentQuery):
    """Run the LangGraph agent workflow with detailed step-by-step execution tracking"""
    try:
        import time
        
        start_time = time.time()
        execution_steps = []
        
        # Create initial state for workflow
        initial_state = {
            "query": agent_query.query,
            "query_type": "hybrid" if agent_query.include_web else "kb_only",
            "kb_results": [],
            "web_results": [],
            "final_answer": "",
            "sources": [],
            "kb_available": False,
            "include_web": agent_query.include_web
        }
        
        # Track each step of execution
        current_state = initial_state.copy()
        
        # Step 1: Router Agent
        step_start = time.time()
        from pharma_rag_system import PharmaAgents
        agents = PharmaAgents(rag_system)
        
        router_output = agents.router_agent(current_state)
        current_state.update(router_output)
        
        execution_steps.append(AgentStepResult(
            step_name="router_agent",
            input_state={"query": agent_query.query, "include_web": agent_query.include_web},
            output_state=router_output,
            execution_time=time.time() - step_start
        ))
        
        # Step 2: Knowledge Agent
        step_start = time.time()
        knowledge_output = agents.knowledge_agent(current_state)
        current_state.update(knowledge_output)
        
        execution_steps.append(AgentStepResult(
            step_name="knowledge_agent",
            input_state={"query_type": current_state["query_type"], "kb_available": current_state["kb_available"]},
            output_state={"kb_results_count": len(knowledge_output["kb_results"]), "web_results_count": len(knowledge_output["web_results"])},
            execution_time=time.time() - step_start
        ))
        
        # Step 3: Synthesis Agent
        step_start = time.time()
        synthesis_output = agents.synthesis_agent(current_state)
        current_state.update(synthesis_output)
        
        execution_steps.append(AgentStepResult(
            step_name="synthesis_agent",
            input_state={"sources_available": len(current_state["kb_results"]) + len(current_state["web_results"])},
            output_state={"answer_length": len(synthesis_output["final_answer"]), "sources_count": len(synthesis_output["sources"])},
            execution_time=time.time() - step_start
        ))
        
        total_time = time.time() - start_time
        
        return AgentResponse(
            final_answer=current_state["final_answer"],
            sources=current_state["sources"],
            query_type=current_state.get("query_type", "unknown"),
            kb_sources_count=current_state.get("kb_sources_count", 0),
            web_sources_count=current_state.get("web_sources_count", 0),
            execution_steps=execution_steps,
            total_execution_time=total_time
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running agent workflow: {str(e)}")

@app.get("/kb-stats")
async def get_kb_stats():
    """Get knowledge base statistics"""
    try:
        docs = rag_system.get_kb_documents()
        total_docs = len(docs)
        total_chunks = sum(doc.chunks for doc in docs)
        
        return {
            "total_documents": total_docs,
            "total_chunks": total_chunks,
            "doc_types": list(set(doc.doc_type for doc in docs)) if docs else []
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting stats: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 