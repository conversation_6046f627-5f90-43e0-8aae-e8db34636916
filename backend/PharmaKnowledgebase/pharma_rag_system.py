from typing import Dict, List, Any, Optional
import asyncio
from dataclasses import dataclass
import uuid
import json
from datetime import datetime
import io
import tempfile
import os

# Core libraries
import litellm
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
import openai
from tavily import TavilyClient
import PyPDF2
import docx
from sentence_transformers import SentenceTransformer

# LangGraph
from langgraph.graph import StateGraph, END
from typing_extensions import TypedDict

# Pydantic models for data structures
from pydantic import BaseModel

from dotenv import load_dotenv

load_dotenv()

class SearchResult(BaseModel):
    content: str
    filename: str
    score: float
    doc_type: str
    page_number: int

class WebResult(BaseModel):
    title: str
    content: str
    url: str
    score: float

class DocumentMetadata(BaseModel):
    filename: str
    doc_type: str
    upload_date: str
    chunks: int

class AgentState(TypedDict):
    query: str
    query_type: str
    kb_results: List[Dict]
    web_results: List[Dict]
    final_answer: str
    sources: List[str]
    kb_available: bool
    include_web: bool

@dataclass
class Document:
    id: str
    content: str
    metadata: Dict
    embedding: Optional[List[float]] = None

# Configuration
LITELLM_BASE_URL = os.getenv("LITELLM_BASE_URL")
LITELLM_API_KEY = os.getenv("LITELLM_API_KEY")
LITELLM_MODEL = os.getenv("LITELLM_MODEL")
AZURE_KEY = os.getenv("AZURE_KEY")

QDRANT_URL = os.getenv("QDRANT_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
AZURE_DEPLOYMENT = os.getenv("AZURE_DEPLOYMENT")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
OPENAI_API_VERSION = os.getenv("OPENAI_API_VERSION")

TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")

def init_clients():
    """Initialize external service clients"""
    # Configure LiteLLM
    if LITELLM_BASE_URL and LITELLM_API_KEY:
        litellm.api_base = LITELLM_BASE_URL
        litellm.api_key = LITELLM_API_KEY
    
    # Initialize Qdrant - use memory mode if no URL provided
    if QDRANT_URL:
        qdrant_client = QdrantClient(
            url=QDRANT_URL,
            api_key=QDRANT_API_KEY,
        )
    else:
        # Use in-memory Qdrant for demo/testing
        qdrant_client = QdrantClient(":memory:")
    
    # Initialize Azure OpenAI for embeddings - handle missing credentials
    openai_client = None
    if OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT and OPENAI_API_VERSION:
        try:
            openai_client = openai.AzureOpenAI(
                api_key=OPENAI_API_KEY,
                api_version=OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT
            )
        except Exception as e:
            print(f"Warning: Could not initialize Azure OpenAI client: {e}")
            print("Running in demo mode - AI features will return mock responses")
    else:
        print("Warning: Missing Azure OpenAI credentials - running in demo mode")
    
    # Initialize Tavily for web search
    tavily_client = TavilyClient(api_key=TAVILY_API_KEY) if TAVILY_API_KEY else None
    
    return qdrant_client, openai_client, tavily_client

class PharmaRAGSystem:
    """Core RAG system for pharmaceutical knowledge base"""
    
    def __init__(self):
        self.qdrant_client, self.openai_client, self.tavily_client = init_clients()
        self.collection_name = "pharma_kb_v2"
        self.setup_qdrant_collection()
        
    def setup_qdrant_collection(self):
        """Setup Qdrant collection if it doesn't exist"""
        try:
            collections = self.qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                self.qdrant_client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
                )
        except Exception as e:
            raise Exception(f"Error setting up Qdrant collection: {e}")
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding using Azure OpenAI"""
        if not self.openai_client:
            # Return mock embedding for demo mode
            import hashlib
            import random
            random.seed(int(hashlib.md5(text.encode()).hexdigest(), 16))
            return [random.uniform(-1, 1) for _ in range(1536)]
        
        try:
            response = self.openai_client.embeddings.create(
                input=text,
                model=AZURE_DEPLOYMENT
            )
            return response.data[0].embedding
        except Exception as e:
            raise Exception(f"Error getting embedding: {e}")
    
    def extract_text_from_file(self, file_content: bytes, filename: str) -> str:
        """Extract text from uploaded file"""
        text = ""
        try:
            if filename.lower().endswith('.pdf'):
                with io.BytesIO(file_content) as file_stream:
                    pdf_reader = PyPDF2.PdfReader(file_stream)
                    for page in pdf_reader.pages:
                        text += page.extract_text()
            elif filename.lower().endswith('.docx'):
                with io.BytesIO(file_content) as file_stream:
                    doc = docx.Document(file_stream)
                    for paragraph in doc.paragraphs:
                        text += paragraph.text + "\n"
            else:  # Assume TXT
                text = file_content.decode('utf-8')
        except Exception as e:
            raise Exception(f"Error extracting text from file: {e}")
        return text
    
    def chunk_text(self, text: str, chunk_size: int = 800) -> List[str]:
        """Improved text chunking with overlap"""
        sentences = text.split('. ')
        chunks = []
        current_chunk = []
        current_length = 0
        
        for sentence in sentences:
            sentence_length = len(sentence.split())
            if current_length + sentence_length > chunk_size and current_chunk:
                chunks.append('. '.join(current_chunk) + '.')
                # Keep last sentence for overlap
                current_chunk = [current_chunk[-1]] if current_chunk else []
                current_length = len(current_chunk[-1].split()) if current_chunk else 0
            
            current_chunk.append(sentence)
            current_length += sentence_length
        
        if current_chunk:
            chunks.append('. '.join(current_chunk))
        
        return chunks
    
    async def add_document(self, file_content: bytes, filename: str, doc_type: str = "general"):
        """Add document to knowledge base"""
        try:
            # Extract text
            text = self.extract_text_from_file(file_content, filename)
            
            # Chunk text
            chunks = self.chunk_text(text)
            
            # Create embeddings and points
            points = []
            for i, chunk in enumerate(chunks):
                doc_id = str(uuid.uuid4())
                embedding = self.get_embedding(chunk)
                
                if embedding:
                    # Calculate page number based on chunk size and position
                    page_number = (i // 3) + 1  # Assuming roughly 3 chunks per page
                    
                    point = PointStruct(
                        id=doc_id,
                        vector=embedding,
                        payload={
                            "content": chunk,
                            "filename": filename,
                            "doc_type": doc_type,
                            "chunk_id": i,
                            "page_number": page_number,
                            "upload_date": datetime.now().isoformat()
                        }
                    )
                    points.append(point)
            
            if points:
                self.qdrant_client.upsert(
                    collection_name=self.collection_name,
                    points=points
                )
                return len(points)
            return 0
        except Exception as e:
            raise Exception(f"Unable to process document: {e}")
    
    def clear_knowledge_base(self):
        """Clear all documents from the knowledge base"""
        try:
            # Delete the existing collection
            self.qdrant_client.delete_collection(self.collection_name)
            # Create a new empty collection
            self.qdrant_client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
            )
            return True
        except Exception as e:
            raise Exception(f"Error clearing knowledge base: {e}")
    
    def get_kb_documents(self) -> List[DocumentMetadata]:
        """Get list of all documents in KB with metadata"""
        try:
            scroll_result = self.qdrant_client.scroll(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )
            
            documents = {}
            for point in scroll_result[0]:
                filename = point.payload.get("filename", "Unknown")
                doc_type = point.payload.get("doc_type", "general")
                upload_date = point.payload.get("upload_date", "Unknown")
                
                if filename not in documents:
                    documents[filename] = DocumentMetadata(
                        filename=filename,
                        doc_type=doc_type,
                        upload_date=upload_date,
                        chunks=0
                    )
                documents[filename].chunks += 1
            
            return list(documents.values())
        except Exception as e:
            raise Exception(f"Error getting documents: {e}")
    
    def delete_document(self, filename: str) -> bool:
        """Delete a specific document from the knowledge base"""
        try:
            # Get all points from the collection
            scroll_result = self.qdrant_client.scroll(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )
            
            # Filter points by filename in memory
            point_ids = []
            for point in scroll_result[0]:
                if point.payload.get("filename") == filename:
                    point_ids.append(point.id)
            
            if point_ids:
                # Delete points by their IDs
                self.qdrant_client.delete(
                    collection_name=self.collection_name,
                    points_selector=point_ids
                )
                return True
            return False
        except Exception as e:
            raise Exception(f"Error deleting document: {e}")
    
    def search_kb(self, query: str, limit: int = 5) -> List[SearchResult]:
        """Search knowledge base with improved relevance scoring"""
        try:
            query_embedding = self.get_embedding(query)
            if not query_embedding:
                return []
            
            # Use lower threshold for better recall
            search_result = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit,
                score_threshold=0.1  # Lower threshold for better recall
            )
            
            results = []
            for hit in search_result:
                # Calculate page number based on chunk_id
                chunk_id = hit.payload.get("chunk_id", 0)
                page_number = (chunk_id // 3) + 1  # Assuming roughly 3 chunks per page
                
                results.append(SearchResult(
                    content=hit.payload["content"],
                    filename=hit.payload["filename"],
                    score=hit.score,
                    doc_type=hit.payload.get("doc_type", "general"),
                    page_number=page_number
                ))
            
            return results
        except Exception as e:
            raise Exception(f"Error searching knowledge base: {e}")
    
    def search_web(self, query: str, limit: int = 3) -> List[WebResult]:
        """Search web using Tavily"""
        if not self.tavily_client:
            return []
            
        try:
            response = self.tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=limit,
                include_answer=False
            )
            
            results = []
            for result in response.get('results', []):
                results.append(WebResult(
                    title=result.get('title', 'Web Source'),
                    content=result.get('content', ''),
                    url=result.get('url', ''),
                    score=result.get('score', 0.0)
                ))
            return results
        except Exception as e:
            # Don't raise exception for web search failures
            return []
    
    def get_kb_sample_content(self, limit: int = 10) -> List[str]:
        """Get sample content from KB to generate relevant questions"""
        try:
            scroll_result = self.qdrant_client.scroll(
                collection_name=self.collection_name,
                limit=limit,
                with_payload=True
            )
            
            contents = []
            for point in scroll_result[0]:
                content = point.payload.get("content", "")
                if len(content) > 100:
                    contents.append(content[:500])
            
            return contents
        except Exception as e:
            return []
    
    def generate_sample_questions(self) -> List[str]:
        """Generate sample questions based on actual KB content"""
        sample_contents = self.get_kb_sample_content()
        
        if not sample_contents:
            return [
                "What information is available in the knowledge base?",
                "Can you search for any available documents?",
                "What topics are covered in the uploaded files?"
            ]
        
        kb_summary = "\n".join(sample_contents[:3])
        
        prompt = f"""
        Based on the following knowledge base content, generate 4 specific and relevant questions that users might ask:

        Knowledge Base Content:
        {kb_summary}

        Requirements:
        - Questions should be specific to the content shown
        - Questions should be practical and searchable
        - Avoid generic questions
        - Make questions detailed enough to demonstrate the KB capabilities

        Generate exactly 4 questions, one per line:
        """
        
        try:
            # Check if LiteLLM is properly configured
            if not LITELLM_MODEL or not (LITELLM_BASE_URL or OPENAI_API_KEY):
                # Return basic questions for demo mode
                return [
                    "What information is available in the knowledge base?",
                    "Can you search the uploaded documents?",
                    "What topics are covered in the files?",
                    "What specific details can you find?"
                ]
            
            response = litellm.completion(
                model=LITELLM_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200
            )
            
            questions = response.choices[0].message.content.strip().split('\n')
            questions = [q.strip('- ').strip() for q in questions if q.strip()]
            
            return questions[:4] if len(questions) >= 4 else [
                "What are the main topics in the knowledge base?",
                "Can you search for specific information?",
                "What documents have been uploaded?",
                "What information is available?"
            ]
        except Exception as e:
            return [
                "What information is available in the knowledge base?",
                "Can you search the uploaded documents?",
                "What topics are covered?",
                "What specific details can you find?"
            ]

class PharmaAgents:
    """LangGraph agents for intelligent query processing"""
    
    def __init__(self, rag_system: PharmaRAGSystem):
        self.rag_system = rag_system
    
    def _create_demo_response(self, query: str, kb_results: List, web_results: List, query_type: str) -> str:
        """Create a demo response when AI services are not available"""
        response_parts = []
        
        response_parts.append(f"**Demo Mode Response for: {query}**\n")
        
        if kb_results:
            response_parts.append("**Knowledge Base Results:**")
            for idx, result in enumerate(kb_results[:3], 1):
                response_parts.append(f"{idx}. From {result['filename']} (Page {result.get('page_number', 'N/A')}):")
                response_parts.append(f"   {result['content'][:200]}...")
                response_parts.append("")
        
        if web_results:
            response_parts.append("**Web Search Results:**")
            for idx, result in enumerate(web_results[:3], 1):
                response_parts.append(f"{idx}. {result.get('title', 'Web Result')}:")
                response_parts.append(f"   {result['content'][:200]}...")
                response_parts.append("")
        
        if not kb_results and not web_results:
            response_parts.append("No relevant information found in the knowledge base or web search.")
        
        response_parts.append("**Note:** This is a demo response. For AI-powered analysis, please configure your API keys.")
        
        return "\n".join(response_parts)
    
    def router_agent(self, state: AgentState) -> Dict:
        """Route query and check KB availability"""
        query = state["query"].lower()
        include_web = state.get("include_web", False)
        
        # Check if KB has content
        try:
            info = self.rag_system.qdrant_client.get_collection(
                self.rag_system.collection_name
            )
            kb_available = info.points_count > 0
        except:
            kb_available = False
        
        # Determine search strategy
        web_only_keywords = ["latest", "recent", "current", "news", "today", "update", "breaking", "2024", "2025"]
        is_web_focused = any(word in query for word in web_only_keywords)
        
        if is_web_focused and include_web:
            query_type = "web_focused"
        elif kb_available and include_web:
            query_type = "hybrid"  # Search both KB and web
        elif kb_available:
            query_type = "kb_only"  # Search only KB
        else:
            query_type = "web_only" if include_web else "kb_only"
        
        return {
            "query_type": query_type,
            "kb_available": kb_available
        }
    
    def knowledge_agent(self, state: AgentState) -> Dict:
        """Search KB and web based on strategy"""
        query = state["query"]
        query_type = state["query_type"]
        kb_available = state["kb_available"]
        
        kb_results = []
        web_results = []
        
        # Search KB first if available
        if kb_available:
            kb_results = self.rag_system.search_kb(query, limit=5)
            # Convert to dict for JSON serialization
            kb_results = [result.dict() for result in kb_results]
        
        # Search web only if hybrid search is enabled
        if query_type == "hybrid":
            web_results = self.rag_system.search_web(query, limit=3)
            # Convert to dict for JSON serialization
            web_results = [result.dict() for result in web_results]
        
        return {
            "kb_results": kb_results,
            "web_results": web_results
        }
    
    def synthesis_agent(self, state: AgentState) -> Dict:
        """Generate comprehensive answer using both KB and web results with improved prompt structure"""
        query = state["query"]
        kb_results = state["kb_results"]
        web_results = state["web_results"]
        kb_available = state["kb_available"]
        query_type = state["query_type"]
        
        # Handle case with no results
        if not kb_results and not web_results:
            if not kb_available:
                return {
                    "final_answer": "I don't have any knowledge base to search from. Please upload relevant pharmaceutical documents first, or I can search the web for current information.",
                    "sources": []
                }
            else:
                return {
                    "final_answer": "I couldn't find relevant information about your question in the knowledge base or web search. You may want to try rephrasing your question or upload more relevant documents.",
                    "sources": []
                }
        
        # Prepare context sections
        kb_context_section = ""
        web_context_section = ""
        
        if kb_results:
            kb_entries = []
            for idx, r in enumerate(kb_results[:3], 1):
                kb_entries.append(f"""
{idx}) {r['filename']} (Page {r.get('page_number', 'N/A')}):
{r['content']}
""")
            kb_context_section = "KNOWLEDGE_BASE_SOURCES:\n" + "\n".join(kb_entries)
        
        if web_results and query_type == "hybrid":
            web_entries = []
            for idx, r in enumerate(web_results[:3], 1):
                web_entries.append(f"""
{idx}) {r.get('url', 'No URL Available')}:
{r['content']}
""")
            web_context_section = "WEB_SOURCES:\n" + "\n".join(web_entries)
        
        # Create the improved synthesis prompt
        prompt = f"""You are a specialized pharmaceutical research assistant with expertise in drug development, clinical research, regulatory affairs, and pharmaceutical sciences.

TASK: Provide evidence-based pharmaceutical information using the available sources below.

USER_QUESTION: {query}

AVAILABLE_SOURCES:
{kb_context_section}

{web_context_section if query_type == "hybrid" else ""}

RESPONSE REQUIREMENTS:

• RESPONSE STRUCTURE:
  [Clear, comprehensive response to the question]

  **KEY POINTS:**
  - [3-4 critical points]

  **LIMITATIONS:**
  - [Uncertainties, disclaimers]

• QUALITY MARKERS:
  - Flag conflicts between sources
  - Indicate confidence level (HIGH/MEDIUM/LOW)
  - Note need for clinical validation
  - Highlight insufficient information

• TERMINOLOGY REQUIREMENTS:
  - Generic names (brand names)
  - Mechanism of action
  - Therapeutic class
  - Pharmacokinetics when relevant

CRITICAL: Include medical disclaimers for drug information.

Generate your response following this structure."""
        
        try:
            # Check if LiteLLM is properly configured
            if not LITELLM_MODEL or not (LITELLM_BASE_URL or OPENAI_API_KEY):
                # Demo mode - create a structured response based on available data
                answer = self._create_demo_response(query, kb_results, web_results, query_type)
            else:
                response = litellm.completion(
                    model=LITELLM_MODEL,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=1500,
                    temperature=0.1
                )
                answer = response.choices[0].message.content
            
            # Collect sources with improved formatting
            sources = []
            
            # KB Sources
            for idx, r in enumerate(kb_results, 1):
                page_info = f" (Page {r.get('page_number', 'N/A')})" if r.get('page_number') else ""
                sources.append(f"{idx}) {r['filename']}{page_info}")
            
            # Web Sources  
            if query_type == "hybrid":
                for r in web_results:
                    if r.get('url'):
                        sources.append(r.get('url'))
            
            return {
                "final_answer": answer,
                "sources": sources,
                "query_type": query_type,
                "kb_sources_count": len(kb_results),
                "web_sources_count": len(web_results) if query_type == "hybrid" else 0
            }
            
        except Exception as e:
            return {
                "final_answer": f"Error generating pharmaceutical response: {str(e)}",
                "sources": [],
                "query_type": query_type,
                "kb_sources_count": 0,
                "web_sources_count": 0
            }

def create_workflow(rag_system: PharmaRAGSystem):
    """Create LangGraph workflow for intelligent query processing"""
    agents = PharmaAgents(rag_system)
    
    workflow = StateGraph(AgentState)
    
    # Add nodes
    workflow.add_node("router", agents.router_agent)
    workflow.add_node("knowledge", agents.knowledge_agent)
    workflow.add_node("synthesis", agents.synthesis_agent)
    
    # Define edges
    workflow.add_edge("router", "knowledge")
    workflow.add_edge("knowledge", "synthesis")
    workflow.add_edge("synthesis", END)
    
    # Set entry point
    workflow.set_entry_point("router")
    
    return workflow.compile() 