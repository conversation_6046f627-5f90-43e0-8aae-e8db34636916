import os
import sys
import json
import time
import requests
import traceback
# from dotenv import load_dotenv
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
# import openai
from assemblyai import TranscriptionConfig, Transcriber
from google import genai
from google.genai import types
from typing import Dict, Any, Optional
import assemblyai as aai
import streamlit as st
import dotenv

dotenv.load_dotenv()

aai.settings.api_key = os.getenv("AAI_API_KEY")
EMAIL_ADDRESS = os.getenv("EMAIL_ADDRESS")
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD")
client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

# Configure transcription settings
config = aai.TranscriptionConfig(
    speaker_labels=True,
    sentiment_analysis=True,
    entity_detection=True,
)

AGENT_PROFILES = {
    "Andrew K": {
        "id": "AK001",
        "name": "<PERSON>",
        "categories": ["technical_support", "product_information"],
        "expertise_level": "senior",
        "department": "technical_support"
    },
    "Sarah M": {
        "id": "SM002",
        "name": "Sarah M",
        "categories": ["billing", "account_management"],
        "expertise_level": "mid",
        "department": "customer_service"
    },
    "Michael J": {
        "id": "MJ003",
        "name": "Michael J",
        "categories": ["sales", "product_upsell"],
        "expertise_level": "senior",
        "department": "sales"
    },
    "Lisa P": {
        "id": "LP004",
        "name": "Lisa P",
        "categories": ["returns", "order_management"],
        "expertise_level": "junior",
        "department": "customer_service"
    }
}

CONVERSATION_CATEGORIES = [
    "technical_support",   # Technical issues, troubleshooting, product fixes
    "billing",             # Payments, refunds, charges
    "sales",               # New purchases, upgrades
    "account_management",  # Account issues, profile changes
    "product_information", # Product details, how-to questions
    "returns",             # Product returns, exchanges 
    "order_management"     # Order status, shipping issues
]

# Simple decorator for tracking function execution time
def track_time(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f"⏱️ Starting: {func.__name__}")
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"⏱️ Completed: {func.__name__} - Took {execution_time:.4f} seconds")
        return result
    return wrapper

# Main Call Analysis System class
class CallAnalysisSystem:
    def __init__(self):
        self.transcripts = []
        self.call_summary = {}
        self.agent_analysis = {}
        self.agent_evaluation = None
        self.client = client  # Reference the global client

    # Helper function for consistent JSON parsing
    def parse_llm_json_response(self, result_text):
        """Helper function to parse JSON from LLM responses with consistent error handling."""
        import json
        
        # Try direct JSON parsing first
        try:
            return json.loads(result_text.strip())
        except json.JSONDecodeError:
            # Extract from code blocks if needed
            if "```json" in result_text and "```" in result_text:
                json_content = result_text.split("```json")[1].split("```")[0].strip()
            elif "```" in result_text:
                json_content = result_text.split("```")[1].split("```")[0].strip()
            else:
                json_content = result_text
            
            try:
                return json.loads(json_content)
            except json.JSONDecodeError:
                # Final fallback: Extract key-value pairs using string manipulation
                extracted_data = {}
                lines = result_text.replace("```", "").strip().split("\n")
                for line in lines:
                    if ":" in line:
                        parts = line.split(":", 1)
                        if len(parts) == 2:
                            key = parts[0].strip().strip('"{}').strip()
                            value = parts[1].strip().strip('",').strip()
                            if key and value:
                                # Check if the value looks like a list
                                if value.startswith('[') and value.endswith(']'):
                                    try:
                                        list_value = json.loads(value)
                                        if isinstance(list_value, list):
                                            extracted_data[key] = list_value
                                            continue
                                    except:
                                        pass
                                extracted_data[key] = value
                
                # Only return if we actually extracted something
                if extracted_data:
                    return extracted_data
                
                # If all parsing attempts fail, raise the original error
                raise json.JSONDecodeError(
                    f"Failed to parse LLM response as JSON: {result_text[:100]}...", 
                    result_text, 0
                )

    @track_time
    def identify_agent_and_category(self, transcript: str) -> Dict[str, Any]:
        """Ask LLM to identify agent and conversation category in one call"""
        # Format categories and agents for the prompt
        categories_list = ", ".join(CONVERSATION_CATEGORIES)
        
        # Only include agent names, not full profiles
        agent_names = [profile.get('name', agent_id) for agent_id, profile in AGENT_PROFILES.items()]
        agents_list = ", ".join(agent_names)
        
        prompt = f"""
        You are analyzing a customer service call transcript.
        
        POSSIBLE CATEGORIES: {categories_list}
        
        AGENT NAMES: {agents_list}
        
        TRANSCRIPT:
        {transcript}
        
        === INSTRUCTIONS ===
        Based only on the transcript above:
        
        1. AGENT IDENTIFICATION: Look for explicit mentions of the agent's name in the transcript. This might appear in:
           - Agent introductions ("Hello, my name is [NAME]" or "This is [NAME] speaking")
           - Customer addressing the agent by name ("Thank you, [NAME]")
           - Agent signing off ("My name is [NAME], thank you for calling")
           - Only choose a name from the AGENT NAMES list provided
           - Return "Unidentified" if you cannot find a clear name match from the list
        
        2. CATEGORY IDENTIFICATION: Determine which category best describes this conversation.
           - Focus on the main topic of the call
           - Choose only from the POSSIBLE CATEGORIES provided
        
        IMPORTANT: Do not guess the agent name. If there is no clear evidence of the agent's name in the transcript or if the name mentioned is not in the AGENT NAMES list, return "Unidentified".
        
        Return ONLY a JSON object in this format:
        {{
            "agent_name": "The agent's name or 'Unidentified' if not clearly mentioned",
            "category": "The conversation category"
        }}
        """

        
        try:
            # Send to LLM
            sys_instruct = prompt
            response = self.client.models.generate_content(
                model="gemini-2.0-flash",
                config=types.GenerateContentConfig(
                    system_instruction=sys_instruct),
                contents=["Identify agent and category"]
            )
            
            result_text = response.text
            
            # Use the helper function to parse JSON
            try:
                result = self.parse_llm_json_response(result_text)
                return {
                    "success": True,
                    "agent_name": result.get("agent_name"),
                    "category": result.get("category")
                }
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON: {str(e)}")
                return {
                    "success": False,
                    "error": f"Failed to parse LLM response: {str(e)}",
                    "raw_response": result_text
                }
            
        except Exception as e:
            print(f"Error identifying agent and category: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    @track_time
    def check_agent_category_match(self, agent_name: str, category: str) -> Dict[str, Any]:
        """Check if the agent is appropriate for this category"""
        # Check if agent exists
        if agent_name not in AGENT_PROFILES:
            return {
                "match": False,
                "reason": f"Unknown agent: {agent_name}"
            }
        
        # Get agent profile
        agent_profile = AGENT_PROFILES[agent_name]
        
        # Check if agent handles this category
        match = category in agent_profile["categories"]
        
        if match:
            reason = f"Agent {agent_name} is authorized to handle {category} calls"
        else:
            authorized_categories = ", ".join(agent_profile["categories"])
            reason = f"Agent {agent_name} is not authorized for {category}. They handle: {authorized_categories}"
            
        return {
            "match": match,
            "reason": reason,
            "agent_profile": agent_profile
        }

    @track_time
    def evaluate_agent_performance_with_category(self, transcript_text, agent_name, category, is_authorized):
        """Evaluate the agent's performance based on company standards and category match"""
        company_standards = [
            {
                "name": "Used the customer's name minimum once on the call",
                "criteria": "The agent should use the customer's name at least once during the conversation.",
                "na_condition": "None - this is applicable to all calls."
            },
            {
                "name": "Does Active Listening (remembers info)",
                "criteria": "The agent references information the customer provided earlier in the call without asking for it again.",
                "na_condition": "None - this is applicable to all calls."
            },
            {
                "name": "Does not interrupt",
                "criteria": "The agent allows the customer to finish speaking before responding.",
                "na_condition": "None - this is applicable to all calls."
            },
            {
                "name": "Used apology & empathy wherever required",
                "criteria": "The agent apologizes when appropriate and shows understanding of customer's situation.",
                "na_condition": "Only marked N/A if there was no situation requiring an apology or empathy (no frustration, complaints, or issues mentioned by customer)."
            },
            {
                "name": "Used Please / Thank you wherever appropriate",
                "criteria": "The agent uses polite language throughout the call.",
                "na_condition": "None - this is applicable to all calls."
            },
            {
                "name": "Transferred to correct department",
                "criteria": "If a transfer was needed, the agent transferred to the appropriate department.",
                "na_condition": "Mark as N/A if no transfer was required based on the nature of the call."
            },
            {
                "name": "Did the CSA provide alternatives",
                "criteria": "The agent offered different options or solutions to the customer's inquiry.",
                "na_condition": "Mark as N/A only if the customer's request had exactly one possible solution with no alternatives."
            },
            {
                "name": "Did the CSA maintain proper tone throughout the call",
                "criteria": "The agent remained professional and courteous throughout the entire interaction.",
                "na_condition": "None - this is applicable to all calls."
            },
            {
                "name": "Verified the customer appropriately as per the nature of the call",
                "criteria": "The agent confirmed customer identity when required by policy.",
                "na_condition": "Mark as N/A for inquiries that don't require customer verification per company policy."
            },
            {
                "name": "Provided correct information",
                "criteria": "All information provided by the agent was accurate and current.",
                "na_condition": "None - this is applicable to all calls."
            },
            {
                "name": "Tagged the call properly in the CRM",
                "criteria": "Agent properly categorized the call and entered all required information in the system.",
                "na_condition": "This can only be evaluated if there's evidence in the transcript of CRM tagging."
            }
        ]
    
        standards_json = json.dumps(company_standards, indent=2)
    
        prompt = f"""
        You are evaluating a customer service agent's performance based on a transcript.
        
        AGENT: {agent_name}
        CALL CATEGORY: {category}
        AGENT IS AUTHORIZED FOR THIS CATEGORY: {"Yes" if is_authorized else "No"}
        
        COMPANY STANDARDS:
        {standards_json}
        
        TRANSCRIPT:
        {transcript_text}
        
        For each standard, evaluate using these possible outcomes:
        - "Yes" if the standard was met
        - "No" if the standard was not met
        - "N/A" if the standard does not apply to this call based on the na_condition
        
        Important instructions:
        1. Only use "N/A" when explicitly allowed by the na_condition
        2. A standard should not be marked as "No" if it doesn't apply to the call
        3. Pay careful attention to whether evidence exists in the transcript to evaluate each standard
        4. Be consistent in your evaluations across different calls
        
        Return your evaluation as a JSON object with these fields:
        1. "standards_met": An object with each standard name as key and "Yes"/"No"/"N/A" as value
        2. "strengths": List 2-3 things the agent did well
        3. "areas_for_improvement": List 2-3 areas where the agent could improve
        4. "overall_rating": A score from 1-10
        5. "category_expertise": How well the agent handled this specific category type (High/Medium/Low)
        6. "customer_pain_points": The main issues or problems the customer needed help with
        7. "resolution_quality": Was the issue resolved (Yes/No/Partial) and how satisfied would the customer be (1-10)
        8. "agent_empathy": Did the agent show empathy and understanding (1-10)
        9. "would_recommend": Would the customer want to speak with this agent again (Yes/No/Maybe)
        10. "next_best_actions": Based on customer behavior patterns and unspoken needs, predict 2 specific high-value follow-up actions (10-15 words each).
        """
        
        try:
            # LLM API call
            prompt_start = time.time()
            sys_instruct = prompt
            response = client.models.generate_content(
                model="gemini-2.0-flash",
                config=types.GenerateContentConfig(
                    system_instruction=sys_instruct),
                contents=["Evaluate agent performance"]
            )
            eval_text = response.text
            prompt_end = time.time()
            print(f"⏱️ Agent evaluation LLM call: {prompt_end - prompt_start:.4f}s")
            
            # Parse JSON response
            parsing_start = time.time()
            try:
                evaluation = self.parse_llm_json_response(eval_text)
                result = {
                    "success": True,
                    "evaluation": evaluation
                }
                
                # Store the result
                self.agent_evaluation = result
                parsing_end = time.time()
                print(f"⏱️ JSON parsing: {parsing_end - parsing_start:.4f}s")
                return result
                
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON in agent evaluation: {str(e)}")
                return {
                    "success": False,
                    "error": f"Failed to parse evaluation: {str(e)}",
                    "raw_response": eval_text
                }
                
        except Exception as e:
            print(f"Error evaluating agent performance: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    @track_time
    def analyze_agent_category_match(self, transcript_idx=0):
        """ Complete agent analysis workflow including identification, category matching, and evaluation """
        if not self.transcripts or transcript_idx >= len(self.transcripts):
            print("No transcript available for analysis")
            return None
            
        # Get the formatted transcript
        transcript_text = self.get_text_transcripts()[transcript_idx]
        
        print("\n⏱️ STEP 1: AGENT AND CATEGORY IDENTIFICATION")
        step_start = time.time()
        identification = self.identify_agent_and_category(transcript_text)
        print("IDENTIFICATION (IDENTIFY_AGENT_AND_CATEGORY)",identification)
        step_end = time.time()
        print(f"⏱️ Step 1 completed in {step_end - step_start:.4f}s")
        
        if not identification["success"]:
            return {
                "success": False,
                "error": identification.get("error", "Failed to identify agent and category"),
                "stage": "identification"
            }
            
        agent_name = identification["agent_name"]
        category = identification["category"]
        
        print(f"Identified Agent: {agent_name}")
        print(f"Conversation Category: {category}")
        
        # Step 2: Check if agent is authorized for this category
        print("\n⏱️ STEP 2: AGENT-CATEGORY MATCH CHECK")
        step_start = time.time()
        match_result = self.check_agent_category_match(agent_name, category)
        print("MATCH RESULTS (CHECK_AGENT_CATEGORY_MATCH)",match_result)
        step_end = time.time()
        print(f"⏱️ Step 2 completed in {step_end - step_start:.4f}s")
        
        if "agent_profile" not in match_result:
            return {
                "success": False,
                "error": match_result.get("reason", "Failed to check agent authorization"),
                "stage": "matching",
                "identification": identification
            }
            
        is_authorized = match_result["match"]
        agent_profile = match_result["agent_profile"]
        
        print(f"Agent is authorized: {is_authorized}")
        print(f"Reason: {match_result['reason']}")
        
        # Step 3: Evaluate agent performance
        print("\n⏱️ STEP 3: AGENT PERFORMANCE EVALUATION")
        step_start = time.time()
        evaluation = self.evaluate_agent_performance_with_category(
            transcript_text, 
            agent_name, 
            category, 
            is_authorized
        )

        print("EVALUATION (EVALUATE_AGENT_PERFORMANCE_WITH_CATEGORY)",evaluation)
        step_end = time.time()
        print(f"⏱️ Step 3 completed in {step_end - step_start:.4f}s")
        
        if not evaluation["success"]:
            return {
                "success": False,
                "error": evaluation.get("error", "Failed to evaluate performance"),
                "stage": "evaluation",
                "identification": identification,
                "match_result": match_result
            }
            
        # Complete analysis
        final_result = {
            "success": True,
            "agent_name": agent_name,
            "agent_profile": agent_profile,
            "category": category,
            "is_authorized": is_authorized,
            "authorization_details": match_result["reason"],
            "evaluation": evaluation["evaluation"],
            "timestamp": time.time()
        }
        
        # Store the complete result
        self.agent_analysis = final_result
        print("FINAL_RESULT",final_result)
        return final_result

    @track_time
    def upload_audio_files(self, file_paths):
        """ Upload multiple audio files for processing """

        print("Uploading and transcribing audio files...")
        transcripts = []
        
        for file_path in file_paths:
            try:
                file_start = time.time()
                transcript = aai.Transcriber().transcribe(
                    file_path,
                    config=config
                )
                file_end = time.time()
                transcripts.append(transcript)
                print(f"Transcribed: {file_path} in {file_end - file_start:.4f}s")
            except Exception as e:
                print(f"Error transcribing {file_path}: {str(e)}")
                
        self.transcripts = transcripts
        return transcripts
    
    @track_time
    def get_text_transcripts(self):
        """Extract and format text from transcripts with speaker information"""
        formatted_transcripts = []
        
        for i, transcript in enumerate(self.transcripts):
            start_time = time.time()
            if not transcript.utterances:
                formatted_text = transcript.text
            else:
                # Format with speaker labels more efficiently using join and list comprehension
                formatted_text = "\n".join(
                    f"Speaker {utterance.speaker}: {utterance.text}" 
                    for utterance in transcript.utterances
                )
            
            formatted_transcripts.append(formatted_text)
            end_time = time.time()
            print(f"Processed transcript {i+1} in {end_time - start_time:.4f}s")
            
        return formatted_transcripts
    
    @track_time
    def perform_sentiment_analysis(self):
        """ Extract sentiment analysis from AssemblyAI transcripts """

        sentiment_results = []
        
        for i, transcript in enumerate(self.transcripts):
            if transcript.sentiment_analysis:
                sentiment_results.append(transcript.sentiment_analysis)
                print(f"Extracted sentiment from transcript {i+1}")
                
        return sentiment_results
    
    @track_time
    def format_sentiment_data(self, sentiment_results):
        """ Format sentiment analysis results for monitoring purposes """
        format_start = time.time()
        formatted_conversations = []
        
        for i, result_set in enumerate(sentiment_results):
            # Use list comprehension for more efficient processing
            conversation = [
                {
                    "speaker": f"Speaker {sentiment.speaker}",
                    "text": sentiment.text,
                    "sentiment": str(sentiment.sentiment).replace("SentimentType.", "").replace("'", "").replace(">", "")
                }
                for sentiment in result_set
            ]
            formatted_conversations.append(conversation)
            
        format_end = time.time()
        print(f"⏱️ Sentiment formatting: {format_end - format_start:.4f}s")
        return formatted_conversations
    
    @track_time
    def analyze_call(self, transcript_text):
        """ Use LLM to analyze call transcript and generate a summary """
        # Prepare prompt
        prompt_start = time.time()
        prompt = """
Analyze the following customer service call transcript and provide a structured summary with these fields:
- Summary: A crisp overview of what the conversation is about.keep it short in one or two sentences.
- Topic: Main subject of the call
- Product: Product or service discussed
- Resolved: Whether the issue was resolved (Yes/No/Partial)
- Callback: Whether a callback is needed (Yes/No)
- Politeness: Rate politeness level of agent (Low/Medium/High)
- Customer sentiment: Overall customer sentiment (Negative/Neutral/Positive)
- Agent sentiment: Overall agent sentiment (Negative/Neutral/Positive)
- Action: What actions did the Agent take?

Provide the answer in JSON format.
"""
        prompt_end = time.time()
        print(f"⏱️ Prompt preparation: {prompt_end - prompt_start:.4f}s")
        
        try:
            # LLM API call
            api_start = time.time()
            sys_instruct = prompt
            response = client.models.generate_content(
                model="gemini-2.0-flash",
                config=types.GenerateContentConfig(
                    system_instruction=sys_instruct),
                contents=[f"Transcript: {transcript_text}"]
            )
            summary_text = response.text
            api_end = time.time()
            print(f"⏱️ LLM API call: {api_end - api_start:.4f}s")
            
            # Parse JSON response
            parsing_start = time.time()
            try:
                summary = self.parse_llm_json_response(summary_text)
                parsing_end = time.time()
                print(f"⏱️ JSON parsing: {parsing_end - parsing_start:.4f}s")
                
                self.call_summary = summary
                print("SUMMARY", summary)
                return summary
                
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON: {str(e)}")
                print(f"Raw response: {summary_text}")
                return None
                
        except Exception as e:
            error_time = time.time()
            print(f"Error analyzing call with LLM: {str(e)}")
            print(traceback.format_exc())
            return None
        
    @track_time
    def predict_customer_needs(self, transcript_text, call_summary=None):
        """Predict customer needs and recommend next best actions based on call transcript"""
        # Use previously generated summary if available, otherwise use empty dict
        summary_context = ""
        if call_summary:
            # Extract relevant fields from call summary
            topic = call_summary.get('Topic', 'Unknown')
            product = call_summary.get('Product', 'Unknown')
            resolved = call_summary.get('Resolved', 'Unknown')
            customer_sentiment = call_summary.get('Customer sentiment', 'Unknown')
            
            # Format summary information
            summary_context = f"""
            Call summary information:
            - Topic: {topic}
            - Product: {product}
            - Issue resolved: {resolved}
            - Customer sentiment: {customer_sentiment}
            """
        
        # Prepare prompt for LLM
        prompt = f"""
    You are analyzing a customer service call transcript to identify customer needs 
    and recommend next best actions for follow-up.

    {summary_context}
    
    Based on the transcript below, provide:
    1. 2-3 underlying customer needs that might not have been directly addressed
    2. 2-3 specific next best actions the agent should take

    IMPORTANT GUIDELINES:
    - Each point must be a single, concise sentence (10-20 words maximum)
    - Express each need or action in plain, direct language
    - Do not include explanations, justifications, or multiple sentences
    - Focus on clarity, actionability, and brevity
    - Avoid technical jargon unless absolutely necessary

    TRANSCRIPT:
    {transcript_text}
    
    Return your analysis in JSON format with these fields:
    - "underlying_needs": Array of single-sentence customer needs
    - "next_best_actions": Array of single-sentence action items
    """
        
        try:
            # Send to LLM
            print("⏱️ Sending prediction request to LLM")
            sys_instruct = prompt
            response = self.client.models.generate_content(
                model="gemini-2.0-flash",
                config=types.GenerateContentConfig(
                    system_instruction=sys_instruct),
                contents=["Predict customer needs and recommend actions"]
            )
            
            result_text = response.text
            print("⏱️ Received prediction response from LLM")
            
            try:
                # First try direct JSON parsing
                predictions = json.loads(result_text.strip())
            except json.JSONDecodeError:
                # Extract from code blocks if needed
                try:
                    if "```json" in result_text and "```" in result_text:
                        json_content = result_text.split("```json")[1].split("```")[0].strip()
                    elif "```" in result_text:
                        json_content = result_text.split("```")[1].split("```")[0].strip()
                    else:
                        json_content = result_text
                    
                    predictions = json.loads(json_content)
                except (json.JSONDecodeError, IndexError) as e:
                    print(f"Error parsing JSON: {str(e)}")
                    # Fallback: Extract key-value pairs using string manipulation
                    predictions = {}
                    lines = result_text.replace("```", "").strip().split("\n")
                    for line in lines:
                        if ":" in line:
                            parts = line.split(":", 1)
                            if len(parts) == 2:
                                key = parts[0].strip().strip('"{}').strip()
                                value = parts[1].strip().strip('",').strip()
                                if key and value:
                                    # Check if the value looks like a list
                                    if value.startswith('[') and value.endswith(']'):
                                        try:
                                            list_value = json.loads(value)
                                            if isinstance(list_value, list):
                                                predictions[key] = list_value
                                                continue
                                        except:
                                            pass
                                    predictions[key] = value
                
                    if not predictions:
                        print(f"Failed to parse predictions from: {result_text}")
                        return {
                            "success": False,
                            "error": "Failed to parse LLM response",
                            "raw_response": result_text
                        }
        
            return {
                "success": True,
                "predictions": predictions
            }
        
        except Exception as e:
            print(f"Error predicting customer needs: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
            
            
    @track_time
    def check_human_intervention(self):
        """ Determine the type of intervention needed based on call summary analysis """
        if not self.call_summary:
            return "urgent_email"  # Default to highest priority if no summary
        
        # Extract all relevant fields from the call summary
        is_resolved = self.call_summary.get("Resolved", "").lower() in ["yes", "true"]
        is_partial = self.call_summary.get("Resolved", "").lower() == "partial"
        needs_callback = self.call_summary.get("Callback", "").lower() in ["yes", "true"]
        customer_sentiment = self.call_summary.get("Customer sentiment", "").lower()
        agent_sentiment = self.call_summary.get("Agent sentiment", "").lower()
        agent_politeness = self.call_summary.get("Politeness", "").lower()
        topic = self.call_summary.get("Topic", "").lower()
        
        # Scenario 1: Urgent human intervention (email)
        if any([
            # Unresolved issue with negative customer sentiment and needs callback
            (not is_resolved and customer_sentiment == "negative" and needs_callback),
            # Partially resolved with negative sentiment and callback needed
            (is_partial and customer_sentiment == "negative" and needs_callback),
            # Low agent politeness with negative customer sentiment
            (agent_politeness == "low" and customer_sentiment == "negative"),
            # Critical topics requiring immediate attention regardless of other factors
            any(critical in topic for critical in ["billing error", "outage", "security", "fraud", "legal"])
        ]):
            return "urgent_email"
            
        # Scenario 2: High priority ticket
        elif any([
            # Unresolved with negative sentiment but no callback requested
            (not is_resolved and customer_sentiment == "negative"),
            # Partially resolved with negative sentiment
            (is_partial and customer_sentiment == "negative"),
            # Agent and customer both negative (indicates problematic interaction)
            (agent_sentiment == "negative" and customer_sentiment == "negative"),
            # Medium agent politeness with negative customer sentiment
            (agent_politeness == "medium" and customer_sentiment == "negative"),
            # Callback requested but not for negative sentiment cases (already covered in urgent)
            (needs_callback and customer_sentiment != "positive")
        ]):
            return "high_priority_ticket"
            
        # Scenario 3: Normal ticket
        elif any([
            # Unresolved or partially resolved but customer not negative
            (not is_resolved or is_partial) and customer_sentiment != "negative",
            # Resolved but callback still needed
            (is_resolved and needs_callback),
            # Any neutral sentiment case
            (customer_sentiment == "neutral")
        ]):
            return "normal_ticket"
            
        # No intervention needed (resolved, positive sentiment, no callbacks)
        return "none"
    

    @track_time
    def send_email_alert(self, recipient_email, intervention_type):
        """Send email alert for human intervention"""
        if not self.call_summary:
            return False
            
        try:
            # Set up email
            msg = MIMEMultipart()
            msg['From'] = EMAIL_ADDRESS
            msg['To'] = recipient_email
            print("INVERVENTION TYPE",intervention_type)
            if intervention_type == "urgent_email":
                print("URGENT: Human intervention required. Sending email alert...")
                priority = "urgent"
            elif intervention_type == "high_priority_ticket":
                print("HIGH PRIORITY: Sending high priority ticket email...")
                priority = "high"
            elif intervention_type == "normal_ticket":
                print("NORMAL: Sending normal priority ticket email...")
                priority = "normal"
            else:
                priority = "normal"
            
            # Set subject based on priority
            if priority == "urgent":
                msg['Subject'] = f"URGENT: Human Intervention Needed - Ticket #{id(self.transcripts[0])}"
            elif priority == "high":
                msg['Subject'] = f"HIGH PRIORITY: Ticket #{id(self.transcripts[0])}"
            else:
                msg['Subject'] = f"NORMAL: Ticket #{id(self.transcripts[0])}"
            
            # Create email body
            ticket_type = ""
            action_message = ""
            
            if priority == "urgent":
                ticket_type = "URGENT INTERVENTION TICKET"
                action_message = "review the call and take immediate action"
            elif priority == "high":
                ticket_type = "HIGH PRIORITY TICKET"
                action_message = "address this ticket as a high priority"
            else:
                ticket_type = "NORMAL TICKET"
                action_message = "address this ticket at normal priority"
            
            # Prepare agent information if available
            agent_info = ""
            if hasattr(self, 'agent_analysis') and self.agent_analysis and self.agent_analysis.get('success', False):
                agent_data = self.agent_analysis
                agent_evaluation = agent_data.get('evaluation', {})
                
                # Format the agent analysis information
                agent_info = f"""
                <h3>Agent Information:</h3>
                <ul>
                  <li><strong>Agent Name:</strong> {agent_data.get('agent_name', 'Unknown')}</li>
                  <li><strong>Department:</strong> {agent_data.get('agent_profile', {}).get('department', 'Unknown')}</li>
                  <li><strong>Expertise Level:</strong> {agent_data.get('agent_profile', {}).get('expertise_level', 'Unknown')}</li>
                  <li><strong>Call Category:</strong> {agent_data.get('category', 'Unknown')}</li>
                  <li><strong>Agent is Authorized:</strong> <span style="color:{'green' if agent_data.get('is_authorized', False) else 'red'}">{'Yes' if agent_data.get('is_authorized', False) else 'No'}</span></li>
                  <li><strong>Category Expertise:</strong> {agent_evaluation.get('category_expertise', 'Unknown')}</li>
                  <li><strong>Overall Rating:</strong> {agent_evaluation.get('overall_rating', 'N/A')}/10</li>
                </ul>
                
                <h4>Agent Strengths:</h4>
                <ul>
                  {' '.join([f'<li>{strength}</li>' for strength in agent_evaluation.get('strengths', ['None identified'])])}
                </ul>
                
                <h4>Agent Areas for Improvement:</h4>
                <ul>
                  {' '.join([f'<li>{area}</li>' for area in agent_evaluation.get('areas_for_improvement', ['None identified'])])}
                </ul>
                """
                
            body = f"""
            <html>
              <body>
                <h2>{ticket_type}</h2>
                <p>A recent customer service call has been processed and this ticket has been created.</p>
                
                <h3>Ticket Information:</h3>
                <ul>
                  <li><strong>Ticket #:</strong> {id(self.transcripts[0])}</li>
                  <li><strong>Priority:</strong> <span style="color:{'red' if priority == 'urgent' else ('orange' if priority == 'high' else 'green')}"><strong>{priority.upper()}</strong></span></li>
                  <li><strong>Topic:</strong> {self.call_summary.get('Topic', 'Unknown')}</li>
                  <li><strong>Product:</strong> {self.call_summary.get('Product', 'Unknown')}</li>
                  <li><strong>Resolved:</strong> {self.call_summary.get('Resolved', 'No')}</li>
                  <li><strong>Callback:</strong> {self.call_summary.get('Callback', 'Unknown')}</li>
                  <li><strong>Customer sentiment:</strong> {self.call_summary.get('Customer sentiment', 'Unknown')}</li>
                  <li><strong>Agent sentiment:</strong> {self.call_summary.get('Agent sentiment', 'Unknown')}</li>
                  <li><strong>Action taken:</strong> {self.call_summary.get('Action', 'Review required')}</li>
                </ul>
                
                
                <p>Please {action_message}.</p>
              </body>
            </html>
            """
            
            msg.attach(MIMEText(body, 'html'))
            
            # Setup SMTP server
            server = smtplib.SMTP('smtp.gmail.com', 587)
            server.starttls()
            server.login(EMAIL_ADDRESS, EMAIL_PASSWORD)
            
            # Send email
            server.send_message(msg)
            server.quit()
            
            print(f"{priority.capitalize()} email alert sent to {recipient_email}")
            return True
            
        except Exception as e:
            print(f"Error sending email alert: {str(e)}")
            return False

