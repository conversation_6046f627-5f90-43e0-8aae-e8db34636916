from fastapi import Fast<PERSON><PERSON>, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional, Dict, Any
import os
import tempfile
from pydantic import BaseModel, EmailStr
import json
import time
import sys

# Import directly from inference.py
from inference import CallAnalysisSystem, AGENT_PROFILES, CONVERSATION_CATEGORIES

# Define Pydantic models (previously in schemas.py)
class TranscriptItem(BaseModel):
    text: str
    speaker: Optional[int] = None
    start: Optional[float] = None
    end: Optional[float] = None

class SentimentItem(BaseModel):
    speaker: str
    text: str
    sentiment: str
    start: Optional[float] = None
    end: Optional[float] = None

class CallSummary(BaseModel):
    summary: str
    topic: str
    product: str
    resolved: str
    callback: str
    politeness: str
    customer_sentiment: str
    key_points: List[str]
    action_items: List[str]

class AgentAnalysis(BaseModel):
    success: bool
    agent_name: str
    agent_profile: Dict[str, Any]
    category: str
    is_authorized: bool
    authorization_details: str
    evaluation: Dict[str, Any]
    timestamp: float

class CustomerNeedsPrediction(BaseModel):
    success: bool
    predictions: Dict[str, List[str]]

class AnalysisResponse(BaseModel):
    success: bool
    call_summaries: List[Optional[CallSummary]]
    agent_analysis: Optional[AgentAnalysis] = None
    sentiment_data: List[List[SentimentItem]]
    customer_needs: Optional[CustomerNeedsPrediction] = None
    transcripts: Optional[List[str]] = None
    error: Optional[str] = None

# Initialize FastAPI app
app = FastAPI(
    title="Call Analysis API",
    description="API for analyzing customer service call recordings",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize the call analysis system directly
call_system = CallAnalysisSystem()

@app.post("/analyze-calls", response_model=AnalysisResponse)
async def analyze_calls(
    files: List[UploadFile] = File(...),
    notification_email: Optional[EmailStr] = Form(None)
):
    """
    Upload and analyze call audio files (complete analysis).
    
    This endpoint:
    1. Transcribes the audio files
    2. Performs sentiment analysis
    3. Generates call summaries
    4. Analyzes agent performance
    5. Predicts customer needs
    """
    if not files:
        raise HTTPException(status_code=400, detail="No audio files provided")
    
    # Create temporary files for processing
    temp_file_paths = []
    try:
        # Save uploaded files to temporary location
        for file in files:
            content = await file.read()
            fd, temp_path = tempfile.mkstemp(suffix=".mp3")
            with os.fdopen(fd, 'wb') as tmp:
                tmp.write(content)
            temp_file_paths.append(temp_path)
        
        # Step 1: Upload and transcribe audio files
        call_system.upload_audio_files(temp_file_paths)
        
        # Step 2: Get text transcripts
        transcripts = call_system.get_text_transcripts()
        
        if not transcripts:
            return {
                "success": False,
                "error": "No transcripts generated. Please check the audio files."
            }
        
        # Step 3: Perform sentiment analysis
        sentiment_results = call_system.perform_sentiment_analysis()
        
        # Format sentiment data for visualization
        formatted_conversations = []
        if sentiment_results:
            formatted_conversations = call_system.format_sentiment_data(sentiment_results)
        
        # Step 4: Analyze call with LLM for summaries
        call_summaries = []
        for transcript in transcripts:
            call_summary = call_system.analyze_call(transcript)
            
            # Normalize keys to match Pydantic model expectations
            if call_summary:
                normalized_summary = {
                    "summary": call_summary.get("Summary", ""),
                    "topic": call_summary.get("Topic", ""),
                    "product": call_summary.get("Product", ""),
                    "resolved": call_summary.get("Resolved", ""),
                    "callback": call_summary.get("Callback", ""),
                    "politeness": call_summary.get("Politeness", ""),
                    "customer_sentiment": call_summary.get("Customer sentiment", ""),
                    "key_points": call_summary.get("Action", []),
                    "action_items": call_summary.get("Action", [])
                }
                call_summaries.append(normalized_summary)
            else:
                call_summaries.append(None)
            
            # Check if human intervention is needed
            call_system.call_summary = call_summary  # Set current summary for checking
            if notification_email:
                intervention_type = call_system.check_human_intervention()
                
                # Send email alert if needed
                if intervention_type != "none":
                    call_system.send_email_alert(notification_email, intervention_type)
        
        # Step 5: Analyze agent performance
        agent_analysis = call_system.analyze_agent_category_match()
        
        # Step 6: Predict customer needs
        customer_needs = None
        if transcripts:
            customer_needs = call_system.predict_customer_needs(
                transcripts[0], 
                call_summaries[0] if call_summaries else None
            )
        
        # Clean up temporary files
        for temp_file in temp_file_paths:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        return {
            "success": True,
            "transcripts": transcripts,
            "call_summaries": call_summaries,
            "agent_analysis": agent_analysis,
            "sentiment_data": formatted_conversations,
            "customer_needs": customer_needs
        }
        
    except Exception as e:
        # Clean up temporary files in case of error
        for temp_file in temp_file_paths:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/agent-profiles")
async def get_agent_profiles():
    """Get all available agent profiles"""
    return AGENT_PROFILES

@app.get("/conversation-categories")
async def get_conversation_categories():
    """Get all available conversation categories"""
    return CONVERSATION_CATEGORIES

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)