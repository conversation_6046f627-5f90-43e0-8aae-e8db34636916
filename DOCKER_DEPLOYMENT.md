# 🐳 Docker Deployment Guide

This guide explains how to deploy the 3 AI applications using Docker containers with ports in the 3000-3005 range.

## 📋 Applications & Ports

| Service | Port | Description |
|---------|------|-------------|
| Medical QA | 3001 | Medical question answering service |
| Content Brief Generator | 3002 | Content brief generation service |
| Medical Report Analysis | 3003 | Medical report analysis service |

## 🚀 Quick Start

### 1. Prerequisites
- Docker and Docker Compose installed
- API keys for various services (see environment setup below)

### 2. Environment Setup

Copy the example environment files and fill in your API keys:

```bash
# Medical QA Service
cp backend/medical-qa/.env.example backend/medical-qa/.env
nano backend/medical-qa/.env

# Content Brief Generator
cp backend/content-brief/.env.example backend/content-brief/.env
nano backend/content-brief/.env

# Medical Report Analysis
cp backend/medical-report/.env.example backend/medical-report/.env
nano backend/medical-report/.env
```

### 3. Build and Run

```bash
# Build all services
docker-compose build

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Check service status
docker-compose ps
```

## 🔧 Individual Service Management

### Build Individual Services
```bash
# Medical QA
docker-compose build medical-qa

# Content Brief Generator
docker-compose build content-brief

# Medical Report Analysis
docker-compose build medical-report
```

### Start/Stop Individual Services
```bash
# Start specific service
docker-compose up -d medical-qa

# Stop specific service
docker-compose stop content-brief

# Restart specific service
docker-compose restart medical-report
```

## 🔍 Health Checks & Testing

### Service Health Checks
```bash
# Check all services
curl http://localhost:3001/health  # Medical QA
curl http://localhost:3002/health  # Content Brief
curl http://localhost:3003/health  # Medical Report

# Or use docker-compose
docker-compose ps
```

### API Testing
```bash
# Medical QA - Test endpoint
curl -X POST http://localhost:3001/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "What is diabetes?"}'

# Content Brief - Test endpoint
curl -X POST http://localhost:3002/generate \
  -H "Content-Type: application/json" \
  -d '{"topic": "AI in healthcare"}'

# Medical Report - Test endpoint
curl -X POST http://localhost:3003/analyze \
  -H "Content-Type: application/json" \
  -d '{"report_text": "Patient shows normal vital signs"}'
```

## 📝 Environment Variables

### Medical QA Service (Port 3001)
Required environment variables in `backend/medical-qa/.env`:
- `LITELLM_API_KEY`
- `LITELLM_BASE_URL`
- `AZURE_ENDPOINT`
- `AZURE_KEY`
- `OPENAI_API_KEY`
- `QDRANT_URL`
- `QDRANT_API_KEY`

### Content Brief Generator (Port 3002)
Required environment variables in `backend/content-brief/.env`:
- `LITELLM_API_KEY`
- `LITELLM_BASE_URL`
- `HUGGINGFACE_HUB_TOKEN`

### Medical Report Analysis (Port 3003)
Required environment variables in `backend/medical-report/.env`:
- `LITELLM_API_KEY`
- `LITELLM_BASE_URL`
- `AZURE_ENDPOINT`
- `AZURE_KEY`
- `OPENAI_API_KEY`
- `QDRANT_URL`
- `QDRANT_API_KEY`

## 🛠️ Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check if ports are in use
   lsof -i :3001
   lsof -i :3002
   lsof -i :3003
   ```

2. **Environment variables not loaded**
   ```bash
   # Check if .env files exist
   ls -la backend/*/\.env
   
   # Restart services after env changes
   docker-compose restart
   ```

3. **Build failures**
   ```bash
   # Clean build
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```

4. **Service not responding**
   ```bash
   # Check logs
   docker-compose logs medical-qa
   docker-compose logs content-brief
   docker-compose logs medical-report
   
   # Check container status
   docker-compose ps
   ```

### Logs and Debugging
```bash
# View all logs
docker-compose logs

# Follow logs for specific service
docker-compose logs -f medical-qa

# Execute commands in running container
docker-compose exec medical-qa bash
```

## 🔄 Updates and Maintenance

### Updating Services
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose build
docker-compose up -d
```

### Cleanup
```bash
# Stop and remove containers
docker-compose down

# Remove images
docker-compose down --rmi all

# Remove volumes
docker-compose down --volumes
```

## 📊 Monitoring

### Resource Usage
```bash
# Check resource usage
docker stats

# Check specific service
docker stats medical-qa-service
```

### Service Status
```bash
# Quick status check
docker-compose ps

# Detailed service info
docker-compose top
```

## 🔐 Security Notes

- Environment files contain sensitive API keys
- Ensure `.env` files are not committed to version control
- Use proper firewall rules for production deployment
- Consider using Docker secrets for production environments

## 🚀 Production Deployment

For production deployment:
1. Use proper secrets management
2. Set up reverse proxy (nginx/traefik)
3. Configure SSL certificates
4. Set up monitoring and logging
5. Use production-grade databases
6. Implement backup strategies
