#!/bin/bash

# Environment Variables Setup Script
# Run this on the server after deployment to set up your API keys

echo "🔑 Setting up environment variables..."

# Function to update environment file
update_env_file() {
    local service_path=$1
    local service_name=$2
    
    echo ""
    echo "📝 Setting up environment for $service_name..."
    echo "Environment file: $service_path/.env"
    
    # Check if .env file exists
    if [ ! -f "$service_path/.env" ]; then
        echo "❌ Environment file not found at $service_path/.env"
        return 1
    fi
    
    echo "✅ Environment file found. Please edit manually with your API keys:"
    echo "   nano $service_path/.env"
    echo ""
    echo "Required variables for $service_name:"
    
    if [ "$service_name" = "content-brief" ]; then
        echo "   - LITELLM_API_KEY"
        echo "   - LITELLM_BASE_URL"
        echo "   - HUGGINGFACE_HUB_TOKEN"
    else
        echo "   - LITELLM_API_KEY"
        echo "   - LITELLM_BASE_URL"
        echo "   - AZURE_ENDPOINT"
        echo "   - AZURE_KEY"
        echo "   - OPENAI_API_KEY"
        echo "   - AZURE_DEPLOYMENT"
        echo "   - AZURE_OPENAI_ENDPOINT"
        echo "   - QDRANT_URL"
        echo "   - QDRANT_API_KEY"
    fi
}

# Update environment files for all services
update_env_file "/root/ai-planet-apps/backend/medical-qa" "medical-qa"
update_env_file "/root/ai-planet-apps/backend/content-brief" "content-brief"
update_env_file "/root/ai-planet-apps/backend/medical-report" "medical-report"

echo ""
echo "🔧 After updating all .env files, restart services with:"
echo "   pm2 restart all"
echo ""
echo "📊 To monitor services:"
echo "   pm2 list"
echo "   pm2 logs"
echo "   pm2 monit"
echo ""
echo "🌐 Test your services:"
echo "   curl http://37.27.255.120:8001/health"
echo "   curl http://37.27.255.120:8002/health"
echo "   curl http://37.27.255.120:8003/health"
