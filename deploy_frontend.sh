#!/bin/bash

# Frontend Deployment Script
# Deploys React frontend with Vite

set -e

echo "🎨 Deploying frontend..."

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
npm install

# Update API endpoints for production
echo "🔧 Updating API endpoints for production..."
mkdir -p src/config
cat > src/config/api.ts << EOF
// Production API Configuration
export const API_CONFIG = {
  MEDICAL_QA_URL: 'http://*************:8001',
  CONTENT_BRIEF_URL: 'http://*************:8002',
  MEDICAL_REPORT_URL: 'http://*************:8003'
};
EOF

# Update frontend components to use production URLs
echo "📝 Updating frontend API calls..."

# Update Medical QA component
sed -i 's|http://localhost:8001|http://*************:8001|g' src/pages/use-cases/MedicalQA.tsx

# Update Content Brief component  
sed -i 's|http://localhost:8002|http://*************:8002|g' src/pages/use-cases/ContentBriefGenerator.tsx

# Update Medical Report component (it currently uses 8002, change to 8003)
sed -i 's|http://localhost:8002|http://*************:8003|g' src/pages/use-cases/MedicalReportAnalysis.tsx

# Build frontend for production
echo "🏗️ Building frontend for production..."
npm run build

# Create PM2 configuration for frontend
cat > frontend.ecosystem.config.cjs << EOF
module.exports = {
  apps: [{
    name: 'ai-apps-frontend',
    script: 'npx',
    args: 'vite preview --host 0.0.0.0 --port 3000',
    cwd: '/root/ai-planet-apps',
    env: {
      NODE_ENV: 'production'
    },
    log_file: '/var/log/ai-apps/frontend.log',
    error_file: '/var/log/ai-apps/frontend-error.log',
    out_file: '/var/log/ai-apps/frontend-out.log',
    time: true,
    autorestart: true,
    max_restarts: 5,
    min_uptime: '10s'
  }]
};
EOF

# Start frontend with PM2
echo "🚀 Starting frontend with PM2..."
pm2 start frontend.ecosystem.config.cjs

# Save PM2 configuration
pm2 save

echo "✅ Frontend deployed successfully!"
echo "🌐 Frontend accessible at: http://*************:3000"

# Show all running services
echo ""
echo "📋 All services status:"
pm2 list
