#!/bin/bash

# Simple Docker Services Test
# Tests each service individually without complex cleanup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=========================================="
echo "Simple Docker Services Test"
echo "Testing: medical-qa, content-brief, medical-report"
echo "=========================================="

# Test medical-qa service
echo ""
print_status "Testing medical-qa service..."

print_status "Building medical-qa image..."
if docker build -t simple-test-medical-qa ./backend/medical-qa; then
    print_success "medical-qa image built successfully"
    
    print_status "Starting medical-qa container..."
    container_id=$(docker run -d --name simple-medical-qa -p 3001:3001 simple-test-medical-qa)
    print_success "Container started: ${container_id:0:12}"
    
    print_status "Waiting for service to be ready..."
    sleep 15
    
    print_status "Testing health endpoint..."
    if curl -s http://localhost:3001/health | grep -q "healthy"; then
        print_success "medical-qa health check passed!"
        curl -s http://localhost:3001/health | jq . 2>/dev/null || curl -s http://localhost:3001/health
    else
        print_error "medical-qa health check failed"
        docker logs simple-medical-qa --tail 10
    fi
    
    print_status "Testing docs endpoint..."
    if curl -s http://localhost:3001/docs | grep -q "FastAPI"; then
        print_success "medical-qa docs endpoint accessible"
    else
        print_warning "medical-qa docs endpoint may not be working"
    fi
    
    print_status "Cleaning up medical-qa..."
    docker stop simple-medical-qa >/dev/null 2>&1 || true
    docker rm simple-medical-qa >/dev/null 2>&1 || true
    
else
    print_error "Failed to build medical-qa image"
fi

# Test content-brief service
echo ""
print_status "Testing content-brief service..."

print_status "Building content-brief image..."
if docker build -t simple-test-content-brief ./backend/content-brief; then
    print_success "content-brief image built successfully"
    
    print_status "Starting content-brief container..."
    container_id=$(docker run -d --name simple-content-brief -p 3002:3002 simple-test-content-brief)
    print_success "Container started: ${container_id:0:12}"
    
    print_status "Waiting for service to be ready..."
    sleep 15
    
    print_status "Testing health endpoint..."
    if curl -s http://localhost:3002/health | grep -q "healthy"; then
        print_success "content-brief health check passed!"
        curl -s http://localhost:3002/health | jq . 2>/dev/null || curl -s http://localhost:3002/health
    else
        print_error "content-brief health check failed"
        docker logs simple-content-brief --tail 10
    fi
    
    print_status "Testing docs endpoint..."
    if curl -s http://localhost:3002/docs | grep -q "FastAPI"; then
        print_success "content-brief docs endpoint accessible"
    else
        print_warning "content-brief docs endpoint may not be working"
    fi
    
    print_status "Cleaning up content-brief..."
    docker stop simple-content-brief >/dev/null 2>&1 || true
    docker rm simple-content-brief >/dev/null 2>&1 || true
    
else
    print_error "Failed to build content-brief image"
fi

# Test medical-report service
echo ""
print_status "Testing medical-report service..."

print_status "Building medical-report image..."
if docker build -t simple-test-medical-report ./backend/medical-report; then
    print_success "medical-report image built successfully"
    
    print_status "Starting medical-report container..."
    container_id=$(docker run -d --name simple-medical-report -p 3003:3003 simple-test-medical-report)
    print_success "Container started: ${container_id:0:12}"
    
    print_status "Waiting for service to be ready..."
    sleep 15
    
    print_status "Testing health endpoint..."
    if curl -s http://localhost:3003/health | grep -q "healthy"; then
        print_success "medical-report health check passed!"
        curl -s http://localhost:3003/health | jq . 2>/dev/null || curl -s http://localhost:3003/health
    else
        print_error "medical-report health check failed"
        docker logs simple-medical-report --tail 10
    fi
    
    print_status "Testing docs endpoint..."
    if curl -s http://localhost:3003/docs | grep -q "FastAPI"; then
        print_success "medical-report docs endpoint accessible"
    else
        print_warning "medical-report docs endpoint may not be working"
    fi
    
    print_status "Cleaning up medical-report..."
    docker stop simple-medical-report >/dev/null 2>&1 || true
    docker rm simple-medical-report >/dev/null 2>&1 || true
    
else
    print_error "Failed to build medical-report image"
fi

echo ""
echo "=========================================="
echo "Simple Test Summary"
echo "=========================================="
print_success "Individual service testing completed!"
echo ""
echo "Next steps:"
echo "1. Test docker-compose: docker compose up --build"
echo "2. Test all services running together"
echo "3. Deploy to production environment"
