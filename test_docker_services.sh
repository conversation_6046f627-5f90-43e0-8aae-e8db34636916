#!/bin/bash

# Docker Services Testing Script
# Tests all 3 backend services: medical-qa, content-brief, medical-report

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    print_status "Checking Docker installation and status..."

    # Check if docker is available in PATH or use full path
    DOCKER_CMD="docker"
    if ! command -v docker &> /dev/null; then
        if [ -f "/Applications/Docker.app/Contents/Resources/bin/docker" ]; then
            DOCKER_CMD="/Applications/Docker.app/Contents/Resources/bin/docker"
            print_status "Using Docker from: $DOCKER_CMD"
        else
            print_error "Docker is not installed. Please install Docker Desktop first."
            echo "Download from: https://www.docker.com/products/docker-desktop/"
            exit 1
        fi
    fi

    if ! $DOCKER_CMD info &> /dev/null; then
        print_error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi

    print_success "Docker is installed and running"
    $DOCKER_CMD --version
}

# Function to clean up existing containers and images
cleanup() {
    print_status "Cleaning up existing containers and images..."
    
    # Stop and remove containers if they exist
    for service in medical-qa-service content-brief-service medical-report-service; do
        if $DOCKER_CMD ps -a --format "table {{.Names}}" | grep -q "^${service}$"; then
            print_status "Stopping and removing container: $service"
            $DOCKER_CMD stop $service 2>/dev/null || true
            $DOCKER_CMD rm $service 2>/dev/null || true
        fi
    done

    # Remove images if they exist
    for image in ai-planet-explore-medical-qa ai-planet-explore-content-brief ai-planet-explore-medical-report; do
        if $DOCKER_CMD images --format "table {{.Repository}}" | grep -q "^${image}$"; then
            print_status "Removing image: $image"
            $DOCKER_CMD rmi $image 2>/dev/null || true
        fi
    done
    
    print_success "Cleanup completed"
}

# Function to test individual service build
test_service_build() {
    local service_name=$1
    local service_path=$2
    local port=$3
    
    print_status "Testing $service_name Docker build..."
    
    # Build the Docker image
    if $DOCKER_CMD build -t "ai-planet-explore-$service_name" "$service_path"; then
        print_success "$service_name Docker image built successfully"
    else
        print_error "Failed to build $service_name Docker image"
        return 1
    fi

    # Run the container
    print_status "Starting $service_name container..."
    if $DOCKER_CMD run -d --name "${service_name}-service" -p "$port:$port" "ai-planet-explore-$service_name"; then
        print_success "$service_name container started successfully"
    else
        print_error "Failed to start $service_name container"
        return 1
    fi
    
    # Wait for service to be ready
    print_status "Waiting for $service_name to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "http://localhost:$port/health" &> /dev/null; then
            print_success "$service_name is responding to health checks"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "$service_name failed to respond to health checks after $max_attempts attempts"
            $DOCKER_CMD logs "${service_name}-service"
            return 1
        fi

        print_status "Attempt $attempt/$max_attempts - waiting for $service_name..."
        sleep 2
        ((attempt++))
    done

    # Test the health endpoint
    print_status "Testing $service_name health endpoint..."
    local health_response=$(curl -s "http://localhost:$port/health")
    echo "Health response: $health_response"

    # Stop the container
    print_status "Stopping $service_name container..."
    $DOCKER_CMD stop "${service_name}-service"
    $DOCKER_CMD rm "${service_name}-service"
    
    print_success "$service_name test completed successfully"
}

# Function to test docker-compose
test_docker_compose() {
    print_status "Testing Docker Compose setup..."
    
    # Check if .env files exist
    for service in medical-qa content-brief medical-report; do
        if [ ! -f "backend/$service/.env" ]; then
            print_warning ".env file missing for $service - creating template"
            cat > "backend/$service/.env" << EOF
# Template .env file for $service
API_HOST=0.0.0.0
API_PORT=300${service: -1}
# Add your API keys and other environment variables here
EOF
        fi
    done
    
    # Build and start all services
    if $DOCKER_CMD compose up --build -d; then
        print_success "All services started with docker-compose"
    else
        print_error "Failed to start services with docker-compose"
        return 1
    fi
    
    # Wait for all services to be ready
    print_status "Waiting for all services to be ready..."
    sleep 10
    
    # Test each service health endpoint
    for port in 3001 3002 3003; do
        local service_name=""
        case $port in
            3001) service_name="medical-qa" ;;
            3002) service_name="content-brief" ;;
            3003) service_name="medical-report" ;;
        esac
        
        print_status "Testing $service_name health endpoint (port $port)..."
        local max_attempts=15
        local attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            if curl -f "http://localhost:$port/health" &> /dev/null; then
                print_success "$service_name is responding"
                break
            fi
            
            if [ $attempt -eq $max_attempts ]; then
                print_error "$service_name failed to respond"
                $DOCKER_CMD compose logs $service_name
            fi

            sleep 2
            ((attempt++))
        done
    done

    # Show running containers
    print_status "Running containers:"
    $DOCKER_CMD compose ps

    # Show logs for debugging
    print_status "Recent logs from all services:"
    $DOCKER_CMD compose logs --tail=10
    
    print_success "Docker Compose test completed"
}

# Main execution
main() {
    echo "=========================================="
    echo "Docker Services Testing Script"
    echo "Testing: medical-qa, content-brief, medical-report"
    echo "=========================================="
    
    # Check prerequisites
    check_docker
    
    # Cleanup previous runs
    cleanup
    
    # Test individual services
    print_status "Starting individual service tests..."
    
    test_service_build "medical-qa" "./backend/medical-qa" "3001"
    test_service_build "content-brief" "./backend/content-brief" "3002"
    test_service_build "medical-report" "./backend/medical-report" "3003"
    
    print_success "All individual service tests completed"
    
    # Test docker-compose
    print_status "Starting Docker Compose integration test..."
    test_docker_compose
    
    print_success "All tests completed successfully!"
    echo ""
    echo "=========================================="
    echo "Test Summary:"
    echo "✅ Docker installation verified"
    echo "✅ Individual service builds tested"
    echo "✅ Health checks verified"
    echo "✅ Docker Compose integration tested"
    echo "=========================================="
    echo ""
    echo "To stop all services: $DOCKER_CMD compose down"
    echo "To view logs: $DOCKER_CMD compose logs [service-name]"
    echo "To rebuild: $DOCKER_CMD compose up --build"
}

# Run main function
main "$@"
