#!/bin/bash

# Individual Service Docker Testing Script
# Usage: ./test_individual_service.sh <service-name>
# Example: ./test_individual_service.sh medical-qa

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Service configuration
declare -A SERVICE_PORTS=(
    ["medical-qa"]="3001"
    ["content-brief"]="3002"
    ["medical-report"]="3003"
)

# Check arguments
if [ $# -eq 0 ]; then
    print_error "Usage: $0 <service-name>"
    echo "Available services: medical-qa, content-brief, medical-report"
    exit 1
fi

SERVICE_NAME=$1
SERVICE_PATH="./backend/$SERVICE_NAME"
SERVICE_PORT=${SERVICE_PORTS[$SERVICE_NAME]}

if [ -z "$SERVICE_PORT" ]; then
    print_error "Unknown service: $SERVICE_NAME"
    echo "Available services: medical-qa, content-brief, medical-report"
    exit 1
fi

if [ ! -d "$SERVICE_PATH" ]; then
    print_error "Service directory not found: $SERVICE_PATH"
    exit 1
fi

print_status "Testing $SERVICE_NAME service..."
print_status "Service path: $SERVICE_PATH"
print_status "Service port: $SERVICE_PORT"

# Check if Docker is running
if ! docker info &> /dev/null; then
    print_error "Docker is not running. Please start Docker Desktop."
    exit 1
fi

# Cleanup existing container and image
CONTAINER_NAME="${SERVICE_NAME}-test"
IMAGE_NAME="test-${SERVICE_NAME}"

print_status "Cleaning up existing containers and images..."
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true
docker rmi $IMAGE_NAME 2>/dev/null || true

# Build the Docker image
print_status "Building Docker image for $SERVICE_NAME..."
if docker build -t $IMAGE_NAME $SERVICE_PATH; then
    print_success "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Create .env file if it doesn't exist
ENV_FILE="$SERVICE_PATH/.env"
if [ ! -f "$ENV_FILE" ]; then
    print_warning "Creating template .env file for $SERVICE_NAME"
    cat > "$ENV_FILE" << EOF
# Template .env file for $SERVICE_NAME
API_HOST=0.0.0.0
API_PORT=$SERVICE_PORT
# Add your API keys and other environment variables here
EOF
fi

# Run the container
print_status "Starting container..."
if docker run -d --name $CONTAINER_NAME -p "$SERVICE_PORT:$SERVICE_PORT" --env-file "$ENV_FILE" $IMAGE_NAME; then
    print_success "Container started successfully"
else
    print_error "Failed to start container"
    exit 1
fi

# Wait for service to be ready
print_status "Waiting for service to be ready..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f "http://localhost:$SERVICE_PORT/health" &> /dev/null; then
        print_success "Service is responding to health checks"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Service failed to respond after $max_attempts attempts"
        print_status "Container logs:"
        docker logs $CONTAINER_NAME
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME
        exit 1
    fi
    
    print_status "Attempt $attempt/$max_attempts - waiting..."
    sleep 2
    ((attempt++))
done

# Test the health endpoint
print_status "Testing health endpoint..."
health_response=$(curl -s "http://localhost:$SERVICE_PORT/health")
echo "Health response: $health_response"

# Test other endpoints if available
print_status "Testing API documentation endpoint..."
if curl -f "http://localhost:$SERVICE_PORT/docs" &> /dev/null; then
    print_success "API documentation is accessible at http://localhost:$SERVICE_PORT/docs"
else
    print_warning "API documentation endpoint not accessible"
fi

# Show container status
print_status "Container status:"
docker ps --filter "name=$CONTAINER_NAME"

print_status "Container logs (last 20 lines):"
docker logs --tail=20 $CONTAINER_NAME

print_success "$SERVICE_NAME test completed successfully!"
echo ""
echo "Service is running at: http://localhost:$SERVICE_PORT"
echo "API docs available at: http://localhost:$SERVICE_PORT/docs"
echo "Health check: http://localhost:$SERVICE_PORT/health"
echo ""
echo "To stop the container: docker stop $CONTAINER_NAME"
echo "To remove the container: docker rm $CONTAINER_NAME"
echo "To view logs: docker logs $CONTAINER_NAME"
