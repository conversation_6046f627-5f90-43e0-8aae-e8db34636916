#!/bin/bash

# Server Setup Script for Hetzner Ubuntu Server
# This script installs all necessary dependencies

set -e

echo "🔧 Setting up server environment..."

# Update system
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# Install Python 3.12 and pip (Ubuntu 24.04 default)
echo "🐍 Installing Python 3.12..."
apt install -y python3 python3-venv python3-pip python3-dev

# Install Node.js 18
echo "📦 Installing Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# Install system dependencies for Python packages
echo "🔧 Installing system dependencies..."
apt install -y \
    build-essential \
    libssl-dev \
    libffi-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    pkg-config \
    libhdf5-dev \
    libopenblas-dev \
    gfortran \
    ffmpeg \
    git \
    curl \
    wget \
    unzip

# Install PM2 for process management
echo "⚙️ Installing PM2..."
npm install -g pm2

# Create directories for logs
mkdir -p /var/log/ai-apps

echo "✅ Server setup completed!"
echo "📋 Installed:"
echo "   - Python $(python3 --version)"
echo "   - Node.js $(node --version)"
echo "   - npm $(npm --version)"
echo "   - PM2 $(pm2 --version)"
