#!/bin/bash

# Docker Build Testing Script
# Tests Docker image builds for all 3 services without requiring API keys

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Docker command (handle both PATH and full path)
DOCKER_CMD="docker"
if ! command -v docker &> /dev/null; then
    if [ -f "/Applications/Docker.app/Contents/Resources/bin/docker" ]; then
        DOCKER_CMD="/Applications/Docker.app/Contents/Resources/bin/docker"
    fi
fi

# Services configuration
SERVICES=("medical-qa" "content-brief" "medical-report")
PORTS=("3001" "3002" "3003")

echo "=========================================="
echo "Docker Build Testing Script"
echo "Testing Docker builds for all 3 services"
echo "=========================================="

# Check Docker
print_status "Checking Docker..."
if ! $DOCKER_CMD info &> /dev/null; then
    print_error "Docker is not running. Please start Docker Desktop."
    exit 1
fi
print_success "Docker is running"

# Test each service build
for i in "${!SERVICES[@]}"; do
    service="${SERVICES[$i]}"
    port="${PORTS[$i]}"
    service_path="./backend/$service"
    image_name="test-$service"
    
    echo ""
    print_status "Testing $service service build..."
    
    # Check if service directory exists
    if [ ! -d "$service_path" ]; then
        print_error "Service directory not found: $service_path"
        continue
    fi
    
    # Clean up any existing image
    print_status "Cleaning up existing image..."
    $DOCKER_CMD rmi $image_name 2>/dev/null || true
    
    # Build the Docker image
    print_status "Building Docker image for $service..."
    if $DOCKER_CMD build -t $image_name $service_path; then
        print_success "$service Docker image built successfully"
        
        # Get image info
        image_size=$($DOCKER_CMD images $image_name --format "table {{.Size}}" | tail -n 1)
        print_status "Image size: $image_size"
        
        # Test basic container creation (without starting)
        print_status "Testing container creation..."
        container_id=$($DOCKER_CMD create --name "test-$service" -p "$port:$port" $image_name)
        if [ $? -eq 0 ]; then
            print_success "Container created successfully: ${container_id:0:12}"
            
            # Clean up container
            $DOCKER_CMD rm "test-$service" >/dev/null
            print_status "Test container cleaned up"
        else
            print_error "Failed to create container"
        fi
        
        # Test image layers
        print_status "Checking image layers..."
        layer_count=$($DOCKER_CMD history $image_name --quiet | wc -l)
        print_status "Image has $layer_count layers"
        
    else
        print_error "Failed to build $service Docker image"
        continue
    fi
    
    print_success "$service build test completed"
done

echo ""
echo "=========================================="
echo "Build Test Summary"
echo "=========================================="

# Show all built images
print_status "Built images:"
$DOCKER_CMD images | grep "test-"

echo ""
print_success "Docker build testing completed!"
echo ""
echo "Next steps:"
echo "1. Add real API keys to .env files for functional testing"
echo "2. Test with docker-compose: docker compose up --build"
echo "3. Test individual services with real configuration"
echo ""
echo "To clean up test images:"
echo "docker rmi test-medical-qa test-content-brief test-medical-report"
