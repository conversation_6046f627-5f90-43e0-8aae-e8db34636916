#!/bin/bash

# Backend Deployment Script
# Deploys all 3 FastAPI backend services

set -e

echo "🔧 Deploying backend services..."

# Function to deploy a backend service
deploy_backend() {
    local service_name=$1
    local port=$2
    local service_dir="backend/$service_name"
    
    echo "📦 Deploying $service_name on port $port..."
    
    # Navigate to service directory
    cd $service_dir
    
    # Create virtual environment
    echo "🐍 Creating virtual environment for $service_name..."
    python3 -m venv venv
    source venv/bin/activate
    
    # Install dependencies
    echo "📋 Installing dependencies for $service_name..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # Create environment file template
    echo "📝 Creating environment file template for $service_name..."
    cat > .env << EOF
# $service_name Environment Variables
# IMPORTANT: Replace these with your actual values

# LiteLLM Configuration
LITELLM_API_KEY=your_litellm_api_key_here
LITELLM_BASE_URL=https://litellm.aiplanet.com
LITELLM_MODEL=gpt-4o-mini

# HuggingFace Configuration (for content-brief)
HUGGINGFACE_HUB_TOKEN=your_huggingface_token_here

# Azure Configuration (for medical services)
AZURE_ENDPOINT=your_azure_endpoint_here
AZURE_KEY=your_azure_key_here
OPENAI_API_KEY=your_openai_api_key_here
AZURE_DEPLOYMENT=your_deployment_name_here
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
OPENAI_API_VERSION=2023-05-15

# Qdrant Configuration (for medical services)
QDRANT_URL=your_qdrant_url_here
QDRANT_API_KEY=your_qdrant_api_key_here
EOF

    # Create PM2 ecosystem file
    cat > ecosystem.config.cjs << EOF
module.exports = {
  apps: [{
    name: '$service_name',
    script: '/root/ai-planet-apps/$service_dir/venv/bin/python',
    args: 'main.py',
    cwd: '/root/ai-planet-apps/$service_dir',
    env: {
      PORT: $port,
      HOST: '0.0.0.0'
    },
    log_file: '/var/log/ai-apps/$service_name.log',
    error_file: '/var/log/ai-apps/$service_name-error.log',
    out_file: '/var/log/ai-apps/$service_name-out.log',
    time: true,
    autorestart: true,
    max_restarts: 5,
    min_uptime: '10s'
  }]
};
EOF

    # Start service with PM2
    echo "🚀 Starting $service_name with PM2..."
    pm2 start ecosystem.config.cjs
    
    # Go back to project root
    cd /root/ai-planet-apps
    
    echo "✅ $service_name deployed successfully on port $port"
}

# Deploy each backend service
deploy_backend "medical-qa" 8001
deploy_backend "content-brief" 8002
deploy_backend "medical-report" 8003

# Save PM2 configuration
pm2 save
pm2 startup

echo "✅ All backend services deployed!"
echo "📋 Services running:"
pm2 list

echo ""
echo "⚠️  IMPORTANT: Update environment variables in each service:"
echo "   - /root/ai-planet-apps/backend/medical-qa/.env"
echo "   - /root/ai-planet-apps/backend/content-brief/.env"
echo "   - /root/ai-planet-apps/backend/medical-report/.env"
echo ""
echo "After updating .env files, restart services with:"
echo "   pm2 restart all"
