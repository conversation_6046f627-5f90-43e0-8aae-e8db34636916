#!/bin/bash

# Docker Setup Validation Script
# Validates Docker files and prerequisites before testing

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

SERVICES=("medical-qa" "content-brief" "medical-report")
PORTS=("3001" "3002" "3003")
ISSUES_FOUND=0

print_status "Validating Docker setup for all services..."

# Check Docker installation
check_docker_installation() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        echo "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/"
        ((ISSUES_FOUND++))
        return 1
    fi
    
    print_success "Docker is installed: $(docker --version)"
    
    if ! docker info &> /dev/null; then
        print_warning "Docker is installed but not running"
        echo "Please start Docker Desktop"
        ((ISSUES_FOUND++))
        return 1
    fi
    
    print_success "Docker is running"
}

# Validate individual service
validate_service() {
    local service=$1
    local port=$2
    local service_path="./backend/$service"
    
    print_status "Validating $service service..."
    
    # Check if service directory exists
    if [ ! -d "$service_path" ]; then
        print_error "Service directory not found: $service_path"
        ((ISSUES_FOUND++))
        return 1
    fi
    
    # Check Dockerfile
    if [ ! -f "$service_path/Dockerfile" ]; then
        print_error "Dockerfile not found in $service_path"
        ((ISSUES_FOUND++))
    else
        print_success "Dockerfile found for $service"
        
        # Check Dockerfile content
        if grep -q "FROM python:3.11-slim" "$service_path/Dockerfile"; then
            print_success "Base image is correct (python:3.11-slim)"
        else
            print_warning "Base image might not be optimal"
        fi
        
        if grep -q "EXPOSE $port" "$service_path/Dockerfile"; then
            print_success "Port $port is exposed in Dockerfile"
        else
            print_warning "Port $port not found in EXPOSE directive"
        fi
        
        if grep -q "HEALTHCHECK" "$service_path/Dockerfile"; then
            print_success "Health check is configured"
        else
            print_warning "No health check configured"
        fi
    fi
    
    # Check requirements.txt
    if [ ! -f "$service_path/requirements.txt" ]; then
        print_error "requirements.txt not found in $service_path"
        ((ISSUES_FOUND++))
    else
        print_success "requirements.txt found for $service"
        
        # Check for essential dependencies
        if grep -q "fastapi" "$service_path/requirements.txt"; then
            print_success "FastAPI dependency found"
        else
            print_error "FastAPI dependency missing"
            ((ISSUES_FOUND++))
        fi
        
        if grep -q "uvicorn" "$service_path/requirements.txt"; then
            print_success "Uvicorn dependency found"
        else
            print_error "Uvicorn dependency missing"
            ((ISSUES_FOUND++))
        fi
    fi
    
    # Check main.py
    if [ ! -f "$service_path/main.py" ]; then
        print_error "main.py not found in $service_path"
        ((ISSUES_FOUND++))
    else
        print_success "main.py found for $service"
        
        # Check for health endpoint
        if grep -q "/health" "$service_path/main.py"; then
            print_success "Health endpoint found in main.py"
        else
            print_warning "Health endpoint not found in main.py"
        fi
    fi
    
    # Check .env file
    if [ ! -f "$service_path/.env" ]; then
        print_warning ".env file not found for $service (will create template during testing)"
    else
        print_success ".env file found for $service"
    fi
    
    echo ""
}

# Validate docker-compose.yml
validate_docker_compose() {
    print_status "Validating docker-compose.yml..."
    
    if [ ! -f "./docker-compose.yml" ]; then
        print_error "docker-compose.yml not found in project root"
        ((ISSUES_FOUND++))
        return 1
    fi
    
    print_success "docker-compose.yml found"
    
    # Check if all services are defined
    for service in "${SERVICES[@]}"; do
        if grep -q "$service:" "./docker-compose.yml"; then
            print_success "$service service defined in docker-compose.yml"
        else
            print_error "$service service not found in docker-compose.yml"
            ((ISSUES_FOUND++))
        fi
    done
    
    # Check port mappings
    for i in "${!SERVICES[@]}"; do
        local service="${SERVICES[$i]}"
        local port="${PORTS[$i]}"
        if grep -q "\"$port:$port\"" "./docker-compose.yml"; then
            print_success "Port mapping $port:$port found for $service"
        else
            print_warning "Port mapping $port:$port not found for $service"
        fi
    done
    
    echo ""
}

# Check for port conflicts
check_port_conflicts() {
    print_status "Checking for port conflicts..."
    
    for port in "${PORTS[@]}"; do
        if lsof -i :$port &> /dev/null; then
            print_warning "Port $port is already in use"
            echo "Process using port $port:"
            lsof -i :$port
            ((ISSUES_FOUND++))
        else
            print_success "Port $port is available"
        fi
    done
    
    echo ""
}

# Main validation
main() {
    echo "=========================================="
    echo "Docker Setup Validation"
    echo "=========================================="
    
    check_docker_installation
    echo ""
    
    # Validate each service
    for i in "${!SERVICES[@]}"; do
        validate_service "${SERVICES[$i]}" "${PORTS[$i]}"
    done
    
    validate_docker_compose
    check_port_conflicts
    
    echo "=========================================="
    echo "Validation Summary"
    echo "=========================================="
    
    if [ $ISSUES_FOUND -eq 0 ]; then
        print_success "All validations passed! Ready for Docker testing."
        echo ""
        echo "Next steps:"
        echo "1. Run individual service tests: ./test_individual_service.sh <service-name>"
        echo "2. Run full test suite: ./test_docker_services.sh"
        echo "3. Use docker-compose: docker-compose up --build"
    else
        print_error "Found $ISSUES_FOUND issue(s) that need to be addressed"
        echo ""
        echo "Please fix the issues above before running Docker tests"
    fi
    
    echo "=========================================="
}

main "$@"
