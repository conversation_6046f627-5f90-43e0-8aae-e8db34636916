# 🚀 Hetzner Cloud Deployment Guide

This guide will help you deploy your 3 AI applications to Hetzner Cloud with Cloudflare for domain management.

## 📋 Prerequisites

1. **Hetzner Cloud Account** - Sign up at https://console.hetzner.cloud/
2. **Cloudflare Account** - Sign up at https://cloudflare.com/
3. **Domain Name** (optional but recommended)
4. **API Keys** for your AI services

## 🎯 Applications to Deploy

1. **Medical QA** - Port 8001
2. **Content Brief Generator** - Port 8002  
3. **Medical Report Analysis** - Port 8003
4. **Frontend** - Port 3000

## 🏗️ Step 1: Create Hetzner Server

### 1.1 Login to Hetz<PERSON> Console
- Go to https://console.hetzner.cloud/
- Create a new project or select existing one

### 1.2 Create Server
```
Server Type: CPX21 (2 vCPUs, 4GB RAM) or higher
Location: Choose closest to your users (e.g., Nuremberg, Helsinki, Ashburn)
Image: Ubuntu 22.04 LTS
SSH Key: Generate new or upload existing
Name: ai-planet-apps
```

### 1.3 Configure Firewall
Create firewall rules for:
- SSH (Port 22)
- HTTP (Port 80)
- HTTPS (Port 443)
- Custom ports: 3000, 8001, 8002, 8003

## 🔐 Step 2: SSH Key Setup

### 2.1 Generate SSH Key (if you don't have one)
```bash
ssh-keygen -t rsa -b 4096 -c "<EMAIL>"
```

### 2.2 Add SSH Key to Hetzner
- Copy your public key: `cat ~/.ssh/id_rsa.pub`
- Add it in Hetzner Console under SSH Keys

## 🌐 Step 3: Cloudflare Setup (Optional but Recommended)

### 3.1 Add Domain to Cloudflare
- Add your domain to Cloudflare
- Update nameservers at your domain registrar

### 3.2 Create DNS Records
```
Type: A
Name: @
Content: [Your Hetzner Server IP]
TTL: Auto

Type: A  
Name: api
Content: [Your Hetzner Server IP]
TTL: Auto

Type: CNAME
Name: www
Content: your-domain.com
TTL: Auto
```

## 🚀 Step 4: Server Setup

### 4.1 Connect to Server
```bash
ssh root@[YOUR_SERVER_IP]
```

### 4.2 Update System
```bash
apt update && apt upgrade -y
```

### 4.3 Install Dependencies
```bash
# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# Install Python 3.11
apt install -y python3.11 python3.11-venv python3.11-dev python3-pip

# Install PM2
npm install -g pm2

# Install Nginx
apt install -y nginx

# Install Certbot for SSL
apt install -y certbot python3-certbot-nginx

# Install Git
apt install -y git
```

## 📦 Step 5: Deploy Applications

### 5.1 Clone Repository
```bash
cd /opt
git clone [YOUR_REPOSITORY_URL] ai-planet-apps
cd ai-planet-apps
```

### 5.2 Setup Backend Services

#### Medical QA Service
```bash
cd /opt/ai-planet-apps/backend/medical-qa
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### Content Brief Generator
```bash
cd /opt/ai-planet-apps/backend/content-brief
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### Medical Report Analysis
```bash
cd /opt/ai-planet-apps/backend/medical-report
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 5.3 Setup Frontend
```bash
cd /opt/ai-planet-apps
npm install
npm run build
```

## ⚙️ Step 6: Configuration

### 6.1 Environment Variables
Create environment files for each service:

#### Medical QA (.env)
```bash
nano /opt/ai-planet-apps/backend/medical-qa/.env
```
```env
LITELLM_API_KEY=your_key
LITELLM_BASE_URL=your_url
AZURE_ENDPOINT=your_endpoint
AZURE_KEY=your_key
OPENAI_API_KEY=your_key
AZURE_DEPLOYMENT=your_deployment
AZURE_OPENAI_ENDPOINT=your_endpoint
QDRANT_URL=your_url
QDRANT_API_KEY=your_key
PORT=8001
HOST=0.0.0.0
```

#### Content Brief (.env)
```bash
nano /opt/ai-planet-apps/backend/content-brief/.env
```
```env
LITELLM_API_KEY=your_key
LITELLM_BASE_URL=your_url
HUGGINGFACE_HUB_TOKEN=your_token
PORT=8002
HOST=0.0.0.0
```

#### Medical Report (.env)
```bash
nano /opt/ai-planet-apps/backend/medical-report/.env
```
```env
LITELLM_API_KEY=your_key
LITELLM_BASE_URL=your_url
AZURE_ENDPOINT=your_endpoint
AZURE_KEY=your_key
OPENAI_API_KEY=your_key
AZURE_DEPLOYMENT=your_deployment
AZURE_OPENAI_ENDPOINT=your_endpoint
QDRANT_URL=your_url
QDRANT_API_KEY=your_key
PORT=8003
HOST=0.0.0.0
```

## 🔄 Step 7: PM2 Process Management

### 7.1 Create PM2 Ecosystem File
```bash
nano /opt/ai-planet-apps/ecosystem.config.js
```

### 7.2 Start Services
```bash
cd /opt/ai-planet-apps
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 🌐 Step 8: Nginx Configuration

### 8.1 Create Nginx Config
```bash
nano /etc/nginx/sites-available/ai-planet-apps
```

### 8.2 Enable Site
```bash
ln -s /etc/nginx/sites-available/ai-planet-apps /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx
```

## 🔒 Step 9: SSL Certificate (with Cloudflare)

### 9.1 Get SSL Certificate
```bash
certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 9.2 Auto-renewal
```bash
crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🎯 Step 10: Testing

### 10.1 Test Services
```bash
curl http://localhost:8001/health
curl http://localhost:8002/health
curl http://localhost:8003/health
curl http://localhost:3000
```

### 10.2 Test External Access
- Frontend: https://your-domain.com
- API: https://api.your-domain.com

## 📊 Monitoring & Management

### PM2 Commands
```bash
pm2 list                 # List all processes
pm2 restart all         # Restart all services
pm2 logs                # View logs
pm2 monit              # Monitor resources
```

### System Monitoring
```bash
htop                    # System resources
df -h                   # Disk usage
free -h                 # Memory usage
```

## 🔧 Troubleshooting

### Common Issues
1. **Port conflicts**: Check if ports are already in use
2. **Firewall**: Ensure Hetzner firewall allows required ports
3. **DNS**: Verify Cloudflare DNS propagation
4. **SSL**: Check certificate validity

### Logs
```bash
pm2 logs [service-name]
tail -f /var/log/nginx/error.log
journalctl -u nginx
```

## 🚀 Next Steps

1. Create the Hetzner server
2. Set up Cloudflare DNS
3. Run the deployment commands
4. Configure your API keys
5. Test all services
6. Share the URLs with your team

Your applications will be accessible at:
- Frontend: https://your-domain.com
- APIs: https://api.your-domain.com/medical-qa, /content-brief, /medical-report
