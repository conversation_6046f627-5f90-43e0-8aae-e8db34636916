# Docker Testing Guide

## Overview
This guide helps you test the Docker files for the 3 backend services:
- **medical-qa** (Port 3001)
- **content-brief** (Port 3002) 
- **medical-report** (Port 3003)

## Prerequisites
1. Docker installed via Homebrew: `brew install --cask docker`
2. Docker Desktop running
3. All testing scripts are executable (already done)

## Testing Scripts Available

### 1. Quick Validation
```bash
./validate_setup.sh
```
- Checks if all required files exist
- Creates template .env files if missing
- Validates basic setup

### 2. Individual Service Testing
```bash
./test_individual_service.sh <service-name>
```
Examples:
```bash
./test_individual_service.sh medical-qa
./test_individual_service.sh content-brief
./test_individual_service.sh medical-report
```

**What it does:**
- Builds Docker image for the specific service
- Runs container with proper port mapping
- Tests health endpoint
- Shows logs and status
- Cleans up after testing

### 3. Full Integration Testing
```bash
./test_docker_services.sh
```

**What it does:**
- Tests all 3 services individually
- Tests docker-compose integration
- Verifies health checks for all services
- Shows comprehensive test results

### 4. Manual Docker Compose Testing
```bash
# Build and start all services
docker-compose up --build

# Run in background
docker-compose up --build -d

# Stop all services
docker-compose down

# View logs
docker-compose logs
docker-compose logs medical-qa
```

## Service Endpoints

Once services are running, you can access:

### Medical QA Service (Port 3001)
- Health: http://localhost:3001/health
- API Docs: http://localhost:3001/docs
- OpenAPI: http://localhost:3001/openapi.json

### Content Brief Service (Port 3002)
- Health: http://localhost:3002/health
- API Docs: http://localhost:3002/docs
- OpenAPI: http://localhost:3002/openapi.json

### Medical Report Service (Port 3003)
- Health: http://localhost:3003/health
- API Docs: http://localhost:3003/docs
- OpenAPI: http://localhost:3003/openapi.json

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Check what's using the port
   lsof -i :3001
   
   # Kill process if needed
   kill -9 <PID>
   ```

2. **Docker not running**
   ```bash
   # Check Docker status
   docker info
   
   # Start Docker Desktop manually
   open /Applications/Docker.app
   ```

3. **Build failures**
   ```bash
   # Check Docker logs
   docker logs <container-name>
   
   # Rebuild without cache
   docker build --no-cache -t <image-name> <path>
   ```

4. **Environment variables missing**
   - Check .env files in each backend directory
   - Add required API keys and configurations

### Useful Docker Commands

```bash
# List running containers
docker ps

# List all containers
docker ps -a

# List images
docker images

# Remove container
docker rm <container-name>

# Remove image
docker rmi <image-name>

# View container logs
docker logs <container-name>

# Execute command in running container
docker exec -it <container-name> bash
```

## Expected Test Results

### Successful Individual Test
```
✅ Docker image built successfully
✅ Container started successfully  
✅ Service is responding to health checks
✅ API documentation is accessible
```

### Successful Integration Test
```
✅ All individual service tests completed
✅ Docker Compose integration tested
✅ All services responding to health checks
```

## Next Steps After Testing

1. **For Development**: Use individual service testing
2. **For Integration**: Use docker-compose
3. **For Production**: Services are ready for deployment to Hetzner

## Notes

- Template .env files are created automatically
- Add your actual API keys to .env files before production
- Health checks are configured with 30-second intervals
- All services use Python 3.11-slim base image
- Non-root user (appuser) is used for security
