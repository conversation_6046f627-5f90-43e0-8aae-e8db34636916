#!/bin/bash

# Medical QA Full Stack Application Startup Script

echo "🏥 Starting Medical QA Application..."

# Function to check if port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "Port $1 is already in use. Please stop the existing process or use a different port."
        return 1
    fi
    return 0
}

# Check if required ports are available
echo "Checking ports..."
if ! check_port 8001; then
    echo "❌ Backend port 8001 is in use"
    exit 1
fi

if ! check_port 8080; then
    echo "❌ Frontend port 8080 is in use"
    exit 1
fi

echo "✅ Ports are available"

# Start Backend
echo "🚀 Starting Medical QA Backend..."
cd backend/medical-qa

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Setting up virtual environment..."
    ./setup_env.sh
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found in backend/medical-qa/"
    echo "Please create .env file with your API keys and configurations"
    exit 1
fi

# Start backend in background
source venv/bin/activate && python main.py &
BACKEND_PID=$!
echo "✅ Backend started (PID: $BACKEND_PID)"

# Wait for backend to start
echo "Waiting for backend to initialize..."
sleep 5

# Test backend health
if curl -s http://localhost:8001/health > /dev/null; then
    echo "✅ Backend is healthy"
else
    echo "❌ Backend health check failed"
    kill $BACKEND_PID
    exit 1
fi

# Start Frontend
echo "🎨 Starting Frontend..."
cd ../../

# Install frontend dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi

# Start frontend in background
npm run dev &
FRONTEND_PID=$!
echo "✅ Frontend started (PID: $FRONTEND_PID)"

# Wait for frontend to start
echo "Waiting for frontend to initialize..."
sleep 3

echo ""
echo "🎉 Medical QA Application is ready!"
echo ""
echo "📱 Frontend: http://localhost:8080"
echo "🔧 Backend API: http://localhost:8001"
echo "📚 API Docs: http://localhost:8001/docs"
echo "🏥 Medical QA: http://localhost:8080/use-cases/medical-qa"
echo ""
echo "Press Ctrl+C to stop both services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Services stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
